import{aR as m,j as i}from"./index-B283E1a3.js";import"./pos-api-C7RsFAun.js";import"./vietqr-api-BbJFOv9v.js";import"./user-Cxn8z8PZ.js";import"./crm-api-CE_jLH-z.js";import"./header-BZ_7I_4c.js";import"./main-BlYSJOOd.js";import"./search-context-CZoJZmsi.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{C as p}from"./create-table-form-DJjfFSac.js";import"./separator-DLHnMAQ0.js";import"./command-C1ySvjo8.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";import"./createReactComponent-WabRa4kY.js";import"./scroll-area-BlxlVxpe.js";import"./index-Df0XEEuz.js";import"./select-BzVwefGp.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./IconChevronRight-jxL9ONfH.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";import"./popover-CCXriU_R.js";import"./use-areas-Cik1fKOj.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./images-api-CKiFhZ_l.js";import"./query-keys-3lmd-xp6.js";import"./use-sales-channels-BWGXhSRx.js";import"./use-tables-o2tqLcYq.js";import"./input-Bx4sCRS0.js";import"./checkbox-DtNgKdj2.js";import"./collapsible-lV085WFw.js";import"./use-items-in-store-data-BqQj7vLM.js";import"./use-item-types-ClqdZoOZ.js";import"./use-item-classes-TuVgnk5f.js";import"./use-units-5I1VqZ6k.js";import"./use-removed-items-D-t1fwGe.js";import"./items-in-store-api-BbbXWOGT.js";import"./xlsx-DkH2s96g.js";import"./copy-DB1-P1gG.js";import"./plus-CKQNSsha.js";import"./minus-Dj8sUgsf.js";const mo=function(){const{store_uid:o,area_uid:t,tableLayout:r}=m.useSearch();return i.jsx(p,{storeUid:o,areaId:t,fromTableLayout:r})};export{mo as component};
