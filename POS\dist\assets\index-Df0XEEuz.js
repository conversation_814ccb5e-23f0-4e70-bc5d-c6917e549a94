import{r as E,C as D,R as r,I as S,j as m,J as M}from"./index-B283E1a3.js";var j=E.createContext(void 0);function w(l){const i=E.useContext(j);return l||i||"ltr"}function L(l){const i=l+"CollectionProvider",[A,N]=D(i),[v,u]=A(i,{collectionRef:{current:null},itemMap:new Map}),C=c=>{const{scope:e,children:s}=c,t=r.useRef(null),o=r.useRef(new Map).current;return m.jsx(v,{scope:e,itemMap:o,collectionRef:t,children:s})};C.displayName=i;const f=l+"CollectionSlot",T=M(f),p=r.forwardRef((c,e)=>{const{scope:s,children:t}=c,o=u(f,s),n=S(e,o.collectionRef);return m.jsx(T,{ref:n,children:t})});p.displayName=f;const d=l+"CollectionItemSlot",x="data-radix-collection-item",O=M(d),R=r.forwardRef((c,e)=>{const{scope:s,children:t,...o}=c,n=r.useRef(null),I=S(e,n),a=u(d,s);return r.useEffect(()=>(a.itemMap.set(n,{ref:n,...o}),()=>void a.itemMap.delete(n))),m.jsx(O,{[x]:"",ref:I,children:t})});R.displayName=d;function _(c){const e=u(l+"CollectionConsumer",c);return r.useCallback(()=>{const t=e.collectionRef.current;if(!t)return[];const o=Array.from(t.querySelectorAll(`[${x}]`));return Array.from(e.itemMap.values()).sort((a,y)=>o.indexOf(a.ref.current)-o.indexOf(y.ref.current))},[e.collectionRef,e.itemMap])}return[{Provider:C,Slot:p,ItemSlot:R},_,N]}export{L as c,w as u};
