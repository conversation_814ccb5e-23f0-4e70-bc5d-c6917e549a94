import{r as o,Q as _e,A as Te,j as n,U as nt,C as rt,D as Ie,I as L,V as st,P as j,F as _,Y as $,H as at,s as Ne,Z as lt,_ as ct,J as it,K as dt,W as ut,X as pt,c as ee}from"./index-B283E1a3.js";import{u as ft,c as mt}from"./index-Df0XEEuz.js";import{h as ht,u as vt,R as gt,F as xt}from"./index-CqlrRQAb.js";import{u as St}from"./index-DY0KH0l4.js";import{c as Re}from"./createLucideIcon-D6RMy2u2.js";import{C as wt}from"./check-CjIon4B5.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Ee=Re("chevron-down",yt);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],It=Re("chevron-up",Ct);function Pe(t,[r,e]){return Math.min(e,Math.max(r,t))}var bt=[" ","Enter","ArrowUp","ArrowDown"],Tt=[" ","Enter"],se="Select",[ie,de,Pt]=mt(se),[te,fo]=rt(se,[Pt,_e]),ue=_e(),[_t,G]=te(se),[Nt,Rt]=te(se),je=t=>{const{__scopeSelect:r,children:e,open:c,defaultOpen:i,onOpenChange:p,value:a,defaultValue:l,onValueChange:s,dir:f,name:g,autoComplete:w,disabled:T,required:P,form:b}=t,d=ue(r),[v,y]=o.useState(null),[u,h]=o.useState(null),[M,A]=o.useState(!1),oe=ft(f),[N=!1,D]=Te({prop:c,defaultProp:i,onChange:p}),[U,q]=Te({prop:a,defaultProp:l,onChange:s}),V=o.useRef(null),B=v?b||!!v.closest("form"):!0,[z,H]=o.useState(new Set),F=Array.from(z).map(R=>R.props.value).join(";");return n.jsx(nt,{...d,children:n.jsxs(_t,{required:P,scope:r,trigger:v,onTriggerChange:y,valueNode:u,onValueNodeChange:h,valueNodeHasChildren:M,onValueNodeHasChildrenChange:A,contentId:Ie(),value:U,onValueChange:q,open:N,onOpenChange:D,dir:oe,triggerPointerDownPosRef:V,disabled:T,children:[n.jsx(ie.Provider,{scope:r,children:n.jsx(Nt,{scope:t.__scopeSelect,onNativeOptionAdd:o.useCallback(R=>{H(k=>new Set(k).add(R))},[]),onNativeOptionRemove:o.useCallback(R=>{H(k=>{const W=new Set(k);return W.delete(R),W})},[]),children:e})}),B?n.jsxs(et,{"aria-hidden":!0,required:P,tabIndex:-1,name:g,autoComplete:w,value:U,onChange:R=>q(R.target.value),disabled:T,form:b,children:[U===void 0?n.jsx("option",{value:""}):null,Array.from(z)]},F):null]})})};je.displayName=se;var Me="SelectTrigger",Ae=o.forwardRef((t,r)=>{const{__scopeSelect:e,disabled:c=!1,...i}=t,p=ue(e),a=G(Me,e),l=a.disabled||c,s=L(r,a.onTriggerChange),f=de(e),g=o.useRef("touch"),[w,T,P]=tt(d=>{const v=f().filter(h=>!h.disabled),y=v.find(h=>h.value===a.value),u=ot(v,d,y);u!==void 0&&a.onValueChange(u.value)}),b=d=>{l||(a.onOpenChange(!0),P()),d&&(a.triggerPointerDownPosRef.current={x:Math.round(d.pageX),y:Math.round(d.pageY)})};return n.jsx(st,{asChild:!0,...p,children:n.jsx(j.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Qe(a.value)?"":void 0,...i,ref:s,onClick:_(i.onClick,d=>{d.currentTarget.focus(),g.current!=="mouse"&&b(d)}),onPointerDown:_(i.onPointerDown,d=>{g.current=d.pointerType;const v=d.target;v.hasPointerCapture(d.pointerId)&&v.releasePointerCapture(d.pointerId),d.button===0&&d.ctrlKey===!1&&d.pointerType==="mouse"&&(b(d),d.preventDefault())}),onKeyDown:_(i.onKeyDown,d=>{const v=w.current!=="";!(d.ctrlKey||d.altKey||d.metaKey)&&d.key.length===1&&T(d.key),!(v&&d.key===" ")&&bt.includes(d.key)&&(b(),d.preventDefault())})})})});Ae.displayName=Me;var Oe="SelectValue",De=o.forwardRef((t,r)=>{const{__scopeSelect:e,className:c,style:i,children:p,placeholder:a="",...l}=t,s=G(Oe,e),{onValueNodeHasChildrenChange:f}=s,g=p!==void 0,w=L(r,s.onValueNodeChange);return $(()=>{f(g)},[f,g]),n.jsx(j.span,{...l,ref:w,style:{pointerEvents:"none"},children:Qe(s.value)?n.jsx(n.Fragment,{children:a}):p})});De.displayName=Oe;var Et="SelectIcon",ke=o.forwardRef((t,r)=>{const{__scopeSelect:e,children:c,...i}=t;return n.jsx(j.span,{"aria-hidden":!0,...i,ref:r,children:c||"▼"})});ke.displayName=Et;var jt="SelectPortal",Le=t=>n.jsx(at,{asChild:!0,...t});Le.displayName=jt;var J="SelectContent",Ve=o.forwardRef((t,r)=>{const e=G(J,t.__scopeSelect),[c,i]=o.useState();if($(()=>{i(new DocumentFragment)},[]),!e.open){const p=c;return p?Ne.createPortal(n.jsx(Be,{scope:t.__scopeSelect,children:n.jsx(ie.Slot,{scope:t.__scopeSelect,children:n.jsx("div",{children:t.children})})}),p):null}return n.jsx(He,{...t,ref:r})});Ve.displayName=J;var O=10,[Be,Y]=te(J),Mt="SelectContentImpl",At=it("SelectContent.RemoveScroll"),He=o.forwardRef((t,r)=>{const{__scopeSelect:e,position:c="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:p,onPointerDownOutside:a,side:l,sideOffset:s,align:f,alignOffset:g,arrowPadding:w,collisionBoundary:T,collisionPadding:P,sticky:b,hideWhenDetached:d,avoidCollisions:v,...y}=t,u=G(J,e),[h,M]=o.useState(null),[A,oe]=o.useState(null),N=L(r,m=>M(m)),[D,U]=o.useState(null),[q,V]=o.useState(null),B=de(e),[z,H]=o.useState(!1),F=o.useRef(!1);o.useEffect(()=>{if(h)return ht(h)},[h]),vt();const R=o.useCallback(m=>{const[I,...E]=B().map(S=>S.ref.current),[C]=E.slice(-1),x=document.activeElement;for(const S of m)if(S===x||(S==null||S.scrollIntoView({block:"nearest"}),S===I&&A&&(A.scrollTop=0),S===C&&A&&(A.scrollTop=A.scrollHeight),S==null||S.focus(),document.activeElement!==x))return},[B,A]),k=o.useCallback(()=>R([D,h]),[R,D,h]);o.useEffect(()=>{z&&k()},[z,k]);const{onOpenChange:W,triggerPointerDownPosRef:K}=u;o.useEffect(()=>{if(h){let m={x:0,y:0};const I=C=>{var x,S;m={x:Math.abs(Math.round(C.pageX)-(((x=K.current)==null?void 0:x.x)??0)),y:Math.abs(Math.round(C.pageY)-(((S=K.current)==null?void 0:S.y)??0))}},E=C=>{m.x<=10&&m.y<=10?C.preventDefault():h.contains(C.target)||W(!1),document.removeEventListener("pointermove",I),K.current=null};return K.current!==null&&(document.addEventListener("pointermove",I),document.addEventListener("pointerup",E,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",I),document.removeEventListener("pointerup",E,{capture:!0})}}},[h,W,K]),o.useEffect(()=>{const m=()=>W(!1);return window.addEventListener("blur",m),window.addEventListener("resize",m),()=>{window.removeEventListener("blur",m),window.removeEventListener("resize",m)}},[W]);const[pe,ae]=tt(m=>{const I=B().filter(x=>!x.disabled),E=I.find(x=>x.ref.current===document.activeElement),C=ot(I,m,E);C&&setTimeout(()=>C.ref.current.focus())}),fe=o.useCallback((m,I,E)=>{const C=!F.current&&!E;(u.value!==void 0&&u.value===I||C)&&(U(m),C&&(F.current=!0))},[u.value]),me=o.useCallback(()=>h==null?void 0:h.focus(),[h]),Q=o.useCallback((m,I,E)=>{const C=!F.current&&!E;(u.value!==void 0&&u.value===I||C)&&V(m)},[u.value]),le=c==="popper"?xe:Fe,ne=le===xe?{side:l,sideOffset:s,align:f,alignOffset:g,arrowPadding:w,collisionBoundary:T,collisionPadding:P,sticky:b,hideWhenDetached:d,avoidCollisions:v}:{};return n.jsx(Be,{scope:e,content:h,viewport:A,onViewportChange:oe,itemRefCallback:fe,selectedItem:D,onItemLeave:me,itemTextRefCallback:Q,focusSelectedItem:k,selectedItemText:q,position:c,isPositioned:z,searchRef:pe,children:n.jsx(gt,{as:At,allowPinchZoom:!0,children:n.jsx(xt,{asChild:!0,trapped:u.open,onMountAutoFocus:m=>{m.preventDefault()},onUnmountAutoFocus:_(i,m=>{var I;(I=u.trigger)==null||I.focus({preventScroll:!0}),m.preventDefault()}),children:n.jsx(dt,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:p,onPointerDownOutside:a,onFocusOutside:m=>m.preventDefault(),onDismiss:()=>u.onOpenChange(!1),children:n.jsx(le,{role:"listbox",id:u.contentId,"data-state":u.open?"open":"closed",dir:u.dir,onContextMenu:m=>m.preventDefault(),...y,...ne,onPlaced:()=>H(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:_(y.onKeyDown,m=>{const I=m.ctrlKey||m.altKey||m.metaKey;if(m.key==="Tab"&&m.preventDefault(),!I&&m.key.length===1&&ae(m.key),["ArrowUp","ArrowDown","Home","End"].includes(m.key)){let C=B().filter(x=>!x.disabled).map(x=>x.ref.current);if(["ArrowUp","End"].includes(m.key)&&(C=C.slice().reverse()),["ArrowUp","ArrowDown"].includes(m.key)){const x=m.target,S=C.indexOf(x);C=C.slice(S+1)}setTimeout(()=>R(C)),m.preventDefault()}})})})})})})});He.displayName=Mt;var Ot="SelectItemAlignedPosition",Fe=o.forwardRef((t,r)=>{const{__scopeSelect:e,onPlaced:c,...i}=t,p=G(J,e),a=Y(J,e),[l,s]=o.useState(null),[f,g]=o.useState(null),w=L(r,N=>g(N)),T=de(e),P=o.useRef(!1),b=o.useRef(!0),{viewport:d,selectedItem:v,selectedItemText:y,focusSelectedItem:u}=a,h=o.useCallback(()=>{if(p.trigger&&p.valueNode&&l&&f&&d&&v&&y){const N=p.trigger.getBoundingClientRect(),D=f.getBoundingClientRect(),U=p.valueNode.getBoundingClientRect(),q=y.getBoundingClientRect();if(p.dir!=="rtl"){const x=q.left-D.left,S=U.left-x,X=N.left-S,Z=N.width+X,he=Math.max(Z,D.width),ve=window.innerWidth-O,ge=Pe(S,[O,Math.max(O,ve-he)]);l.style.minWidth=Z+"px",l.style.left=ge+"px"}else{const x=D.right-q.right,S=window.innerWidth-U.right-x,X=window.innerWidth-N.right-S,Z=N.width+X,he=Math.max(Z,D.width),ve=window.innerWidth-O,ge=Pe(S,[O,Math.max(O,ve-he)]);l.style.minWidth=Z+"px",l.style.right=ge+"px"}const V=T(),B=window.innerHeight-O*2,z=d.scrollHeight,H=window.getComputedStyle(f),F=parseInt(H.borderTopWidth,10),R=parseInt(H.paddingTop,10),k=parseInt(H.borderBottomWidth,10),W=parseInt(H.paddingBottom,10),K=F+R+z+W+k,pe=Math.min(v.offsetHeight*5,K),ae=window.getComputedStyle(d),fe=parseInt(ae.paddingTop,10),me=parseInt(ae.paddingBottom,10),Q=N.top+N.height/2-O,le=B-Q,ne=v.offsetHeight/2,m=v.offsetTop+ne,I=F+R+m,E=K-I;if(I<=Q){const x=V.length>0&&v===V[V.length-1].ref.current;l.style.bottom="0px";const S=f.clientHeight-d.offsetTop-d.offsetHeight,X=Math.max(le,ne+(x?me:0)+S+k),Z=I+X;l.style.height=Z+"px"}else{const x=V.length>0&&v===V[0].ref.current;l.style.top="0px";const X=Math.max(Q,F+d.offsetTop+(x?fe:0)+ne)+E;l.style.height=X+"px",d.scrollTop=I-Q+d.offsetTop}l.style.margin=`${O}px 0`,l.style.minHeight=pe+"px",l.style.maxHeight=B+"px",c==null||c(),requestAnimationFrame(()=>P.current=!0)}},[T,p.trigger,p.valueNode,l,f,d,v,y,p.dir,c]);$(()=>h(),[h]);const[M,A]=o.useState();$(()=>{f&&A(window.getComputedStyle(f).zIndex)},[f]);const oe=o.useCallback(N=>{N&&b.current===!0&&(h(),u==null||u(),b.current=!1)},[h,u]);return n.jsx(kt,{scope:e,contentWrapper:l,shouldExpandOnScrollRef:P,onScrollButtonChange:oe,children:n.jsx("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:n.jsx(j.div,{...i,ref:w,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Fe.displayName=Ot;var Dt="SelectPopperPosition",xe=o.forwardRef((t,r)=>{const{__scopeSelect:e,align:c="start",collisionPadding:i=O,...p}=t,a=ue(e);return n.jsx(ut,{...a,...p,ref:r,align:c,collisionPadding:i,style:{boxSizing:"border-box",...p.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});xe.displayName=Dt;var[kt,be]=te(J,{}),Se="SelectViewport",We=o.forwardRef((t,r)=>{const{__scopeSelect:e,nonce:c,...i}=t,p=Y(Se,e),a=be(Se,e),l=L(r,p.onViewportChange),s=o.useRef(0);return n.jsxs(n.Fragment,{children:[n.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:c}),n.jsx(ie.Slot,{scope:e,children:n.jsx(j.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:_(i.onScroll,f=>{const g=f.currentTarget,{contentWrapper:w,shouldExpandOnScrollRef:T}=a;if(T!=null&&T.current&&w){const P=Math.abs(s.current-g.scrollTop);if(P>0){const b=window.innerHeight-O*2,d=parseFloat(w.style.minHeight),v=parseFloat(w.style.height),y=Math.max(d,v);if(y<b){const u=y+P,h=Math.min(b,u),M=u-h;w.style.height=h+"px",w.style.bottom==="0px"&&(g.scrollTop=M>0?M:0,w.style.justifyContent="flex-end")}}}s.current=g.scrollTop})})})]})});We.displayName=Se;var Ue="SelectGroup",[Lt,Vt]=te(Ue),Bt=o.forwardRef((t,r)=>{const{__scopeSelect:e,...c}=t,i=Ie();return n.jsx(Lt,{scope:e,id:i,children:n.jsx(j.div,{role:"group","aria-labelledby":i,...c,ref:r})})});Bt.displayName=Ue;var ze="SelectLabel",Ht=o.forwardRef((t,r)=>{const{__scopeSelect:e,...c}=t,i=Vt(ze,e);return n.jsx(j.div,{id:i.id,...c,ref:r})});Ht.displayName=ze;var ce="SelectItem",[Ft,Ke]=te(ce),$e=o.forwardRef((t,r)=>{const{__scopeSelect:e,value:c,disabled:i=!1,textValue:p,...a}=t,l=G(ce,e),s=Y(ce,e),f=l.value===c,[g,w]=o.useState(p??""),[T,P]=o.useState(!1),b=L(r,u=>{var h;return(h=s.itemRefCallback)==null?void 0:h.call(s,u,c,i)}),d=Ie(),v=o.useRef("touch"),y=()=>{i||(l.onValueChange(c),l.onOpenChange(!1))};if(c==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return n.jsx(Ft,{scope:e,value:c,disabled:i,textId:d,isSelected:f,onItemTextChange:o.useCallback(u=>{w(h=>h||((u==null?void 0:u.textContent)??"").trim())},[]),children:n.jsx(ie.ItemSlot,{scope:e,value:c,disabled:i,textValue:g,children:n.jsx(j.div,{role:"option","aria-labelledby":d,"data-highlighted":T?"":void 0,"aria-selected":f&&T,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...a,ref:b,onFocus:_(a.onFocus,()=>P(!0)),onBlur:_(a.onBlur,()=>P(!1)),onClick:_(a.onClick,()=>{v.current!=="mouse"&&y()}),onPointerUp:_(a.onPointerUp,()=>{v.current==="mouse"&&y()}),onPointerDown:_(a.onPointerDown,u=>{v.current=u.pointerType}),onPointerMove:_(a.onPointerMove,u=>{var h;v.current=u.pointerType,i?(h=s.onItemLeave)==null||h.call(s):v.current==="mouse"&&u.currentTarget.focus({preventScroll:!0})}),onPointerLeave:_(a.onPointerLeave,u=>{var h;u.currentTarget===document.activeElement&&((h=s.onItemLeave)==null||h.call(s))}),onKeyDown:_(a.onKeyDown,u=>{var M;((M=s.searchRef)==null?void 0:M.current)!==""&&u.key===" "||(Tt.includes(u.key)&&y(),u.key===" "&&u.preventDefault())})})})})});$e.displayName=ce;var re="SelectItemText",Ge=o.forwardRef((t,r)=>{const{__scopeSelect:e,className:c,style:i,...p}=t,a=G(re,e),l=Y(re,e),s=Ke(re,e),f=Rt(re,e),[g,w]=o.useState(null),T=L(r,y=>w(y),s.onItemTextChange,y=>{var u;return(u=l.itemTextRefCallback)==null?void 0:u.call(l,y,s.value,s.disabled)}),P=g==null?void 0:g.textContent,b=o.useMemo(()=>n.jsx("option",{value:s.value,disabled:s.disabled,children:P},s.value),[s.disabled,s.value,P]),{onNativeOptionAdd:d,onNativeOptionRemove:v}=f;return $(()=>(d(b),()=>v(b)),[d,v,b]),n.jsxs(n.Fragment,{children:[n.jsx(j.span,{id:s.textId,...p,ref:T}),s.isSelected&&a.valueNode&&!a.valueNodeHasChildren?Ne.createPortal(p.children,a.valueNode):null]})});Ge.displayName=re;var Ye="SelectItemIndicator",qe=o.forwardRef((t,r)=>{const{__scopeSelect:e,...c}=t;return Ke(Ye,e).isSelected?n.jsx(j.span,{"aria-hidden":!0,...c,ref:r}):null});qe.displayName=Ye;var we="SelectScrollUpButton",Xe=o.forwardRef((t,r)=>{const e=Y(we,t.__scopeSelect),c=be(we,t.__scopeSelect),[i,p]=o.useState(!1),a=L(r,c.onScrollButtonChange);return $(()=>{if(e.viewport&&e.isPositioned){let l=function(){const f=s.scrollTop>0;p(f)};const s=e.viewport;return l(),s.addEventListener("scroll",l),()=>s.removeEventListener("scroll",l)}},[e.viewport,e.isPositioned]),i?n.jsx(Je,{...t,ref:a,onAutoScroll:()=>{const{viewport:l,selectedItem:s}=e;l&&s&&(l.scrollTop=l.scrollTop-s.offsetHeight)}}):null});Xe.displayName=we;var ye="SelectScrollDownButton",Ze=o.forwardRef((t,r)=>{const e=Y(ye,t.__scopeSelect),c=be(ye,t.__scopeSelect),[i,p]=o.useState(!1),a=L(r,c.onScrollButtonChange);return $(()=>{if(e.viewport&&e.isPositioned){let l=function(){const f=s.scrollHeight-s.clientHeight,g=Math.ceil(s.scrollTop)<f;p(g)};const s=e.viewport;return l(),s.addEventListener("scroll",l),()=>s.removeEventListener("scroll",l)}},[e.viewport,e.isPositioned]),i?n.jsx(Je,{...t,ref:a,onAutoScroll:()=>{const{viewport:l,selectedItem:s}=e;l&&s&&(l.scrollTop=l.scrollTop+s.offsetHeight)}}):null});Ze.displayName=ye;var Je=o.forwardRef((t,r)=>{const{__scopeSelect:e,onAutoScroll:c,...i}=t,p=Y("SelectScrollButton",e),a=o.useRef(null),l=de(e),s=o.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return o.useEffect(()=>()=>s(),[s]),$(()=>{var g;const f=l().find(w=>w.ref.current===document.activeElement);(g=f==null?void 0:f.ref.current)==null||g.scrollIntoView({block:"nearest"})},[l]),n.jsx(j.div,{"aria-hidden":!0,...i,ref:r,style:{flexShrink:0,...i.style},onPointerDown:_(i.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(c,50))}),onPointerMove:_(i.onPointerMove,()=>{var f;(f=p.onItemLeave)==null||f.call(p),a.current===null&&(a.current=window.setInterval(c,50))}),onPointerLeave:_(i.onPointerLeave,()=>{s()})})}),Wt="SelectSeparator",Ut=o.forwardRef((t,r)=>{const{__scopeSelect:e,...c}=t;return n.jsx(j.div,{"aria-hidden":!0,...c,ref:r})});Ut.displayName=Wt;var Ce="SelectArrow",zt=o.forwardRef((t,r)=>{const{__scopeSelect:e,...c}=t,i=ue(e),p=G(Ce,e),a=Y(Ce,e);return p.open&&a.position==="popper"?n.jsx(pt,{...i,...c,ref:r}):null});zt.displayName=Ce;function Qe(t){return t===""||t===void 0}var et=o.forwardRef((t,r)=>{const{value:e,...c}=t,i=o.useRef(null),p=L(r,i),a=St(e);return o.useEffect(()=>{const l=i.current,s=window.HTMLSelectElement.prototype,g=Object.getOwnPropertyDescriptor(s,"value").set;if(a!==e&&g){const w=new Event("change",{bubbles:!0});g.call(l,e),l.dispatchEvent(w)}},[a,e]),n.jsx(lt,{asChild:!0,children:n.jsx("select",{...c,ref:p,defaultValue:e})})});et.displayName="BubbleSelect";function tt(t){const r=ct(t),e=o.useRef(""),c=o.useRef(0),i=o.useCallback(a=>{const l=e.current+a;r(l),function s(f){e.current=f,window.clearTimeout(c.current),f!==""&&(c.current=window.setTimeout(()=>s(""),1e3))}(l)},[r]),p=o.useCallback(()=>{e.current="",window.clearTimeout(c.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(c.current),[]),[e,i,p]}function ot(t,r,e){const i=r.length>1&&Array.from(r).every(f=>f===r[0])?r[0]:r,p=e?t.indexOf(e):-1;let a=Kt(t,Math.max(p,0));i.length===1&&(a=a.filter(f=>f!==e));const s=a.find(f=>f.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==e?s:void 0}function Kt(t,r){return t.map((e,c)=>t[(r+c)%t.length])}var $t=je,Gt=Ae,Yt=De,qt=ke,Xt=Le,Zt=Ve,Jt=We,Qt=$e,eo=Ge,to=qe,oo=Xe,no=Ze;function mo({...t}){return n.jsx($t,{"data-slot":"select",...t})}function ho({...t}){return n.jsx(Yt,{"data-slot":"select-value",...t})}function vo({className:t,size:r="default",children:e,...c}){return n.jsxs(Gt,{"data-slot":"select-trigger","data-size":r,className:ee("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...c,children:[e,n.jsx(qt,{asChild:!0,children:n.jsx(Ee,{className:"size-4 opacity-50"})})]})}function go({className:t,children:r,position:e="popper",...c}){return n.jsx(Xt,{children:n.jsxs(Zt,{"data-slot":"select-content",className:ee("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",e==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:e,...c,children:[n.jsx(ro,{}),n.jsx(Jt,{className:ee("p-1",e==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),n.jsx(so,{})]})})}function xo({className:t,children:r,...e}){return n.jsxs(Qt,{"data-slot":"select-item",className:ee("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...e,children:[n.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:n.jsx(to,{children:n.jsx(wt,{className:"size-4"})})}),n.jsx(eo,{children:r})]})}function ro({className:t,...r}){return n.jsx(oo,{"data-slot":"select-scroll-up-button",className:ee("flex cursor-default items-center justify-center py-1",t),...r,children:n.jsx(It,{className:"size-4"})})}function so({className:t,...r}){return n.jsx(no,{"data-slot":"select-scroll-down-button",className:ee("flex cursor-default items-center justify-center py-1",t),...r,children:n.jsx(Ee,{className:"size-4"})})}export{Ee as C,mo as S,vo as a,ho as b,go as c,xo as d,Pe as e,It as f};
