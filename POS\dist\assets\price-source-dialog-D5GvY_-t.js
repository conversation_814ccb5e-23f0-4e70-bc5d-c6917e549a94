import{a3 as O,u as te,l as ne,a4 as C,r as f,j as t,R as se,B as E,z as e}from"./index-B283E1a3.js";import{u as Be}from"./use-dialog-state-DpGk-Lpu.js";import{C as W}from"./checkbox-DtNgKdj2.js";import{I as ce}from"./input-Bx4sCRS0.js";import{P as $e}from"./modal-COeiv6He.js";import"./pos-api-C7RsFAun.js";import"./date-range-picker-B7aoXHt3.js";import{L as Ue,u as le,F as ue,a as $,b as U,c as q,d as G,e as M}from"./form-dNL1hWKC.js";import{C as je}from"./chevron-right-CVT48KKP.js";import{C as Ie}from"./select-BzVwefGp.js";import{s as de}from"./zod-BOoGjb2n.js";import"./user-Cxn8z8PZ.js";import{D as me,a as pe,b as he,c as ge,e as xe,f as be}from"./dialog-BTZKnesd.js";import{C as De}from"./combobox-Db0SwfY4.js";import"./vietqr-api-BbJFOv9v.js";import"./crm-api-CE_jLH-z.js";import{u as H}from"./useMutation-Bf5OzDko.js";import{Q as R}from"./query-keys-3lmd-xp6.js";import{i as F}from"./items-in-store-api-BbbXWOGT.js";import{f as z,g as Xe,h as Ge,i as Ye,s as We,j as Je,k as Ze,l as Te,a as et,u as tt,m as nt,C as we,n as st,o as rt}from"./core.esm-BN1vgNRx.js";import{D as Se}from"./date-picker-zVQMDD0k.js";import{C as Ce}from"./circle-help-Q6UK66s-.js";import{X as it}from"./calendar-AxR9kFpj.js";const en=()=>{const n=O(),s=H({mutationFn:i=>F.createItemInStore(i),onSuccess:()=>{F.clearCache(),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),C.success("Tạo món thành công!")},onError:i=>{C.error(i.message||"Có lỗi xảy ra khi tạo món")}});return{createItemAsync:s.mutateAsync,isPending:s.isPending}},ot=()=>{const n=O(),s=H({mutationFn:i=>F.updateItemInStore(i),onSuccess:()=>{F.clearCache(),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_DETAIL]}),C.success("Cập nhật món thành công!")},onError:i=>{C.error(i.message||"Có lỗi xảy ra khi cập nhật món")}});return{updateItemAsync:s.mutateAsync,isPending:s.isPending}},tn=()=>{const n=O(),{company:s}=te(r=>r.auth),{selectedBrand:i}=ne(),c=H({mutationFn:r=>{const a={company_uid:(s==null?void 0:s.id)||"",brand_uid:(i==null?void 0:i.id)||"",id:r};return F.deleteItemInStore(a)},onSuccess:()=>{n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),C.success("Xóa món thành công!")},onError:r=>{C.error(r.message||"Có lỗi xảy ra khi xóa món")}});return{deleteItemAsync:c.mutateAsync,isPending:c.isPending}},nn=()=>{const n=O(),{company:s}=te(r=>r.auth),{selectedBrand:i}=ne(),c=H({mutationFn:r=>{const a={company_uid:(s==null?void 0:s.id)||"",brand_uid:(i==null?void 0:i.id)||"",list_item_uid:r};return F.deleteMultipleItemsInStore(a)},onSuccess:()=>{n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),C.success("Xóa món ăn thành công")},onError:r=>{C.error((r==null?void 0:r.message)||"Có lỗi xảy ra khi xóa món ăn")}});return{deleteMultipleItemsAsync:c.mutateAsync,isPending:c.isPending}},sn=()=>{const n=O(),{company:s}=te(r=>r.auth),{selectedBrand:i}=ne(),c=H({mutationFn:r=>{const a={id:r.id,active:r.active,company_uid:(s==null?void 0:s.id)||"",brand_uid:(i==null?void 0:i.id)||""};return F.updateItemStatus(a)},onSuccess:()=>{F.clearCache(),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_DETAIL]}),C.success("Cập nhật trạng thái thành công!")},onError:r=>{C.error(r.message||"Có lỗi xảy ra khi cập nhật trạng thái")}});return{updateStatusAsync:c.mutateAsync,isPending:c.isPending}},rn=()=>{const n=O();return H({mutationFn:s=>F.bulkUpdateItemsInStore(s),onSuccess:()=>{n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_FOR_TABLE]}),C.success("Cập nhật thứ tự món ăn thành công")},onError:s=>{C.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi cập nhật thứ tự món ăn")}})},on=()=>{const n=O(),{mutate:s,isPending:i}=H({mutationFn:async c=>F.bulkCreateItemsInStore(c),onSuccess:()=>{n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST],refetchType:"none"}),setTimeout(()=>{n.refetchQueries({queryKey:[R.ITEMS_IN_STORE_LIST]})},100),C.success("Tạo món ăn thành công!")},onError:c=>{C.error((c==null?void 0:c.message)||"Có lỗi xảy ra khi tạo món ăn")}});return{bulkCreateItemsInStore:s,isBulkCreating:i}},an=()=>{const n=O();return H({mutationFn:s=>F.cloneMenu(s),onSuccess:()=>{n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_LIST]}),n.invalidateQueries({queryKey:[R.ITEMS_IN_STORE_FOR_TABLE]}),C.success("Sao chép thực đơn thành công")},onError:s=>{C.error((s==null?void 0:s.message)||"Có lỗi xảy ra khi sao chép thực đơn")}})},ke=se.createContext(null);function cn({children:n,selectedStoreUid:s,setSelectedStoreUid:i}){const[c,r]=Be(null),[a,d]=f.useState(null);return t.jsx(ke,{value:{open:c,setOpen:r,currentRow:a,setCurrentRow:d,selectedStoreUid:s,setSelectedStoreUid:i},children:n})}const ln=()=>{const n=se.useContext(ke);if(!n)throw new Error("useItemsInStore has to be used within <ItemsInStoreContext>");return n};function un({itemsBuffet:n,open:s,onOpenChange:i,onItemsChange:c,items:r,hide:a=!0,enable:d=!0,onEnableChange:m}){const[g,p]=f.useState(""),[o,l]=f.useState([]),[x,_]=f.useState(!1),[T,v]=f.useState(!1),[N,A]=f.useState(!1);f.useEffect(()=>{s&&(l(Array.isArray(n)?n:[]),A(d))},[n,s]);const j=f.useMemo(()=>g?r.filter(b=>{var S;return(S=b.item_name)==null?void 0:S.toLowerCase().includes(g.toLowerCase())}):r,[r,g]),w=f.useMemo(()=>j.length?j.filter(b=>o.includes(b.item_id||"")):[],[j,o]),I=f.useMemo(()=>j.length?j.filter(b=>!o.includes(b.item_id||"")):[],[j,o]),u=b=>{l(S=>S.includes(b)?S.filter(L=>L!==b):[...S,b])},h=w.length,y=j.length,k=y>0&&h===y,P=h>0&&h<y,K=()=>{if(k){const b=j.map(S=>S.item_id);l(S=>S.filter(L=>!b.includes(L)))}else{const b=j.map(S=>S.item_id);l(S=>{const L=[...S];return b.forEach(Y=>{L.includes(Y||"")||L.push(Y||"")}),L})}},V=()=>{c(o),i(!1)},re=()=>{l([]),i(!1)};return t.jsx($e,{title:"Chọn danh sách món không đi kèm vé buffet",centerTitle:!0,open:s,onOpenChange:i,onCancel:re,onConfirm:V,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:t.jsxs("div",{className:"space-y-4",children:[!a&&t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(W,{id:"enable-buffet",checked:N,onCheckedChange:b=>{const S=!!b;A(S),m==null||m(S)}}),t.jsx(Ue,{htmlFor:"enable-buffet",className:"cursor-pointer text-blue-600",children:"Cấu hình món ăn là vé buffet"})]}),(a||N)&&t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"flex items-center gap-2",children:t.jsx(ce,{placeholder:"Tìm kiếm",value:g,onChange:b=>p(b.target.value),className:"w-full"})}),!a&&t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(E,{type:"button",variant:"outline",className:"flex-1 justify-start",children:"Danh sách món không đi kèm vé buffet"}),t.jsx(E,{type:"button",variant:"link",className:"flex-1 justify-start text-blue-600",onClick:()=>{},children:"Danh sách vé buffet được upsize"})]}),t.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx(W,{id:"select-all",checked:k,...P&&{"data-indeterminate":"true"},onCheckedChange:K,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),t.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",h]}),t.jsx(E,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>_(!x),children:x?t.jsx(je,{className:"h-3 w-3"}):t.jsx(Ie,{className:"h-3 w-3"})})]}),!x&&w.length>0&&t.jsx("div",{className:"mt-3 space-y-2",children:w.map(b=>t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx(W,{id:`selected-${b.item_id}`,checked:o.includes(b.item_id),onCheckedChange:()=>u(b.item_id)}),t.jsx("label",{htmlFor:`selected-${b.item_id}`,className:"flex-1 cursor-pointer text-sm",children:b.item_name})]},b.item_id))})]}),t.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",I.length]}),t.jsx(E,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>v(!T),children:T?t.jsx(je,{className:"h-3 w-3"}):t.jsx(Ie,{className:"h-3 w-3"})})]}),!T&&t.jsx("div",{className:"mt-3 max-h-60 space-y-2 overflow-y-auto",children:I.map(b=>t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx(W,{id:b.item_id,checked:o.includes(b.item_id),onCheckedChange:()=>u(b.item_id)}),t.jsx("label",{htmlFor:b.item_id,className:"flex-1 cursor-pointer text-sm",children:b.item_name})]},b.item_id))})]})]})]})})}const at=e.object({customization_uid:e.string().nullable()});function dn({item:n,customizations:s,open:i,onOpenChange:c}){const{updateItemAsync:r}=ot(),{company:a}=te(p=>p.auth),{selectedBrand:d}=ne(),m=le({resolver:de(at),defaultValues:{customization_uid:"none"}});f.useEffect(()=>{if(i)try{m.reset({customization_uid:(n==null?void 0:n.customization_uid)??null})}catch{C.error("Lỗi khi load customization data")}},[i,m,n]);const g=async p=>{try{if(!(n!=null&&n.id)||!(a!=null&&a.id)||!(d!=null&&d.id))throw new Error("Required data is missing");const o=p.customization_uid==="none"?null:p.customization_uid;await r({...n,customization_uid:o}),c(!1)}catch{C.error("Lỗi khi cập nhật customization")}};return n?t.jsx(me,{open:i,onOpenChange:p=>{c(p),m.reset()},children:t.jsxs(pe,{className:"top-[20%] w-full max-w-4xl translate-y-[-50%]",children:[t.jsx(he,{children:t.jsx(ge,{className:"text-center",children:"Cấu hình customization"})}),t.jsx(ue,{...m,children:t.jsxs("form",{onSubmit:m.handleSubmit(g),className:"space-y-4",children:[t.jsx($,{control:m.control,name:"customization_uid",render:({field:p})=>t.jsxs(U,{children:[t.jsx(q,{children:"Customization áp dụng cho món"}),t.jsx(G,{children:t.jsx(De,{value:p.value??"",onValueChange:o=>p.onChange(o===""?null:o),options:s.map(o=>({value:o.id,label:o.name})),placeholder:"Chọn customization...",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không có dữ liệu",className:"w-full"})}),t.jsx(M,{})]})}),t.jsxs(xe,{children:[t.jsx(be,{asChild:!0,children:t.jsx(E,{variant:"outline",type:"button",children:"Hủy"})}),t.jsx(E,{type:"submit",disabled:m.formState.isSubmitting,children:m.formState.isSubmitting?"Đang lưu...":"Lưu"})]})]})})]})}):null}const mn=()=>{const n="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";return`ITEM-${Array.from({length:4},()=>n[Math.floor(Math.random()*n.length)]).join("")}`},pn=(n,s)=>{var i;return(i=s==null?void 0:s.find(c=>c.sourceId===n||c.id===n))==null?void 0:i.sourceName},ct=n=>{const s={1:"Thứ 2",2:"Thứ 3",4:"Thứ 4",8:"Thứ 5",16:"Thứ 6",32:"Thứ 7",64:"Chủ nhật"};return n.map(i=>s[i]).join(", ")},lt=n=>n.map(s=>`${s}h`).join(", "),ut=n=>{const s=[];return n&1&&s.push(1),n&2&&s.push(2),n&4&&s.push(4),n&8&&s.push(8),n&16&&s.push(16),n&32&&s.push(32),n&64&&s.push(64),s},dt=n=>{const s=[];for(let i=0;i<24;i++)n&1<<i&&s.push(i);return s},Ne=n=>n.toLocaleDateString("vi-VN"),ae=n=>n.map(s=>{const i=s.selectedDays.reduce((r,a)=>r|a,0),c=s.selectedHours.reduce((r,a)=>r|1<<a,0);return{price:s.price,from_date:s.startDate.getTime(),to_date:new Date(s.endDate.getTime()+24*60*60*1e3-1).getTime(),time_sale_date_week:i,time_sale_hour_day:c}}),hn=n=>n==="Hoạt động"||n===1||n==="1"||n===!0?1:0,gn=n=>n==="Có"||n===1||n==="1"||n===!0?1:0;function Ee(n,s,i){const c=n.slice();return c.splice(i<0?c.length+i:i,0,c.splice(s,1)[0]),c}function mt(n,s){return n.reduce((i,c,r)=>{const a=s.get(c);return a&&(i[r]=a),i},Array(n.length))}function J(n){return n!==null&&n>=0}function pt(n,s){if(n===s)return!0;if(n.length!==s.length)return!1;for(let i=0;i<n.length;i++)if(n[i]!==s[i])return!1;return!0}function ht(n){return typeof n=="boolean"?{draggable:n,droppable:n}:n}const Re=n=>{let{rects:s,activeIndex:i,overIndex:c,index:r}=n;const a=Ee(s,c,i),d=s[r],m=a[r];return!m||!d?null:{x:m.left-d.left,y:m.top-d.top,scaleX:m.width/d.width,scaleY:m.height/d.height}},Z={scaleX:1,scaleY:1},xn=n=>{var s;let{activeIndex:i,activeNodeRect:c,index:r,rects:a,overIndex:d}=n;const m=(s=a[i])!=null?s:c;if(!m)return null;if(r===i){const p=a[d];return p?{x:0,y:i<d?p.top+p.height-(m.top+m.height):p.top-m.top,...Z}:null}const g=gt(a,r,i);return r>i&&r<=d?{x:0,y:-m.height-g,...Z}:r<i&&r>=d?{x:0,y:m.height+g,...Z}:{x:0,y:0,...Z}};function gt(n,s,i){const c=n[s],r=n[s-1],a=n[s+1];return c?i<s?r?c.top-(r.top+r.height):a?a.top-(c.top+c.height):0:a?a.top-(c.top+c.height):r?c.top-(r.top+r.height):0:0}const Ae="Sortable",Fe=se.createContext({activeIndex:-1,containerId:Ae,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:Re,disabled:{draggable:!1,droppable:!1}});function bn(n){let{children:s,id:i,items:c,strategy:r=Re,disabled:a=!1}=n;const{active:d,dragOverlay:m,droppableRects:g,over:p,measureDroppableContainers:o}=Je(),l=Ze(Ae,i),x=m.rect!==null,_=f.useMemo(()=>c.map(h=>typeof h=="object"&&"id"in h?h.id:h),[c]),T=d!=null,v=d?_.indexOf(d.id):-1,N=p?_.indexOf(p.id):-1,A=f.useRef(_),j=!pt(_,A.current),w=N!==-1&&v===-1||j,I=ht(a);Te(()=>{j&&T&&o(_)},[j,_,T,o]),f.useEffect(()=>{A.current=_},[_]);const u=f.useMemo(()=>({activeIndex:v,containerId:l,disabled:I,disableTransforms:w,items:_,overIndex:N,useDragOverlay:x,sortedRects:mt(_,g),strategy:r}),[v,l,I.draggable,I.droppable,w,_,N,g,x,r]);return se.createElement(Fe.Provider,{value:u},s)}const xt=n=>{let{id:s,items:i,activeIndex:c,overIndex:r}=n;return Ee(i,c,r).indexOf(s)},bt=n=>{let{containerId:s,isSorting:i,wasDragging:c,index:r,items:a,newIndex:d,previousItems:m,previousContainerId:g,transition:p}=n;return!p||!c||m!==a&&r===d?!1:i?!0:d!==r&&s===g},ft={duration:200,easing:"ease"},Le="transform",_t=we.Transition.toString({property:Le,duration:0,easing:"linear"}),yt={roleDescription:"sortable"};function vt(n){let{disabled:s,index:i,node:c,rect:r}=n;const[a,d]=f.useState(null),m=f.useRef(i);return Te(()=>{if(!s&&i!==m.current&&c.current){const g=r.current;if(g){const p=rt(c.current,{ignoreTransform:!0}),o={x:g.left-p.left,y:g.top-p.top,scaleX:g.width/p.width,scaleY:g.height/p.height};(o.x||o.y)&&d(o)}}i!==m.current&&(m.current=i)},[s,i,c,r]),f.useEffect(()=>{a&&d(null)},[a]),a}function fn(n){let{animateLayoutChanges:s=bt,attributes:i,disabled:c,data:r,getNewIndex:a=xt,id:d,strategy:m,resizeObserverConfig:g,transition:p=ft}=n;const{items:o,containerId:l,activeIndex:x,disabled:_,disableTransforms:T,sortedRects:v,overIndex:N,useDragOverlay:A,strategy:j}=f.useContext(Fe),w=jt(c,_),I=o.indexOf(d),u=f.useMemo(()=>({sortable:{containerId:l,index:I,items:o},...r}),[l,r,I,o]),h=f.useMemo(()=>o.slice(o.indexOf(d)),[o,d]),{rect:y,node:k,isOver:P,setNodeRef:K}=et({id:d,data:u,disabled:w.droppable,resizeObserverConfig:{updateMeasurementsFor:h,...g}}),{active:V,activatorEvent:re,activeNodeRect:b,attributes:S,setNodeRef:L,listeners:Y,isDragging:ie,over:Me,setActivatorNodeRef:ze,transform:Oe}=tt({id:d,data:u,attributes:{...yt,...i},disabled:w.draggable}),He=nt(K,L),Q=!!V,fe=Q&&!T&&J(x)&&J(N),_e=!A&&ie,ye=_e&&fe?Oe:null,Pe=fe?ye??(m??j)({rects:v,activeNodeRect:b,activeIndex:x,overIndex:N,index:I}):null,X=J(x)&&J(N)?a({id:d,items:o,activeIndex:x,overIndex:N}):I,B=V==null?void 0:V.id,D=f.useRef({activeId:B,items:o,newIndex:X,containerId:l}),Ke=o!==D.current.items,ve=s({active:V,containerId:l,isDragging:ie,isSorting:Q,id:d,index:I,items:o,newIndex:D.current.newIndex,previousItems:D.current.items,previousContainerId:D.current.containerId,transition:p,wasDragging:D.current.activeId!=null}),oe=vt({disabled:!ve,index:I,node:k,rect:y});return f.useEffect(()=>{Q&&D.current.newIndex!==X&&(D.current.newIndex=X),l!==D.current.containerId&&(D.current.containerId=l),o!==D.current.items&&(D.current.items=o)},[Q,X,l,o]),f.useEffect(()=>{if(B===D.current.activeId)return;if(B!=null&&D.current.activeId==null){D.current.activeId=B;return}const Qe=setTimeout(()=>{D.current.activeId=B},50);return()=>clearTimeout(Qe)},[B]),{active:V,activeIndex:x,attributes:S,data:u,rect:y,index:I,newIndex:X,items:o,isOver:P,isSorting:Q,isDragging:ie,listeners:Y,node:k,overIndex:N,over:Me,setNodeRef:He,setActivatorNodeRef:ze,setDroppableNodeRef:K,setDraggableNodeRef:L,transform:oe??Pe,transition:Ve()};function Ve(){if(oe||Ke&&D.current.newIndex===I)return _t;if(!(_e&&!st(re)||!p)&&(Q||ve))return we.Transition.toString({...p,property:Le})}}function jt(n,s){var i,c;return typeof n=="boolean"?{draggable:n,droppable:!1}:{draggable:(i=n==null?void 0:n.draggable)!=null?i:s.draggable,droppable:(c=n==null?void 0:n.droppable)!=null?c:s.droppable}}function ee(n){if(!n)return!1;const s=n.data.current;return!!(s&&"sortable"in s&&typeof s.sortable=="object"&&"containerId"in s.sortable&&"items"in s.sortable&&"index"in s.sortable)}const It=[z.Down,z.Right,z.Up,z.Left],_n=(n,s)=>{let{context:{active:i,collisionRect:c,droppableRects:r,droppableContainers:a,over:d,scrollableAncestors:m}}=s;if(It.includes(n.code)){if(n.preventDefault(),!i||!c)return;const g=[];a.getEnabled().forEach(l=>{if(!l||l!=null&&l.disabled)return;const x=r.get(l.id);if(x)switch(n.code){case z.Down:c.top<x.top&&g.push(l);break;case z.Up:c.top>x.top&&g.push(l);break;case z.Left:c.left>x.left&&g.push(l);break;case z.Right:c.left<x.left&&g.push(l);break}});const p=Xe({collisionRect:c,droppableRects:r,droppableContainers:g});let o=Ge(p,"id");if(o===(d==null?void 0:d.id)&&p.length>1&&(o=p[1].id),o!=null){const l=a.get(i.id),x=a.get(o),_=x?r.get(x.id):null,T=x==null?void 0:x.node.current;if(T&&_&&l&&x){const N=Ye(T).some((h,y)=>m[y]!==h),A=qe(l,x),j=St(l,x),w=N||!A?{x:0,y:0}:{x:j?c.width-_.width:0,y:j?c.height-_.height:0},I={x:_.left,y:_.top};return w.x&&w.y?I:We(I,w)}}}};function qe(n,s){return!ee(n)||!ee(s)?!1:n.data.current.sortable.containerId===s.data.current.sortable.containerId}function St(n,s){return!ee(n)||!ee(s)||!qe(n,s)?!1:n.data.current.sortable.index<s.data.current.sortable.index}e.object({id:e.string().optional(),item_id:e.string().optional(),item_name:e.string().optional(),description:e.string().optional(),ots_price:e.coerce.number().optional(),ots_tax:e.coerce.number().optional(),ta_price:e.coerce.number().optional(),ta_tax:e.coerce.number().optional(),time_sale_hour_day:e.coerce.number().optional(),time_sale_date_week:e.coerce.number().optional(),allow_take_away:e.coerce.number().optional(),is_eat_with:e.coerce.number().optional(),image_path:e.string().optional(),image_path_thumb:e.string().optional(),item_color:e.string().optional(),list_order:e.coerce.number().optional(),is_service:e.coerce.number().optional(),is_material:e.coerce.number().optional(),active:e.coerce.number().optional(),user_id:e.string().optional(),is_foreign:e.coerce.number().optional(),quantity_default:e.coerce.number().optional(),price_change:e.coerce.number().optional(),currency_type_id:e.string().optional(),point:e.coerce.number().optional(),is_gift:e.coerce.number().optional(),is_fc:e.coerce.number().optional(),show_on_web:e.coerce.number().optional(),show_price_on_web:e.coerce.number().optional(),cost_price:e.coerce.number().optional(),is_print_label:e.coerce.number().optional(),quantity_limit:e.coerce.number().optional(),is_kit:e.coerce.number().optional(),time_cooking:e.coerce.number().optional(),item_id_barcode:e.string().optional(),process_index:e.coerce.number().optional(),is_allow_discount:e.coerce.number().optional(),quantity_per_day:e.coerce.number().optional(),item_id_eat_with:e.string().optional(),is_parent:e.coerce.number().optional(),is_sub:e.coerce.number().optional(),item_id_mapping:e.string().optional(),effective_date:e.coerce.number().optional(),expire_date:e.coerce.number().optional(),sort:e.coerce.number().optional(),sort_online:e.coerce.number().optional(),revision:e.coerce.number().optional(),unit_uid:e.string().optional(),unit_secondary_uid:e.string().nullable().optional(),item_type_uid:e.string().optional(),item_class_uid:e.string().optional(),source_uid:e.string().nullable().optional(),brand_uid:e.string().optional(),city_uid:e.string().optional(),store_uid:e.string().optional(),company_uid:e.string().optional(),customization_uid:e.string().nullable().optional(),is_fabi:e.coerce.number().optional(),deleted:e.boolean().optional(),created_by:e.string().optional(),updated_by:e.string().optional(),deleted_by:e.string().nullable().optional(),created_at:e.coerce.number().optional(),updated_at:e.coerce.number().optional(),deleted_at:e.coerce.number().nullable().optional(),apply_with_store:e.coerce.number().optional(),extra_data:e.object({cross_price:e.array(e.object({price:e.coerce.number().optional(),quantity:e.coerce.number().optional()})).optional(),formula_qrcode:e.string().optional(),is_buffet_item:e.coerce.number().optional(),up_size_buffet:e.array(e.any()).optional(),is_item_service:e.coerce.number().optional(),is_virtual_item:e.coerce.number().optional(),price_by_source:e.array(e.object({price:e.coerce.number().optional(),source_id:e.string().optional(),price_times:e.array(e.any()).optional(),is_source_exist_in_city:e.boolean().optional()})).optional(),enable_edit_price:e.coerce.number().optional(),exclude_items_buffet:e.array(e.string()).optional(),no_update_quantity_toping:e.coerce.number().optional()}).optional(),enable_custom_item_id:e.coerce.number().optional(),Stores:e.array(e.any()).optional(),item_old:e.record(e.any()).optional()});const yn=e.object({id:e.string().optional(),item_id:e.string().optional(),item_name:e.string().min(1,"Tên món là bắt buộc"),description:e.string().optional(),ots_price:e.coerce.number().min(0).optional(),ots_tax:e.coerce.number().min(0).max(1).optional(),ta_price:e.coerce.number().min(0).optional(),ta_tax:e.coerce.number().min(0).max(1).optional(),time_cooking:e.coerce.number().optional(),time_sale_hour_day:e.coerce.number().optional(),time_sale_date_week:e.coerce.number().optional(),allow_take_away:e.coerce.number().optional(),is_eat_with:e.coerce.number().optional(),image_path:e.string().url().optional(),image_path_thumb:e.string().url().optional(),item_color:e.string().optional(),list_order:e.coerce.number().optional(),is_service:e.coerce.number().optional(),is_material:e.coerce.number().optional(),is_print_label:e.coerce.number().optional(),is_allow_discount:e.coerce.number().optional(),item_id_barcode:e.string().optional(),process_index:e.coerce.number().optional(),quantity_per_day:e.coerce.number().optional(),item_id_eat_with:e.string().optional(),is_parent:e.coerce.number().optional(),is_sub:e.coerce.number().optional(),item_id_mapping:e.string().optional(),effective_date:e.coerce.number().optional(),expire_date:e.coerce.number().optional(),sort:e.coerce.number().optional(),sort_online:e.coerce.number().optional(),unit_uid:e.string().min(1,"Đơn vị là bắt buộc").optional(),unit_secondary_uid:e.string().nullable().optional(),item_type_uid:e.string().min(1,"Loại món là bắt buộc"),item_class_uid:e.string().optional(),city_uid:e.string().optional(),store_uid:e.string().optional(),customization_uid:e.string().nullable().optional(),cross_price:e.array(e.object({price:e.coerce.number().min(0).optional(),quantity:e.coerce.number().min(0).optional()})).optional(),formula_qrcode:e.string().optional(),is_buffet_item:e.coerce.number().optional(),up_size_buffet:e.array(e.any()).optional(),is_item_service:e.coerce.number().optional(),is_virtual_item:e.coerce.number().optional(),price_by_source:e.array(e.object({price:e.coerce.number().min(0).optional(),source_id:e.string().optional(),price_times:e.array(e.any()).optional(),is_source_exist_in_city:e.boolean().optional()})).optional(),enable_edit_price:e.coerce.number().optional(),exclude_items_buffet:e.array(e.string()).optional(),no_update_quantity_toping:e.coerce.number().optional(),quantity:e.coerce.number().optional(),price:e.coerce.number().optional(),enable_custom_item_id:e.coerce.number().optional(),apply_with_store:e.coerce.number().optional()}),Ct=e.object({amount:e.coerce.number().min(0,"Số tiền phải lớn hơn hoặc bằng 0"),startDate:e.date({required_error:"Vui lòng chọn ngày bắt đầu"}),endDate:e.date({required_error:"Vui lòng chọn ngày kết thúc"}),selectedDays:e.array(e.number()).min(1,"Vui lòng chọn ít nhất 1 ngày"),selectedHours:e.array(e.number()).min(1,"Vui lòng chọn ít nhất 1 giờ")}).refine(n=>n.startDate&&n.endDate?n.endDate>=n.startDate:!0,{message:"Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu",path:["endDate"]}),Nt=[{value:1,label:"Thứ 2"},{value:2,label:"Thứ 3"},{value:4,label:"Thứ 4"},{value:8,label:"Thứ 5"},{value:16,label:"Thứ 6"},{value:32,label:"Thứ 7"},{value:64,label:"Chủ nhật"}],Dt=Array.from({length:24},(n,s)=>({value:s,label:`${s}h`}));function Tt({open:n,onOpenChange:s,onConfirm:i,sourceName:c,data:r}){const a=le({resolver:de(Ct),defaultValues:{amount:0,startDate:new Date,endDate:new Date,selectedDays:[],selectedHours:[]}});f.useEffect(()=>{if(n&&r){a.setValue("amount",Number(r.amount??0));const o=r.startDate?new Date(r.startDate):new Date,l=r.endDate?new Date(r.endDate):new Date;a.setValue("startDate",o),a.setValue("endDate",l),Array.isArray(r.selectedDays)&&a.setValue("selectedDays",r.selectedDays),Array.isArray(r.selectedHours)&&a.setValue("selectedHours",r.selectedHours)}},[n,r,a]);const d=o=>{i==null||i(o),s(!1),a.reset()},m=o=>{s(o),o||a.reset()},g=o=>{const l=a.getValues("selectedDays"),x=l.includes(o)?l.filter(_=>_!==o):[...l,o];a.setValue("selectedDays",x)},p=o=>{const l=a.getValues("selectedHours"),x=l.includes(o)?l.filter(_=>_!==o):[...l,o];a.setValue("selectedHours",x)};return t.jsx(me,{open:n,onOpenChange:m,children:t.jsxs(pe,{className:"max-w-2xl lg:max-w-2xl",children:[t.jsx(he,{children:t.jsxs(ge,{children:["Cấu hình giá theo khung thời gian nguồn ",c]})}),t.jsx(ue,{...a,children:t.jsxs("form",{onSubmit:a.handleSubmit(d),className:"space-y-6",children:[t.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[t.jsx(q,{className:"text-sm font-medium text-gray-600",children:"Số tiền"}),t.jsx($,{control:a.control,name:"amount",render:({field:o})=>t.jsxs(U,{children:[t.jsx(G,{children:t.jsx(ce,{type:"text",placeholder:"0",value:o.value,onChange:l=>{o.onChange(l.target.value)},onKeyDown:l=>{!/[0-9]/.test(l.key)&&!["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(l.key)&&l.preventDefault()}})}),t.jsx(M,{})]})})]}),t.jsxs("div",{className:"space-y-3",children:[t.jsx(q,{className:"text-sm font-medium text-gray-600",children:"Ngày áp dụng"}),t.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[t.jsx($,{control:a.control,name:"startDate",render:({field:o})=>t.jsxs(U,{children:[t.jsx(q,{className:"text-xs text-gray-500",children:"Ngày bắt đầu"}),t.jsx(G,{children:t.jsx(Se,{date:o.value,onDateChange:o.onChange,placeholder:"Chọn ngày bắt đầu",className:"border-blue-200 bg-blue-50"})}),t.jsx(M,{})]})}),t.jsx($,{control:a.control,name:"endDate",render:({field:o})=>t.jsxs(U,{children:[t.jsx(q,{className:"text-xs text-gray-500",children:"Ngày kết thúc"}),t.jsx(G,{children:t.jsx(Se,{date:o.value,onDateChange:o.onChange,placeholder:"Chọn ngày kết thúc",className:"border-blue-200 bg-blue-50"})}),t.jsx(M,{})]})})]})]}),t.jsxs("div",{className:"space-y-4",children:[t.jsx(q,{className:"text-sm font-medium text-gray-600",children:"Khung thời gian áp dụng"}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(q,{className:"text-xs text-gray-500",children:"Chọn ngày"}),t.jsx(Ce,{className:"h-3 w-3 text-gray-400"})]}),t.jsx("div",{className:"grid grid-cols-7 gap-2",children:Nt.map(o=>{const l=a.watch("selectedDays").includes(o.value);return t.jsx(E,{type:"button",variant:l?"default":"outline",size:"sm",onClick:()=>g(o.value),className:`w-full ${l?"bg-blue-600 text-white hover:bg-blue-700":"border-blue-200 text-gray-700 hover:bg-blue-50"}`,children:o.label},o.value)})}),t.jsx(M,{})]}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(q,{className:"text-xs text-gray-500",children:"Chọn giờ"}),t.jsx(Ce,{className:"h-3 w-3 text-gray-400"})]}),t.jsx("div",{className:"grid grid-cols-6 gap-2",children:Dt.map(o=>{const l=a.watch("selectedHours").includes(o.value);return t.jsx(E,{type:"button",variant:l?"default":"outline",size:"sm",onClick:()=>p(o.value),className:`w-full ${l?"bg-blue-600 text-white hover:bg-blue-700":"border-blue-200 text-gray-700 hover:bg-blue-50"}`,children:o.label},o.value)})}),t.jsx(M,{})]})]}),t.jsxs(xe,{className:"pt-4",children:[t.jsx(be,{asChild:!0,children:t.jsx(E,{type:"button",variant:"outline",children:"Hủy"})}),t.jsx(E,{type:"button",onClick:a.handleSubmit(d),children:"Xác nhận"})]})]})})]})})}const wt=e.object({price:e.coerce.number().min(0,"Số tiền phải lớn hơn hoặc bằng 0"),source_id:e.string().optional(),source_name:e.string().optional(),price_times:e.array(e.any()).optional(),is_source_exist_in_city:e.boolean().optional()});function vn({open:n,onOpenChange:s,onConfirm:i,sources:c,data:r}){const[a,d]=f.useState(null),[m,g]=f.useState(!1),[p,o]=f.useState([]),[l,x]=f.useState(null),[_,T]=f.useState(),v=le({resolver:de(wt),defaultValues:{source_id:(r==null?void 0:r.source_id)||"",price:(r==null?void 0:r.price)||0,price_times:(r==null?void 0:r.price_times)||[]}});f.useEffect(()=>{if(n&&r){v.setValue("source_id",r.source_id),v.setValue("price",r.price),v.setValue("price_times",r.price_times);const u=c.find(h=>h.sourceId===r.source_id);if(d(u||null),Array.isArray(r.price_times)&&r.price_times.length>0){const h=r.price_times.map(y=>({id:Date.now().toString()+Math.random(),price:y.price,startDate:new Date(y.from_date),endDate:new Date(y.to_date),selectedDays:y.time_sale_date_week?ut(y.time_sale_date_week):[],selectedHours:y.time_sale_hour_day?dt(y.time_sale_hour_day):[]}));o(h)}else o([])}},[n,r,v,c]);const N=u=>{const h={...u,source_id:a==null?void 0:a.sourceId,source_name:a==null?void 0:a.sourceName,price_times:ae(p),is_source_exist_in_city:!0};i==null||i(h),s(!1),v.reset(),d(null),o([])},A=u=>{const h={id:l||Date.now().toString(),price:Number(u.amount||0),startDate:u.startDate,endDate:u.endDate,selectedDays:u.selectedDays,selectedHours:u.selectedHours};o(y=>{let k;l?k=y.map(K=>K.id===l?h:K):k=[...y,h];const P=ae(k);return v.setValue("price_times",P),k}),x(null)},j=u=>{o(h=>{const y=h.filter(P=>P.id!==u),k=ae(y);return v.setValue("price_times",k),y})},w=u=>{u.preventDefault(),u.stopPropagation(),v.handleSubmit(N)(u)},I=u=>{s(u),u||(v.reset(),d(null),g(!1),o([]),v.setValue("price_times",[]),x(null),T(void 0))};return t.jsxs(me,{open:n,onOpenChange:I,children:[t.jsxs(pe,{className:"max-w-md lg:max-w-2xl",children:[t.jsx(he,{children:t.jsx(ge,{children:"Cấu hình giá theo nguồn"})}),t.jsx(ue,{...v,children:t.jsxs("form",{onSubmit:w,className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[t.jsx(q,{className:"text-sm font-medium text-gray-600",children:"Nguồn đơn"}),t.jsx($,{control:v.control,name:"source_id",render:({field:u})=>t.jsxs(U,{children:[t.jsx(De,{options:c.map(h=>({value:h.sourceId,label:h.sourceName})),value:u.value,onValueChange:h=>{u.onChange(h);const y=c.find(k=>k.sourceId===h);d(y||null)},placeholder:"Chọn nguồn đơn",searchPlaceholder:"Tìm nguồn đơn...",emptyText:"Không có nguồn phù hợp.",className:"w-full"}),t.jsx(M,{})]})})]}),t.jsxs("div",{className:"grid grid-cols-[120px_1fr] items-center gap-4",children:[t.jsx(q,{className:"text-sm font-medium text-gray-600",children:"Số tiền"}),t.jsx($,{control:v.control,name:"price",render:({field:u})=>t.jsxs(U,{children:[t.jsx(G,{children:t.jsx(ce,{type:"text",placeholder:"0",value:u.value,onChange:h=>{u.onChange(h.target.value)},onKeyDown:h=>{!/[0-9]/.test(h.key)&&!["Backspace","Delete","ArrowLeft","ArrowRight","Tab"].includes(h.key)&&h.preventDefault()}})}),t.jsx(M,{})]})})]}),a&&t.jsxs("div",{className:"space-y-4 border-t pt-4",children:[t.jsxs("div",{className:"space-y-3",children:[t.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Cấu hình giá theo khung thời gian"}),t.jsxs("div",{className:"text-sm leading-relaxed text-gray-600",children:["Giá theo nguồn ",t.jsx("span",{className:"font-semibold text-blue-600",children:a.sourceName})," sẽ được lấy theo số tiền"," ",t.jsxs("span",{className:"font-semibold text-blue-600",children:[v.getValues("price")||"0"," đ"]}),". Khi cấu hình giá theo khung thời gian số tiền sẽ hiển thị theo các khung thời gian cấu hình dưới đây"]}),t.jsx("div",{className:"flex justify-end",children:t.jsx(E,{type:"button",variant:"outline",className:"text-blue-600 hover:text-blue-700",onClick:()=>{x(null),T({amount:Number(v.getValues("price")||0)}),g(!0)},children:"Thêm cấu hình"})})]}),p.length===0?t.jsx("div",{className:"flex min-h-[120px] items-center justify-center rounded-lg border-2 border-dashed border-gray-200 bg-gray-50",children:t.jsxs("div",{className:"text-center text-gray-500",children:[t.jsx("p",{className:"text-sm",children:"Chưa có cấu hình khung thời gian nào"}),t.jsx("p",{className:"mt-1 text-xs",children:'Nhấn "Thêm cấu hình" để bắt đầu'})]})}):t.jsx("div",{className:"space-y-3",children:p.map(u=>{var h;return t.jsxs("div",{className:"flex cursor-pointer items-start gap-3 rounded-lg border border-gray-200 bg-white p-4",onClick:y=>{y.preventDefault(),y.stopPropagation(),x(u.id),T({amount:Number(u.price||0),startDate:u.startDate,endDate:u.endDate,selectedDays:u.selectedDays,selectedHours:u.selectedHours}),g(!0)},children:[t.jsx("div",{className:"mt-1 h-2 w-2 rounded-full bg-gray-400"}),t.jsxs("div",{className:"flex-1 space-y-1",children:[t.jsxs("div",{className:"text-sm text-gray-900",children:["Từ ngày ",t.jsx("span",{className:"font-semibold",children:Ne(u.startDate)})," đến ngày"," ",t.jsx("span",{className:"font-semibold",children:Ne(u.endDate)})]}),t.jsxs("div",{className:"text-sm text-gray-900",children:["Giá: ",t.jsxs("span",{className:"font-semibold",children:[((h=u.price)==null?void 0:h.toLocaleString("vi-VN"))||"0"," ₫"]})]}),t.jsxs("div",{className:"text-sm text-gray-900",children:["Khung giờ ",t.jsx("span",{className:"font-semibold",children:lt(u.selectedHours)})," Thứ"," ",t.jsx("span",{className:"font-semibold",children:ct(u.selectedDays)})]})]}),t.jsx(E,{type:"button",variant:"ghost",size:"sm",onClick:()=>j(u.id),className:"h-8 w-8 p-0 text-gray-400 hover:text-gray-600",children:t.jsx(it,{className:"h-4 w-4"})})]},u.id)})})]}),t.jsxs(xe,{className:"pt-4",children:[t.jsx(be,{asChild:!0,children:t.jsx(E,{type:"button",variant:"outline",children:"Hủy"})}),t.jsx(E,{type:"submit",children:"Xác nhận"})]})]})})]}),t.jsx(Tt,{open:m,onOpenChange:g,onConfirm:A,sourceName:(a==null?void 0:a.sourceName)||"",data:_})]})}export{un as B,dn as C,cn as I,vn as P,bn as S,nn as a,rn as b,hn as c,on as d,an as e,Ee as f,mn as g,fn as h,tn as i,sn as j,ot as k,pn as l,en as m,yn as n,gn as p,Re as r,_n as s,ln as u,xn as v};
