import{r as f,j as e,B as X,c,x as $}from"./index-B283E1a3.js";import{B as b}from"./badge-DZXns0dL.js";import{C as R,a as F,b as G,c as L,d as y,e as x,f as q}from"./command-C1ySvjo8.js";import{P as z,a as H,b as J}from"./popover-CCXriU_R.js";import{S as N}from"./separator-DLHnMAQ0.js";import{C as w}from"./circle-x-CQicUDeF.js";import{X as Q}from"./calendar-AxR9kFpj.js";import{C as S}from"./select-BzVwefGp.js";import{C}from"./check-CjIon4B5.js";const P=$("m-1 transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300",{variants:{variant:{default:"border-foreground/10 text-foreground bg-card hover:bg-card/80",secondary:"border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",inverted:"inverted"}},defaultVariants:{variant:"default"}}),U=f.forwardRef(({options:n,onValueChange:l,variant:p,defaultValue:k=[],value:a,placeholder:D="Select options",animation:j=0,maxCount:d=3,modalPopover:I=!1,asChild:W,className:E,...B},O)=>{const[T,o]=f.useState(k),[V,i]=f.useState(!1),[g,Y]=f.useState(!1),t=a!==void 0?a:T,_=s=>{if(s.key==="Enter")i(!0);else if(s.key==="Backspace"&&!s.currentTarget.value){const r=[...t];r.pop(),a===void 0&&o(r),l(r)}},v=s=>{const r=t.includes(s)?t.filter(m=>m!==s):[...t,s];a===void 0&&o(r),l(r)},h=()=>{a===void 0&&o([]),l([])},A=()=>{i(s=>!s)},K=()=>{const s=t.slice(0,d);a===void 0&&o(s),l(s)},M=()=>{if(t.length===n.length)h();else{const s=n.map(r=>r.value);a===void 0&&o(s),l(s)}};return e.jsxs(z,{open:V,onOpenChange:i,modal:I,children:[e.jsx(H,{asChild:!0,children:e.jsx(X,{ref:O,...B,onClick:A,className:c("flex h-auto min-h-10 w-full items-center justify-between rounded-md border bg-inherit p-1 hover:bg-inherit [&_svg]:pointer-events-auto",E),children:t.length>0?e.jsxs("div",{className:"flex w-full items-center justify-between",children:[e.jsxs("div",{className:"flex flex-wrap items-center",children:[t.slice(0,d).map(s=>{const r=n.find(u=>u.value===s),m=r==null?void 0:r.icon;return e.jsxs(b,{className:c(g?"animate-bounce":"",P({variant:p})),style:{animationDuration:`${j}s`},children:[m&&e.jsx(m,{className:"mr-2 h-4 w-4"}),r==null?void 0:r.label,e.jsx("div",{className:"ml-2 flex items-center",onClick:u=>{u.stopPropagation(),u.preventDefault(),v(s)},children:e.jsx(w,{className:"h-4 w-4 cursor-pointer"})})]},s)}),t.length>d&&e.jsxs(b,{className:c("text-foreground border-foreground/1 bg-transparent hover:bg-transparent",g?"animate-bounce":"",P({variant:p})),style:{animationDuration:`${j}s`},children:[`+ ${t.length-d} thêm`,e.jsx("div",{className:"ml-2 flex items-center",onClick:s=>{s.stopPropagation(),s.preventDefault(),K()},children:e.jsx(w,{className:"h-4 w-4 cursor-pointer"})})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center",onClick:s=>{s.stopPropagation(),s.preventDefault(),h()},children:e.jsx(Q,{className:"text-muted-foreground mx-2 h-4 cursor-pointer"})}),e.jsx(N,{orientation:"vertical",className:"flex h-full min-h-6"}),e.jsx(S,{className:"text-muted-foreground mx-2 h-4 cursor-pointer"})]})]}):e.jsxs("div",{className:"mx-auto flex w-full items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground mx-3 text-sm",children:D}),e.jsx(S,{className:"text-muted-foreground mx-2 h-4 cursor-pointer"})]})})}),e.jsx(J,{className:"w-auto p-0",align:"start",onEscapeKeyDown:()=>i(!1),children:e.jsxs(R,{children:[e.jsx(F,{placeholder:"Tìm kiếm...",onKeyDown:_}),e.jsxs(G,{children:[e.jsx(L,{children:"No results found."}),e.jsxs(y,{children:[e.jsxs(x,{onSelect:M,className:"cursor-pointer",children:[e.jsx("div",{className:c("border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",t.length===n.length?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(C,{className:"h-4 w-4"})}),e.jsx("span",{children:"(Tất cả)"})]},"all"),n.map(s=>{const r=t.includes(s.value);return e.jsxs(x,{onSelect:()=>v(s.value),className:"cursor-pointer",children:[e.jsx("div",{className:c("border-primary mr-2 flex h-4 w-4 items-center justify-center rounded-sm border",r?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(C,{className:"h-4 w-4"})}),s.icon&&e.jsx(s.icon,{className:"text-muted-foreground mr-2 h-4 w-4"}),e.jsx("span",{children:s.label})]},s.value)})]}),e.jsx(q,{}),e.jsx(y,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[t.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(x,{onSelect:h,className:"flex-1 cursor-pointer justify-center",children:"Xoá"}),e.jsx(N,{orientation:"vertical",className:"flex h-full min-h-6"})]}),e.jsx(x,{onSelect:()=>i(!1),className:"max-w-full flex-1 cursor-pointer justify-center",children:"Đóng"})]})})]})]})})]})});U.displayName="MultiSelect";export{U as M};
