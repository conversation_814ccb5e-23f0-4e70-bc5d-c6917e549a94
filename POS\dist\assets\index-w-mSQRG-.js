import{j as e,B as b,r as u,a4 as y,ay as P,h as se}from"./index-B283E1a3.js";import{P as ae}from"./profile-dropdown-DhwpuuhW.js";import{S as ne,T as oe}from"./search-eyocbSug.js";import"./pos-api-C7RsFAun.js";import"./vietqr-api-BbJFOv9v.js";import{d as ie,e as le,f as re,h as B,u as _}from"./use-customizations-BFIcwtLL.js";import"./user-Cxn8z8PZ.js";import"./crm-api-CE_jLH-z.js";import{H as ce}from"./header-BZ_7I_4c.js";import{M as de}from"./main-BlYSJOOd.js";import{I as me}from"./IconCopy-Bx-S4wCr.js";import{I as he}from"./IconTrash-C3WF0IhL.js";import{u as pe,b as ue,e as xe,f as T}from"./index-AD4tjDOS.js";import"./date-range-picker-B7aoXHt3.js";import{L as fe}from"./form-dNL1hWKC.js";import{T as O,a as L,b as S,c as R,d as A,e as D}from"./table-vHuVXp9f.js";import{a as ye,C as Ce}from"./chevron-right-CVT48KKP.js";import{D as je,a as ge,b as Ne,c as U}from"./dropdown-menu-JDsssJHk.js";import{S as H,a as K,b as V,c as $,d as M}from"./select-BzVwefGp.js";import{I as G}from"./input-Bx4sCRS0.js";import{C as ze}from"./index-BQauI_Wp.js";import{P as E}from"./modal-COeiv6He.js";import{I as Q}from"./IconDownload-X--bSLsj.js";import{I as q}from"./IconUpload-DVwAD5aZ.js";import{u as F}from"./use-pos-data-D7fYs1vU.js";import"./avatar-CfLE65or.js";import"./search-context-CZoJZmsi.js";import"./command-C1ySvjo8.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";import"./createReactComponent-WabRa4kY.js";import"./scroll-area-BlxlVxpe.js";import"./index-Df0XEEuz.js";import"./IconChevronRight-jxL9ONfH.js";import"./IconSearch-S0sgK6Kj.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./query-keys-3lmd-xp6.js";import"./separator-DLHnMAQ0.js";import"./react-icons.esm-CwfFxlzT.js";import"./popover-CCXriU_R.js";import"./index-BhFEt02S.js";import"./check-CjIon4B5.js";import"./index-DY0KH0l4.js";import"./use-auth-Cjjp-n-O.js";const be=({onCopyCustomization:l,onDeleteCustomization:r,currentPage:a=1,pageSize:o=20})=>[{id:"index",header:"#",cell:({row:s})=>{const t=(a-1)*o+s.index+1;return e.jsx("div",{className:"text-center",children:t})},size:60},{accessorKey:"name",header:"Tên customization",cell:({row:s})=>{const t=s.original;return e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:t.name}),t.listItem.length>0&&e.jsxs("span",{className:"text-xs text-gray-500",children:[t.listItem.length," món"]})]})},size:300},{accessorKey:"cityName",header:"Thành phố",cell:({row:s})=>{const t=s.original;return e.jsx("div",{className:"text-sm",children:t.cityName})},size:150},{id:"copy",header:()=>e.jsx("div",{className:"text-center",children:"Sao chép"}),cell:({row:s})=>{const t=s.original;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(b,{variant:"ghost",size:"sm",onClick:c=>{c.stopPropagation(),l(t)},className:"h-8 w-8 p-0",children:e.jsx(me,{className:"h-4 w-4"})})})},size:100},{id:"actions",header:()=>e.jsx("div",{className:"text-center"}),cell:({row:s})=>{const t=s.original;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(b,{variant:"ghost",size:"sm",onClick:c=>{c.stopPropagation(),r(t)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:e.jsx(he,{className:"h-4 w-4"})})})},size:80}];function ve({currentPage:l,onPageChange:r,hasNextPage:a}){const o=()=>{l>1&&r(l-1)},s=()=>{a&&r(l+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(b,{variant:"outline",size:"sm",onClick:o,disabled:l===1,className:"flex items-center gap-2",children:[e.jsx(ye,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:l}),e.jsxs(b,{variant:"outline",size:"sm",onClick:s,disabled:!a,className:"flex items-center gap-2",children:["Sau",e.jsx(Ce,{className:"h-4 w-4"})]})]})}function we({columns:l,data:r,isLoading:a=!1,onRowClick:o,currentPage:s,onPageChange:t,hasNextPage:c}){var m,x;const[h,d]=u.useState([]),n=pe({data:r,columns:l,getCoreRowModel:xe(),getSortedRowModel:ue(),onSortingChange:d,state:{sorting:h}});return a?e.jsx("div",{className:"rounded-md border",children:e.jsxs(O,{children:[e.jsx(L,{children:n.getHeaderGroups().map(i=>e.jsx(S,{children:i.headers.map(p=>e.jsx(R,{children:p.isPlaceholder?null:T(p.column.columnDef.header,p.getContext())},p.id))},i.id))}),e.jsx(A,{children:Array.from({length:5}).map((i,p)=>e.jsx(S,{children:l.map((C,v)=>e.jsx(D,{children:e.jsx("div",{className:"h-4 w-full animate-pulse rounded bg-gray-200"})},v))},p))})]})}):e.jsxs("div",{children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(O,{children:[e.jsx(L,{children:n.getHeaderGroups().map(i=>e.jsx(S,{children:i.headers.map(p=>e.jsx(R,{children:p.isPlaceholder?null:T(p.column.columnDef.header,p.getContext())},p.id))},i.id))}),e.jsxs(A,{children:[((m=n.getRowModel().rows)==null?void 0:m.length)&&n.getRowModel().rows.map(i=>e.jsx(S,{"data-state":i.getIsSelected()&&"selected",className:o?"cursor-pointer hover:bg-gray-50":"",onClick:()=>o==null?void 0:o(i.original),children:i.getVisibleCells().map(p=>e.jsx(D,{children:T(p.column.columnDef.cell,p.getContext())},p.id))},i.id)),!((x=n.getRowModel().rows)!=null&&x.length)&&e.jsx(S,{children:e.jsx(D,{colSpan:l.length,className:"h-24 text-center",children:"Không có customization nào."})})]})]})}),e.jsx(ve,{currentPage:s,onPageChange:t,hasNextPage:c})]})}function Se({searchQuery:l,onSearchQueryChange:r,onSearchKeyDown:a,selectedCityId:o,onCityChange:s,cities:t,onExportCustomizations:c,onImportCustomizations:h,onCreateCustomization:d}){return e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Customization"}),e.jsx(G,{placeholder:"Tìm kiếm customization",className:"w-64",value:l,onChange:n=>r(n.target.value),onKeyDown:a}),e.jsxs(H,{value:o,onValueChange:s,children:[e.jsx(K,{className:"w-48",children:e.jsx(V,{placeholder:"Chọn thành phố"})}),e.jsxs($,{children:[e.jsx(M,{value:"all",children:"Tất cả thành phố"}),t.map(n=>e.jsx(M,{value:n.id,children:n.city_name},n.id))]})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(je,{children:[e.jsx(ge,{asChild:!0,children:e.jsx(b,{size:"sm",variant:"outline",children:"Tiện ích"})}),e.jsxs(Ne,{align:"end",children:[e.jsx(U,{onClick:c,children:"Xuất, Chi tiết customization"}),e.jsx(U,{onClick:h,children:"Thêm customization từ file"})]})]}),e.jsx(b,{size:"sm",variant:"default",onClick:d,children:"Tạo customization"})]})]})}function Ie({open:l,onOpenChange:r,selectedCustomization:a,onCancel:o,onConfirm:s,isLoading:t}){return e.jsx(ze,{open:l,onOpenChange:r,title:"Xác nhận xóa customization",content:`Bạn có chắc muốn xóa customization "${a==null?void 0:a.name}"?`,onCancel:o,onConfirm:s,confirmText:"Xóa",cancelText:"Hủy",isLoading:t})}function Me({open:l,onOpenChange:r,copyName:a,onCopyNameChange:o,onCancel:s,onConfirm:t,isLoading:c}){return e.jsx(E,{title:"Sao chép customization",open:l,onOpenChange:r,onCancel:s,onConfirm:t,confirmText:"Sao chép",cancelText:"Hủy",isLoading:c,children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(fe,{htmlFor:"copy-name",className:"min-w-[120px] text-sm font-medium",children:["Tên customization ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(G,{id:"copy-name",value:a,onChange:h=>o(h.target.value),placeholder:"Nhập tên customization",className:"flex-1"})]})})})}function Te({open:l,onOpenChange:r,showParsedData:a,exportCityId:o,onExportCityIdChange:s,cities:t,selectedFile:c,parsedData:h,isExporting:d,isSaving:n,onCancel:m,onConfirm:x,onDownloadExportFile:i,onUploadFile:p}){return e.jsx(E,{title:"Xuất, sửa customization",open:l,onOpenChange:r,onCancel:m,onConfirm:x,confirmText:a?"Lưu":"Tiếp tục",cancelText:"Hủy",centerTitle:!0,maxWidth:a?"sm:max-w-6xl":"sm:max-w-[400px]",isLoading:n,children:e.jsxs("div",{className:"space-y-4",children:[!a&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs(H,{value:o,onValueChange:s,children:[e.jsx(K,{className:"w-full",children:e.jsx(V,{placeholder:"Chọn thành phố"})}),e.jsxs($,{children:[e.jsx(M,{value:"all",children:"Tất cả thành phố"}),t.map(C=>e.jsx(M,{value:C.id,children:C.city_name},C.id))]})]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống"})}),e.jsx(b,{size:"sm",variant:"outline",onClick:i,disabled:d,className:"h-8 w-8 p-0",children:e.jsx(Q,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"rounded-md bg-yellow-50 p-3 text-sm text-yellow-800",children:[e.jsx("strong",{children:"Không sửa các cột:"})," ID, Thành phố."]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-600",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),c&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",c.name]})]}),e.jsx(b,{size:"sm",variant:"default",onClick:p,className:"h-8 w-8 p-0",children:e.jsx(q,{className:"h-4 w-4"})})]})]})]}),a&&e.jsx("div",{className:"max-h-60 overflow-y-auto rounded-md border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Thành phố"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món áp dụng"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên nhóm"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Yêu cầu chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Giới hạn chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món theo nhóm"})]})}),e.jsx("tbody",{children:h.map((C,v)=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-3 py-2",children:C.name}),e.jsx("td",{className:"px-3 py-2",children:C.cityName}),e.jsx("td",{className:"px-3 py-2",children:C.appliedItemCodes}),e.jsx("td",{className:"px-3 py-2",children:C.groupName}),e.jsx("td",{className:"px-3 py-2",children:C.minRequired}),e.jsx("td",{className:"px-3 py-2",children:C.maxAllowed}),e.jsx("td",{className:"px-3 py-2",children:C.groupItemCodes})]},v))})]})})]})})}function De({open:l,onOpenChange:r,showImportParsedData:a,importSelectedFile:o,importParsedData:s,isLoading:t,onCancel:c,onConfirm:h,onDownloadTemplate:d,onImportFileUpload:n}){return e.jsx(E,{title:"Thêm customization từ file",open:l,onOpenChange:r,onCancel:c,onConfirm:h,confirmText:a?"Lưu":"Tiếp tục",cancelText:"Hủy",centerTitle:!0,maxWidth:a?"sm:max-w-6xl":"sm:max-w-[400px]",isLoading:t,children:e.jsxs("div",{className:"space-y-4",children:[!a&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 1. Tải file mẫu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Tải xuống file mẫu để tạo customization"})}),e.jsx(b,{size:"sm",variant:"outline",onClick:d,className:"h-8 w-8 p-0",children:e.jsx(Q,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"rounded-md bg-yellow-50 p-3 text-sm text-yellow-800",children:[e.jsx("strong",{children:"Không được để trống các cột:"})," Tên, Thành phố, Tên nhóm, Mã món theo nhóm."]})]}),e.jsxs("div",{className:"rounded-md border p-3",children:[e.jsx("div",{className:"mb-3 font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"}),o&&e.jsxs("span",{className:"mt-1 text-xs text-gray-500",children:["File đã chọn: ",o.name]})]}),e.jsx(b,{size:"sm",variant:"default",onClick:n,className:"h-8 w-8 p-0",children:e.jsx(q,{className:"h-4 w-4"})})]})]})]}),a&&e.jsx("div",{className:"max-h-60 overflow-y-auto rounded-md border",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Thành phố"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món áp dụng"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Tên nhóm"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Yêu cầu chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Giới hạn chọn"}),e.jsx("th",{className:"border-b px-3 py-2 text-left",children:"Mã món theo nhóm"})]})}),e.jsx("tbody",{children:s.map((m,x)=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"px-3 py-2",children:m.name}),e.jsx("td",{className:"px-3 py-2",children:m.cityName}),e.jsx("td",{className:"px-3 py-2",children:m.appliedItemCodes}),e.jsx("td",{className:"px-3 py-2",children:m.groupName}),e.jsx("td",{className:"px-3 py-2",children:m.minRequired}),e.jsx("td",{className:"px-3 py-2",children:m.maxAllowed}),e.jsx("td",{className:"px-3 py-2",children:m.groupItemCodes})]},x))})]})})]})})}function Ee(){const[l,r]=u.useState(""),[a,o]=u.useState(""),[s,t]=u.useState("all"),{cities:c}=F(),h=s==="all"?c.map(m=>m.id):[s],d=()=>{r(a)};return{searchTerm:l,searchQuery:a,setSearchQuery:o,selectedCityId:s,setSelectedCityId:t,listCityUid:h,cities:c,handleSearchSubmit:d,handleSearchKeyDown:m=>{m.key==="Enter"&&(m.preventDefault(),d())}}}function Fe(){const[l,r]=u.useState(!1),[a,o]=u.useState(!1),[s,t]=u.useState(!1),[c,h]=u.useState(!1),[d,n]=u.useState(null);return{confirmModalOpen:l,copyModalOpen:a,exportModalOpen:s,importModalOpen:c,selectedCustomization:d,openCopyModal:f=>{n(f),o(!0)},closeCopyModal:()=>{o(!1),n(null)},openDeleteModal:f=>{n(f),r(!0)},closeDeleteModal:()=>{r(!1),n(null)},openExportModal:()=>{t(!0)},closeExportModal:()=>{t(!1)},openImportModal:()=>{h(!0)},closeImportModal:()=>{h(!1)},setConfirmModalOpen:r,setCopyModalOpen:o,setExportModalOpen:t,setImportModalOpen:h,setSelectedCustomization:n}}function ke(){const[l,r]=u.useState(""),a=ie(),o=c=>{r(`${c.name} - Copy`)},s=()=>{r("")};return{copyName:l,setCopyName:r,initializeCopyName:o,resetCopyName:s,handleCopyCustomization:async(c,h)=>{if(!l.trim())return y.error("Vui lòng nhập tên customization"),!1;try{return await a.mutateAsync({customizationId:c.id,newName:l.trim(),targetCityUid:h}),y.success("Sao chép customization thành công"),s(),!0}catch(d){return console.error("Copy customization error:",d),y.error("Có lỗi xảy ra khi sao chép customization"),!1}},isLoading:a.isPending}}function Pe(){const l=le();return{handleConfirmDelete:async a=>{if(!a)return!1;try{return await l.mutateAsync(a.id),y.success(`Đã xóa customization "${a.name}" thành công!`),!0}catch{return y.error("Không thể xóa customization"),!1}},isLoading:l.isPending}}function Z(){return{parseExcelFile:async(a,o=!1)=>{try{const s=await P(()=>import("./xlsx-DkH2s96g.js"),[]),t=await a.arrayBuffer(),c=s.read(t,{type:"array"}),h=c.SheetNames[0],d=c.Sheets[h],n=s.utils.sheet_to_json(d,{header:1}),m=[];for(let x=1;x<n.length;x++){const i=n[x];o?i&&i.length>=7&&m.push({id:"",name:String(i[0]||"").trim(),cityName:String(i[1]||"").trim(),appliedItemCodes:String(i[2]||"").trim(),groupName:String(i[3]||"").trim(),minRequired:Number(i[4])||0,maxAllowed:Number(i[5])||0,groupItemCodes:String(i[6]||"").trim()}):i&&i.length>=8&&m.push({id:String(i[0]||"").trim(),name:String(i[1]||"").trim(),cityName:String(i[2]||"").trim(),appliedItemCodes:String(i[3]||"").trim(),groupName:String(i[4]||"").trim(),minRequired:Number(i[5])||0,maxAllowed:Number(i[6])||0,groupItemCodes:String(i[7]||"").trim()})}return m}catch(s){throw y.error("Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file."),s}},downloadTemplate:()=>{const a=[["Tên","Thành phố","Mã món áp dụng","Tên nhóm","Yêu cầu chọn","Giới hạn chọn","Mã món theo nhóm"],["Tên customization","Hồ Chí Minh","","Tên nhóm","1","1","ITEM-CODE1,ITEM-CODE2"]];P(()=>import("./xlsx-DkH2s96g.js"),[]).then(o=>{const s=o.utils.book_new(),t=o.utils.aoa_to_sheet(a);o.utils.book_append_sheet(s,t,"Template"),o.writeFile(s,"create-customization-in-city.xlsx"),y.success("Đã tải file mẫu thành công!")})}}}function _e(){const[l,r]=u.useState("all"),[a,o]=u.useState(null),[s,t]=u.useState([]),[c,h]=u.useState(!1),d=u.useRef(null),{cities:n}=F(),{parseExcelFile:m}=Z(),x=re(),i=B(),p=()=>{h(!1),t([]),o(null)};return{exportCityId:l,setExportCityId:r,selectedFile:a,parsedData:s,showParsedData:c,fileInputRef:d,cities:n,resetExportState:p,handleDownloadExportFile:async()=>{try{const f=l==="all"?n.map(g=>g.id):[l];await x.mutateAsync({list_city_uid:f}),y.success("File đã được tải xuống thành công!")}catch{y.error("Có lỗi xảy ra khi tải file xuống")}},handleUploadFile:()=>{var f;(f=d.current)==null||f.click()},handleFileChange:async f=>{var I;const g=(I=f.target.files)==null?void 0:I[0];if(g){o(g);try{const w=await m(g,!1);t(w),h(!0),y.success(`Đã phân tích ${w.length} customization từ file!`)}catch{}}},handleSaveImportedData:async()=>{if(s.length===0)return y.error("Không có dữ liệu để lưu"),!1;try{return await i.mutateAsync({parsedData:s}),y.success(`Đã tạo thành công ${s.length} customization!`),p(),!0}catch{return y.error("Lỗi khi tạo customization. Vui lòng thử lại."),!1}},isExporting:x.isPending,isSaving:i.isPending}}function Oe(){const[l,r]=u.useState(null),[a,o]=u.useState([]),[s,t]=u.useState(!1),c=u.useRef(null),{parseExcelFile:h,downloadTemplate:d}=Z(),n=B(),{cities:m}=F(),x=()=>{t(!1),o([]),r(null)};return{importSelectedFile:l,importParsedData:a,showImportParsedData:s,importFileInputRef:c,resetImportState:x,handleDownloadTemplate:()=>{d()},handleImportFileUpload:()=>{var N;(N=c.current)==null||N.click()},handleImportFileChange:async N=>{var f;const j=(f=N.target.files)==null?void 0:f[0];if(j){r(j);try{const g=await h(j,!0);o(g),t(!0),y.success(`Đã phân tích ${g.length} customization từ file!`)}catch{}}},handleSaveImportedCustomizations:async()=>{if(a.length===0)return y.error("Không có dữ liệu để lưu"),!1;try{const N=new Map;m.forEach(g=>{N.set(g.city_name,g.id)});const j=a[0],f=j!=null&&j.cityName?N.get(j.cityName):void 0;return!f&&(j!=null&&j.cityName)?(y.error(`Không tìm thấy thành phố: ${j.cityName}`),!1):(await n.mutateAsync({parsedData:a,cityUid:f}),y.success(`Đã tạo thành công ${a.length} customization!`),x(),!0)}catch{return y.error("Lỗi khi tạo customization. Vui lòng thử lại."),!1}},isLoading:n.isPending}}function Le(){const[r,a]=u.useState(1),o=se(),s=Ee(),t=Fe(),c=ke(),h=Pe(),d=_e(),n=Oe(),{data:m=[],isLoading:x,error:i}=_({searchTerm:s.searchTerm||void 0,list_city_uid:s.listCityUid,page:r,limit:20}),{data:p=[]}=_({searchTerm:s.searchTerm||void 0,list_city_uid:s.listCityUid,page:r+1,limit:20,enabled:m.length>0}),C=m.length===0?!1:p.length>0;u.useEffect(()=>{a(1)},[s.selectedCityId,s.searchTerm]);const v=z=>{c.initializeCopyName(z),t.openCopyModal(z)},N=z=>{t.openDeleteModal(z)},j=async()=>{if(!t.selectedCustomization)return;const z=s.selectedCityId!=="all"?s.selectedCityId:void 0;await c.handleCopyCustomization(t.selectedCustomization,z)&&t.closeCopyModal()},f=async()=>{if(!t.selectedCustomization)return;await h.handleConfirmDelete(t.selectedCustomization)&&t.closeDeleteModal()},g=()=>{c.resetCopyName(),t.closeCopyModal()},I=()=>{d.resetExportState(),t.openExportModal()},w=()=>{d.resetExportState(),t.closeExportModal()},Y=async()=>{await d.handleSaveImportedData()&&t.closeExportModal()},W=()=>{n.resetImportState(),t.openImportModal()},k=()=>{n.resetImportState(),t.closeImportModal()},J=async()=>{await n.handleSaveImportedCustomizations()&&t.closeImportModal()},X=()=>{o({to:"/menu/customization/customization-in-city/detail"})},ee=z=>{o({to:"/menu/customization/customization-in-city/detail/$customizationId",params:{customizationId:z.id}})},te=be({onCopyCustomization:v,onDeleteCustomization:N,currentPage:r,pageSize:20});return e.jsxs(e.Fragment,{children:[e.jsx(ce,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(ne,{}),e.jsx(oe,{}),e.jsx(ae,{})]})}),e.jsx(de,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Se,{searchQuery:s.searchQuery,onSearchQueryChange:s.setSearchQuery,onSearchKeyDown:s.handleSearchKeyDown,selectedCityId:s.selectedCityId,onCityChange:s.setSelectedCityId,cities:s.cities,onExportCustomizations:I,onImportCustomizations:W,onCreateCustomization:X}),i&&e.jsxs("div",{className:"mb-4 rounded-md bg-red-50 p-4 text-red-700",children:["Có lỗi xảy ra khi tải dữ liệu: ",i.message]}),e.jsx(we,{columns:te,data:m,isLoading:x,onRowClick:ee,currentPage:r,onPageChange:a,hasNextPage:C}),e.jsx(Ie,{open:t.confirmModalOpen,onOpenChange:t.setConfirmModalOpen,selectedCustomization:t.selectedCustomization,onCancel:t.closeDeleteModal,onConfirm:f,isLoading:h.isLoading}),e.jsx(Me,{open:t.copyModalOpen,onOpenChange:t.setCopyModalOpen,selectedCustomization:t.selectedCustomization,copyName:c.copyName,onCopyNameChange:c.setCopyName,onCancel:g,onConfirm:j,isLoading:c.isLoading}),e.jsx(Te,{open:t.exportModalOpen,onOpenChange:t.setExportModalOpen,showParsedData:d.showParsedData,exportCityId:d.exportCityId,onExportCityIdChange:d.setExportCityId,cities:d.cities,selectedFile:d.selectedFile,parsedData:d.parsedData,isExporting:d.isExporting,isSaving:d.isSaving,onCancel:w,onConfirm:d.showParsedData?Y:w,onDownloadExportFile:d.handleDownloadExportFile,onUploadFile:d.handleUploadFile}),e.jsx(De,{open:t.importModalOpen,onOpenChange:t.setImportModalOpen,showImportParsedData:n.showImportParsedData,importSelectedFile:n.importSelectedFile,importParsedData:n.importParsedData,isLoading:n.isLoading,onCancel:k,onConfirm:n.showImportParsedData?J:k,onDownloadTemplate:n.handleDownloadTemplate,onImportFileUpload:n.handleImportFileUpload}),e.jsx("input",{type:"file",ref:d.fileInputRef,onChange:d.handleFileChange,accept:".xlsx,.xls",className:"hidden"}),e.jsx("input",{type:"file",ref:n.importFileInputRef,onChange:n.handleImportFileChange,accept:".xlsx,.xls",className:"hidden"})]})})]})}const _t=Le;export{_t as component};
