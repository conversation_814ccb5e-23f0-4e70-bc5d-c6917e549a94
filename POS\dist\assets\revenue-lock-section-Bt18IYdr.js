import{z as e,r as p,j as t,B as x}from"./index-B283E1a3.js";import{u as D,M as L,T as O,a as q,L as N}from"./TileLayer-CLS7WM6P.js";import"./date-range-picker-B7aoXHt3.js";import{a as y,b as k,d as j,e as T}from"./form-dNL1hWKC.js";import{D as A}from"./date-picker-zVQMDD0k.js";import"./pos-api-C7RsFAun.js";import{C as P}from"./combobox-Db0SwfY4.js";const W={PAGE_TITLE_CREATE:"Tạo điểm mới",PAGE_TITLE_EDIT:"Chi tiết nhà hàng",LOADING_STORES:"Đang tải dữ liệu cửa hàng...",LOADING_CREATING:"Đang tạo...",LOADING_UPDATING:"<PERSON>ang cập nhật...",BUTTON_SAVE:"Lưu",BUTTON_ACTIVE:"Active",BUTTON_DEACTIVE:"Deactive",EXPIRED_LICENSE:"Điểm này đã hết hạn bản quyền!",EXPIRED_LICENSE_BADGE:"Hết hạn bản quyền",CITY_ALL:"all",MIN_BUTTON_WIDTH:100,ROUTE_STORE_LIST:"/setting/store",ROUTE_STORE_DETAIL:"/setting/store/detail"},Y={ACTIVE:1,ACTIVE_TEXT:"active"},R=e.object({id:e.string(),discountPercentage:e.number(),vatPercentage:e.number(),programName:e.string(),startDate:e.string(),endDate:e.string()}),J=e.object({store_name:e.string().min(1,"Tên điểm là bắt buộc"),address:e.string().min(1,"Địa chỉ là bắt buộc"),phone:e.string().min(1,"Số điện thoại là bắt buộc"),city_uid:e.string().min(1,"Vui lòng chọn thành phố"),description:e.string().optional(),sale_change_vat_enable:e.number().optional().default(1),value_vat:e.string().optional(),vat_discount_config:e.string().optional(),vat_discount_configs:e.array(R).optional().default([]),print_bill_split:e.coerce.number().optional().default(0),invoice_output:e.string().optional().default(""),net_work:e.enum(["0","1"]).optional().default("0"),latitude:e.coerce.number().min(-90).max(90).optional().default(0),longitude:e.coerce.number().min(-180).max(180).optional().default(0),email:e.string().email("Email không hợp lệ").optional().or(e.literal("")),facebook:e.string().optional(),website:e.string().optional(),logo:e.string().optional(),background:e.string().optional(),secondary_screen_image:e.string().optional(),secondary_screen_video:e.string().optional(),bank_id:e.string().optional(),bank_acc:e.string().optional(),bank_acc_name:e.string().optional(),bank_name:e.string().optional(),print_qrcode_pay:e.coerce.number().optional(),auto_check_momo_aio:e.coerce.number().optional().default(0),print_type:e.string().optional(),print_limit:e.string().optional(),group_item:e.boolean().default(!1),bill_template:e.coerce.number().optional(),prevent_cashier_edit_printer:e.boolean().default(!1),disable_print_button_in_payment_screen:e.coerce.number().optional(),is_show_logo_in_provisional_invoice:e.boolean().default(!1),report_item_combo_split:e.coerce.number().optional(),tem_invoice_fake_bill:e.boolean().default(!1),hideItemPriceAfterPrintBill:e.boolean().default(!1),hideItemPriceAfterPrintChecklist:e.boolean().default(!1),require_pin_reprint:e.boolean().default(!1),prevent_print_order_transfer:e.boolean().default(!1),allway_show_tag_so:e.boolean().default(!1),use_shift_pos:e.boolean().default(!1),counter_code:e.string().optional(),counter_mails:e.array(e.string()).optional(),sources_print:e.array(e.string()).default([]),is_ahamove_active:e.boolean(),phone_manager:e.string().optional(),ahamove_payment_method:e.string().optional(),ahamove_voucher_default:e.string().optional(),operate_model:e.coerce.number().optional(),exchange_points_for_voucher:e.boolean().default(!1),view_voucher_of_member:e.boolean().default(!1),enable_checkin_by_phone_number:e.boolean().default(!1),multi_voucher:e.boolean().default(!1),find_member:e.boolean().default(!1),is_run_buffet:e.boolean().default(!1),require_buffet_item:e.boolean().default(!1),enable_count_money:e.boolean().default(!1),disable_shift_total_amount:e.coerce.number().optional(),allow_remove_shift_open:e.boolean().default(!1),close_shift_auto_logout:e.boolean().default(!1),require_close_shift_in_day:e.boolean().default(!1),discount_reverse_on_price:e.boolean().default(!1),inv_skip_item_no_price:e.boolean().default(!1),auto_export_vat:e.boolean().default(!1),export_time_vat:e.string().optional(),require_vat_info:e.boolean().default(!1),pm_export_vat:e.boolean().default(!1),bill_auto_export_vat:e.boolean().default(!1),option_export_vat_pos_or_cms:e.coerce.number().optional(),sorted_by_print:e.boolean().default(!1),enable_cash_drawer:e.boolean().default(!1),confirm_request:e.boolean().default(!1),use_order_control:e.boolean().default(!1),enable_tab_delivery:e.boolean().default(!1),enable_note_delete_item:e.boolean().default(!1),service_charge_optional:e.boolean().default(!1),require_peo_count:e.boolean().default(!1),require_confirm_merge_table:e.boolean().default(!1),hide_peo_count:e.boolean().default(!1),enable_edit_item_price_while_selling:e.coerce.number().optional(),role_quick_login:e.string().optional(),auto_confirm_o2o_post_paid:e.boolean().default(!1),resetItemOutOfStockStatus:e.boolean().default(!1),resetItemQuantityNewDay:e.boolean().default(!1),pin_code:e.string().optional(),time_out_use_pin:e.coerce.number().optional(),open_at:e.number().min(0).max(23).default(0),tracking_sale:e.coerce.number().optional(),enable_turn_order_report:e.boolean().default(!1),change_log_detail:e.boolean().default(!1),enable_change_item_in_store:e.boolean().default(!1),enable_change_item_type_in_store:e.boolean().default(!1),enable_change_printer_position_in_store:e.boolean().default(!1),prevent_create_custom_item:e.boolean().default(!1),require_custom_item_vat:e.boolean().default(!1),require_category_for_custom_item:e.boolean().default(!1),is_menu_by_source:e.boolean().default(!1),tran_no_syn_order:e.coerce.number().optional(),enable_tran_no_prefix:e.boolean().default(!1),tran_no_prefix:e.string().optional(),reset_tran_no_period:e.string().optional(),sources_not_print:e.array(e.string()).default([]),print_order_at_checkout:e.boolean().default(!1),print_label_at_checkout:e.boolean().default(!1),sources_label_print:e.array(e.string()).default([]),print_bill_order_area:e.boolean().default(!1),allow_printer_for_invoice_by_location:e.boolean().default(!1),split_combo:e.boolean().default(!1),ignore_combo_note:e.boolean().default(!1),show_item_price_zero:e.boolean().default(!1),enable_delete_order_bill:e.boolean().default(!1),print_item_switch_table:e.boolean().default(!1),fb_store_id:e.string().optional().default(""),partner_id:e.string().optional().default(""),license_expiry:e.string().optional().default(""),license_package:e.string().optional().default(""),payment_lock_time:e.string().optional().default(""),store_id:e.string().optional(),no_kick_pda:e.boolean().optional(),device_receive_online:e.string().optional().default(""),active_devices:e.array(e.string()).default([]),time_after_lock:e.coerce.number().optional(),time_lock_data:e.coerce.number().optional(),is_delivery_direct:e.number().optional(),workstation_id:e.number().optional(),active:e.number().optional(),is_default:e.number().optional(),is_test:e.number().optional(),store_address:e.any().optional(),district:e.string().optional(),ward:e.string().optional(),pos_server_group_uid:e.string().optional(),brand_uid:e.string().optional(),company_uid:e.string().optional(),company_id:e.string().optional(),pos_type:e.string().optional(),operation_form:e.string().optional(),size:e.number().optional(),currency:e.string().optional(),delivery_services:e.string().optional(),email_delivery_service:e.string().optional(),is_fabi:e.number().optional(),partner:e.string().optional(),revision:e.number().optional(),expiry_date:e.number().optional(),sort:e.number().optional(),last_synced_transaction:e.number().optional(),created_at:e.coerce.number().optional(),source_ids_selected:e.array(e.string()).optional()}),Z=a=>{var n,u,l,i;const o={store_name:(n=a.store_name)==null?void 0:n.trim(),address:(u=a.address)==null?void 0:u.trim(),phone:(l=a.phone)==null?void 0:l.trim(),city_uid:(i=a.city_uid)==null?void 0:i.trim()};return Object.values(o).every(_=>_&&_.length>0)};function M(){return D().map}function U(a){const o=M();return p.useEffect(function(){return o.on(a),function(){o.off(a)}},[o,a]),o}delete N.Icon.Default.prototype._getIconUrl;N.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png"});function F({onLocationSelect:a}){return U({click:o=>{a(o.latlng.lat,o.latlng.lng)}}),null}function ee({defaultAddress:a,onAddressSelect:o,onCancel:n}){const u=[21.0285,105.8542],[l,i]=p.useState(u),[_,m]=p.useState(a||""),[g,b]=p.useState(!1),[G,E]=p.useState(null),h=async(r,c)=>{b(!0);try{const d=await(await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${r}&lon=${c}&accept-language=vi`)).json();d.display_name&&m(d.display_name)}catch(s){console.error("Error reverse geocoding:",s),m(`${r.toFixed(6)}, ${c.toFixed(6)}`)}finally{b(!1)}},f=async r=>{try{const s=await(await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(r)}&accept-language=vi&limit=1`)).json();if(s.length>0){const d=parseFloat(s[0].lat),I=parseFloat(s[0].lon);i([d,I]),m(s[0].display_name)}}catch(c){console.error("Error forward geocoding:",c)}},C=()=>{navigator.geolocation?navigator.geolocation.getCurrentPosition(r=>{const c=r.coords.latitude,s=r.coords.longitude,d=[c,s];E(d),i(d),h(c,s)},r=>{console.error("Error getting current location:",r),a&&f(a)}):(console.error("Geolocation is not supported by this browser"),a&&f(a))};p.useEffect(()=>{a?f(a):C()},[a]);const v=(r,c)=>{i([r,c]),h(r,c)},S=r=>{const s=r.target.getLatLng();v(s.lat,s.lng)},w=()=>{l&&_&&o(_,l[0],l[1])};return t.jsxs("div",{className:"flex h-full w-full flex-col gap-4",children:[t.jsx("div",{className:"flex-shrink-0",children:_&&t.jsxs("div",{className:"mt-2 rounded-md bg-gray-50 p-2 text-sm",children:[_,g&&t.jsx("span",{className:"ml-2 text-blue-500",children:"Đang tải..."})]})}),t.jsx("div",{className:"flex-1 overflow-hidden rounded-md",children:t.jsxs(L,{center:l,zoom:16,style:{height:"100%",width:"100%"},children:[t.jsx(O,{attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),t.jsx(q,{position:l,draggable:!0,eventHandlers:{dragend:S}}),t.jsx(F,{onLocationSelect:v})]},`${l[0]}-${l[1]}`)}),t.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-2",children:[t.jsx(x,{variant:"outline",onClick:n,children:"Hủy"}),t.jsx(x,{onClick:w,disabled:!l||!_||g,children:"Xác nhận"})]})]})}const te=e.object({discountPercentage:e.number().min(0,"Mức giảm phải lớn hơn hoặc bằng 0").max(100,"Mức giảm không được vượt quá 100%"),vatPercentage:e.number().min(0,"VAT phải lớn hơn hoặc bằng 0").max(100,"VAT không được vượt quá 100%"),programName:e.string().min(1,"Tên chương trình chiến dịch là bắt buộc"),startDate:e.string().min(1,"Ngày bắt đầu là bắt buộc"),endDate:e.string().min(1,"Ngày kết thúc là bắt buộc")}),V=[{value:"0",label:"Không khóa"},{value:"0.25",label:"6 giờ"},{value:"0.5",label:"12 giờ"},...Array.from({length:30},(a,o)=>({value:`${o+1}`,label:`${o+1} ngày`}))];function ae({form:a,isLoading:o=!1}){return p.useEffect(()=>{const n=a.getValues("time_after_lock");(n==null||Number.isNaN(n))&&a.setValue("time_after_lock",0)},[a]),t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Cấu hình khóa doanh thu"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Khi cấu hình khóa doanh thu, các hoạt động liên quan doanh thu như bán hàng sẽ bị khóa"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Mục đích: khóa chính sửa hóa đơn (hủy và sửa hóa đơn) sau khoảng thời gian setup"})]}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[t.jsx("div",{className:"col-span-3 pt-2",children:t.jsx("div",{className:"flex items-center gap-2",children:t.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Chọn số ngày tự động khóa dữ liệu"})})}),t.jsx("div",{className:"col-span-9",children:t.jsx(y,{control:a.control,name:"time_after_lock",render:({field:n})=>{var u;return t.jsxs(k,{children:[t.jsx(j,{children:t.jsx(P,{options:V,value:((u=n.value)==null?void 0:u.toString())||"0",onValueChange:l=>n.onChange(parseFloat(l)),placeholder:"Không khóa",searchPlaceholder:"Tìm kiếm...",emptyText:"Không tìm thấy tùy chọn nào.",disabled:o,className:"w-full"})}),t.jsx(T,{})]})}})})]}),t.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[t.jsx("div",{className:"col-span-3 pt-2",children:t.jsx("div",{className:"flex items-center gap-2",children:t.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Thời điểm khóa phát sinh doanh thu"})})}),t.jsx("div",{className:"col-span-9",children:t.jsx(y,{control:a.control,name:"time_lock_data",render:({field:n})=>{const l=(i=>{if(!i||i<=0)return;const _=i>1e12?i:i*1e3,m=new Date(_);return isNaN(m.getTime())?void 0:m})(n.value);return t.jsxs(k,{children:[t.jsx(j,{children:t.jsx(A,{date:l,onDateChange:i=>n.onChange(i?Math.floor(i.getTime()/1e3):0),placeholder:"Chọn ngày",disabled:o,className:"w-full"})}),t.jsx(T,{})]})}})})]})]})]})}export{ee as O,ae as R,W as S,Y as a,te as b,J as s,Z as v};
