import{C as we,r as s,Q as xe,_ as he,j as a,U as pn,V as fn,E as H,H as mn,P as N,I as F,F as M,a2 as vn,J as Mn,K as gn,W as wn,X as xn,$ as _e,A as hn,D as Me,c as A}from"./index-B283E1a3.js";import{c as _n,u as Cn}from"./index-Df0XEEuz.js";import{h as Rn,u as Dn,R as bn,F as In}from"./index-CqlrRQAb.js";import{c as Ce,I as En,R as Pn}from"./index-BhFEt02S.js";import{C as Sn}from"./check-CjIon4B5.js";var ne=["Enter"," "],yn=["ArrowDown","PageUp","Home"],Re=["ArrowUp","PageDown","End"],Nn=[...yn,...Re],An={ltr:[...ne,"ArrowRight"],rtl:[...ne,"ArrowLeft"]},Tn={ltr:["ArrowLeft"],rtl:["ArrowRight"]},G="Menu",[k,jn,On]=_n(G),[I,De]=we(G,[On,xe,Ce]),W=xe(),be=Ce(),[kn,E]=I(G),[Ln,K]=I(G),Ie=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:r,onOpenChange:c,modal:i=!0}=e,l=W(t),[m,v]=s.useState(null),p=s.useRef(!1),u=he(c),f=Cn(r);return s.useEffect(()=>{const w=()=>{p.current=!0,document.addEventListener("pointerdown",g,{capture:!0,once:!0}),document.addEventListener("pointermove",g,{capture:!0,once:!0})},g=()=>p.current=!1;return document.addEventListener("keydown",w,{capture:!0}),()=>{document.removeEventListener("keydown",w,{capture:!0}),document.removeEventListener("pointerdown",g,{capture:!0}),document.removeEventListener("pointermove",g,{capture:!0})}},[]),a.jsx(pn,{...l,children:a.jsx(kn,{scope:t,open:n,onOpenChange:u,content:m,onContentChange:v,children:a.jsx(Ln,{scope:t,onClose:s.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:p,dir:f,modal:i,children:o})})})};Ie.displayName=G;var Fn="MenuAnchor",te=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=W(n);return a.jsx(fn,{...r,...o,ref:t})});te.displayName=Fn;var oe="MenuPortal",[Gn,Ee]=I(oe,{forceMount:void 0}),Pe=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:r}=e,c=E(oe,t);return a.jsx(Gn,{scope:t,forceMount:n,children:a.jsx(H,{present:n||c.open,children:a.jsx(mn,{asChild:!0,container:r,children:o})})})};Pe.displayName=oe;var _="MenuContent",[Kn,re]=I(_),Se=s.forwardRef((e,t)=>{const n=Ee(_,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,c=E(_,e.__scopeMenu),i=K(_,e.__scopeMenu);return a.jsx(k.Provider,{scope:e.__scopeMenu,children:a.jsx(H,{present:o||c.open,children:a.jsx(k.Slot,{scope:e.__scopeMenu,children:i.modal?a.jsx($n,{...r,ref:t}):a.jsx(Un,{...r,ref:t})})})})}),$n=s.forwardRef((e,t)=>{const n=E(_,e.__scopeMenu),o=s.useRef(null),r=F(t,o);return s.useEffect(()=>{const c=o.current;if(c)return Rn(c)},[]),a.jsx(ae,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:M(e.onFocusOutside,c=>c.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Un=s.forwardRef((e,t)=>{const n=E(_,e.__scopeMenu);return a.jsx(ae,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Bn=Mn("MenuContent.ScrollLock"),ae=s.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:r,onOpenAutoFocus:c,onCloseAutoFocus:i,disableOutsidePointerEvents:l,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:p,onFocusOutside:u,onInteractOutside:f,onDismiss:w,disableOutsideScroll:g,...b}=e,P=E(_,n),T=K(_,n),$=W(n),U=be(n),de=jn(n),[an,le]=s.useState(null),B=s.useRef(null),sn=F(t,B,P.onContentChange),V=s.useRef(0),z=s.useRef(""),cn=s.useRef(0),Q=s.useRef(null),pe=s.useRef("right"),Z=s.useRef(0),un=g?bn:s.Fragment,dn=g?{as:Bn,allowPinchZoom:!0}:void 0,ln=d=>{var y,me;const h=z.current+d,C=de().filter(R=>!R.disabled),D=document.activeElement,q=(y=C.find(R=>R.ref.current===D))==null?void 0:y.textValue,ee=C.map(R=>R.textValue),fe=nt(ee,h,q),j=(me=C.find(R=>R.textValue===fe))==null?void 0:me.ref.current;(function R(ve){z.current=ve,window.clearTimeout(V.current),ve!==""&&(V.current=window.setTimeout(()=>R(""),1e3))})(h),j&&setTimeout(()=>j.focus())};s.useEffect(()=>()=>window.clearTimeout(V.current),[]),Dn();const S=s.useCallback(d=>{var C,D;return pe.current===((C=Q.current)==null?void 0:C.side)&&ot(d,(D=Q.current)==null?void 0:D.area)},[]);return a.jsx(Kn,{scope:n,searchRef:z,onItemEnter:s.useCallback(d=>{S(d)&&d.preventDefault()},[S]),onItemLeave:s.useCallback(d=>{var h;S(d)||((h=B.current)==null||h.focus(),le(null))},[S]),onTriggerLeave:s.useCallback(d=>{S(d)&&d.preventDefault()},[S]),pointerGraceTimerRef:cn,onPointerGraceIntentChange:s.useCallback(d=>{Q.current=d},[]),children:a.jsx(un,{...dn,children:a.jsx(In,{asChild:!0,trapped:r,onMountAutoFocus:M(c,d=>{var h;d.preventDefault(),(h=B.current)==null||h.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:a.jsx(gn,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:v,onPointerDownOutside:p,onFocusOutside:u,onInteractOutside:f,onDismiss:w,children:a.jsx(Pn,{asChild:!0,...U,dir:T.dir,orientation:"vertical",loop:o,currentTabStopId:an,onCurrentTabStopIdChange:le,onEntryFocus:M(m,d=>{T.isUsingKeyboardRef.current||d.preventDefault()}),preventScrollOnEntryFocus:!0,children:a.jsx(wn,{role:"menu","aria-orientation":"vertical","data-state":ze(P.open),"data-radix-menu-content":"",dir:T.dir,...$,...b,ref:sn,style:{outline:"none",...b.style},onKeyDown:M(b.onKeyDown,d=>{const C=d.target.closest("[data-radix-menu-content]")===d.currentTarget,D=d.ctrlKey||d.altKey||d.metaKey,q=d.key.length===1;C&&(d.key==="Tab"&&d.preventDefault(),!D&&q&&ln(d.key));const ee=B.current;if(d.target!==ee||!Nn.includes(d.key))return;d.preventDefault();const j=de().filter(y=>!y.disabled).map(y=>y.ref.current);Re.includes(d.key)&&j.reverse(),qn(j)}),onBlur:M(e.onBlur,d=>{d.currentTarget.contains(d.target)||(window.clearTimeout(V.current),z.current="")}),onPointerMove:M(e.onPointerMove,L(d=>{const h=d.target,C=Z.current!==d.clientX;if(d.currentTarget.contains(h)&&C){const D=d.clientX>Z.current?"right":"left";pe.current=D,Z.current=d.clientX}}))})})})})})})});Se.displayName=_;var Vn="MenuGroup",se=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return a.jsx(N.div,{role:"group",...o,ref:t})});se.displayName=Vn;var zn="MenuLabel",ye=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return a.jsx(N.div,{...o,ref:t})});ye.displayName=zn;var X="MenuItem",ge="menu.itemSelect",J=s.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...r}=e,c=s.useRef(null),i=K(X,e.__scopeMenu),l=re(X,e.__scopeMenu),m=F(t,c),v=s.useRef(!1),p=()=>{const u=c.current;if(!n&&u){const f=new CustomEvent(ge,{bubbles:!0,cancelable:!0});u.addEventListener(ge,w=>o==null?void 0:o(w),{once:!0}),vn(u,f),f.defaultPrevented?v.current=!1:i.onClose()}};return a.jsx(Ne,{...r,ref:m,disabled:n,onClick:M(e.onClick,p),onPointerDown:u=>{var f;(f=e.onPointerDown)==null||f.call(e,u),v.current=!0},onPointerUp:M(e.onPointerUp,u=>{var f;v.current||(f=u.currentTarget)==null||f.click()}),onKeyDown:M(e.onKeyDown,u=>{const f=l.searchRef.current!=="";n||f&&u.key===" "||ne.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});J.displayName=X;var Ne=s.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:r,...c}=e,i=re(X,n),l=be(n),m=s.useRef(null),v=F(t,m),[p,u]=s.useState(!1),[f,w]=s.useState("");return s.useEffect(()=>{const g=m.current;g&&w((g.textContent??"").trim())},[c.children]),a.jsx(k.ItemSlot,{scope:n,disabled:o,textValue:r??f,children:a.jsx(En,{asChild:!0,...l,focusable:!o,children:a.jsx(N.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...c,ref:v,onPointerMove:M(e.onPointerMove,L(g=>{o?i.onItemLeave(g):(i.onItemEnter(g),g.defaultPrevented||g.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:M(e.onPointerLeave,L(g=>i.onItemLeave(g))),onFocus:M(e.onFocus,()=>u(!0)),onBlur:M(e.onBlur,()=>u(!1))})})})}),Xn="MenuCheckboxItem",Ae=s.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...r}=e;return a.jsx(Le,{scope:e.__scopeMenu,checked:n,children:a.jsx(J,{role:"menuitemcheckbox","aria-checked":Y(n)?"mixed":n,...r,ref:t,"data-state":ue(n),onSelect:M(r.onSelect,()=>o==null?void 0:o(Y(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Ae.displayName=Xn;var Te="MenuRadioGroup",[Yn,Hn]=I(Te,{value:void 0,onValueChange:()=>{}}),je=s.forwardRef((e,t)=>{const{value:n,onValueChange:o,...r}=e,c=he(o);return a.jsx(Yn,{scope:e.__scopeMenu,value:n,onValueChange:c,children:a.jsx(se,{...r,ref:t})})});je.displayName=Te;var Oe="MenuRadioItem",ke=s.forwardRef((e,t)=>{const{value:n,...o}=e,r=Hn(Oe,e.__scopeMenu),c=n===r.value;return a.jsx(Le,{scope:e.__scopeMenu,checked:c,children:a.jsx(J,{role:"menuitemradio","aria-checked":c,...o,ref:t,"data-state":ue(c),onSelect:M(o.onSelect,()=>{var i;return(i=r.onValueChange)==null?void 0:i.call(r,n)},{checkForDefaultPrevented:!1})})})});ke.displayName=Oe;var ce="MenuItemIndicator",[Le,Wn]=I(ce,{checked:!1}),Fe=s.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...r}=e,c=Wn(ce,n);return a.jsx(H,{present:o||Y(c.checked)||c.checked===!0,children:a.jsx(N.span,{...r,ref:t,"data-state":ue(c.checked)})})});Fe.displayName=ce;var Jn="MenuSeparator",Ge=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return a.jsx(N.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});Ge.displayName=Jn;var Qn="MenuArrow",Ke=s.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=W(n);return a.jsx(xn,{...r,...o,ref:t})});Ke.displayName=Qn;var Zn="MenuSub",[qt,$e]=I(Zn),O="MenuSubTrigger",Ue=s.forwardRef((e,t)=>{const n=E(O,e.__scopeMenu),o=K(O,e.__scopeMenu),r=$e(O,e.__scopeMenu),c=re(O,e.__scopeMenu),i=s.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:m}=c,v={__scopeMenu:e.__scopeMenu},p=s.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return s.useEffect(()=>p,[p]),s.useEffect(()=>{const u=l.current;return()=>{window.clearTimeout(u),m(null)}},[l,m]),a.jsx(te,{asChild:!0,...v,children:a.jsx(Ne,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":ze(n.open),...e,ref:_e(t,r.onTriggerChange),onClick:u=>{var f;(f=e.onClick)==null||f.call(e,u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:M(e.onPointerMove,L(u=>{c.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(c.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:M(e.onPointerLeave,L(u=>{var w,g;p();const f=(w=n.content)==null?void 0:w.getBoundingClientRect();if(f){const b=(g=n.content)==null?void 0:g.dataset.side,P=b==="right",T=P?-5:5,$=f[P?"left":"right"],U=f[P?"right":"left"];c.onPointerGraceIntentChange({area:[{x:u.clientX+T,y:u.clientY},{x:$,y:f.top},{x:U,y:f.top},{x:U,y:f.bottom},{x:$,y:f.bottom}],side:b}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(u),u.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:M(e.onKeyDown,u=>{var w;const f=c.searchRef.current!=="";e.disabled||f&&u.key===" "||An[o.dir].includes(u.key)&&(n.onOpenChange(!0),(w=n.content)==null||w.focus(),u.preventDefault())})})})});Ue.displayName=O;var Be="MenuSubContent",Ve=s.forwardRef((e,t)=>{const n=Ee(_,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,c=E(_,e.__scopeMenu),i=K(_,e.__scopeMenu),l=$e(Be,e.__scopeMenu),m=s.useRef(null),v=F(t,m);return a.jsx(k.Provider,{scope:e.__scopeMenu,children:a.jsx(H,{present:o||c.open,children:a.jsx(k.Slot,{scope:e.__scopeMenu,children:a.jsx(ae,{id:l.contentId,"aria-labelledby":l.triggerId,...r,ref:v,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:p=>{var u;i.isUsingKeyboardRef.current&&((u=m.current)==null||u.focus()),p.preventDefault()},onCloseAutoFocus:p=>p.preventDefault(),onFocusOutside:M(e.onFocusOutside,p=>{p.target!==l.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:M(e.onEscapeKeyDown,p=>{i.onClose(),p.preventDefault()}),onKeyDown:M(e.onKeyDown,p=>{var w;const u=p.currentTarget.contains(p.target),f=Tn[i.dir].includes(p.key);u&&f&&(c.onOpenChange(!1),(w=l.trigger)==null||w.focus(),p.preventDefault())})})})})})});Ve.displayName=Be;function ze(e){return e?"open":"closed"}function Y(e){return e==="indeterminate"}function ue(e){return Y(e)?"indeterminate":e?"checked":"unchecked"}function qn(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function et(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function nt(e,t,n){const r=t.length>1&&Array.from(t).every(v=>v===t[0])?t[0]:t,c=n?e.indexOf(n):-1;let i=et(e,Math.max(c,0));r.length===1&&(i=i.filter(v=>v!==n));const m=i.find(v=>v.toLowerCase().startsWith(r.toLowerCase()));return m!==n?m:void 0}function tt(e,t){const{x:n,y:o}=e;let r=!1;for(let c=0,i=t.length-1;c<t.length;i=c++){const l=t[c].x,m=t[c].y,v=t[i].x,p=t[i].y;m>o!=p>o&&n<(v-l)*(o-m)/(p-m)+l&&(r=!r)}return r}function ot(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return tt(n,t)}function L(e){return t=>t.pointerType==="mouse"?e(t):void 0}var rt=Ie,at=te,st=Pe,ct=Se,ut=se,it=ye,dt=J,lt=Ae,pt=je,ft=ke,mt=Fe,vt=Ge,Mt=Ke,gt=Ue,wt=Ve,ie="DropdownMenu",[xt,eo]=we(ie,[De]),x=De(),[ht,Xe]=xt(ie),Ye=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:r,defaultOpen:c,onOpenChange:i,modal:l=!0}=e,m=x(t),v=s.useRef(null),[p=!1,u]=hn({prop:r,defaultProp:c,onChange:i});return a.jsx(ht,{scope:t,triggerId:Me(),triggerRef:v,contentId:Me(),open:p,onOpenChange:u,onOpenToggle:s.useCallback(()=>u(f=>!f),[u]),modal:l,children:a.jsx(rt,{...m,open:p,onOpenChange:u,dir:o,modal:l,children:n})})};Ye.displayName=ie;var He="DropdownMenuTrigger",We=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...r}=e,c=Xe(He,n),i=x(n);return a.jsx(at,{asChild:!0,...i,children:a.jsx(N.button,{type:"button",id:c.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":c.open?c.contentId:void 0,"data-state":c.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:_e(t,c.triggerRef),onPointerDown:M(e.onPointerDown,l=>{!o&&l.button===0&&l.ctrlKey===!1&&(c.onOpenToggle(),c.open||l.preventDefault())}),onKeyDown:M(e.onKeyDown,l=>{o||(["Enter"," "].includes(l.key)&&c.onOpenToggle(),l.key==="ArrowDown"&&c.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())})})})});We.displayName=He;var _t="DropdownMenuPortal",Je=e=>{const{__scopeDropdownMenu:t,...n}=e,o=x(t);return a.jsx(st,{...o,...n})};Je.displayName=_t;var Qe="DropdownMenuContent",Ze=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=Xe(Qe,n),c=x(n),i=s.useRef(!1);return a.jsx(ct,{id:r.contentId,"aria-labelledby":r.triggerId,...c,...o,ref:t,onCloseAutoFocus:M(e.onCloseAutoFocus,l=>{var m;i.current||(m=r.triggerRef.current)==null||m.focus(),i.current=!1,l.preventDefault()}),onInteractOutside:M(e.onInteractOutside,l=>{const m=l.detail.originalEvent,v=m.button===0&&m.ctrlKey===!0,p=m.button===2||v;(!r.modal||p)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ze.displayName=Qe;var Ct="DropdownMenuGroup",qe=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(ut,{...r,...o,ref:t})});qe.displayName=Ct;var Rt="DropdownMenuLabel",en=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(it,{...r,...o,ref:t})});en.displayName=Rt;var Dt="DropdownMenuItem",nn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(dt,{...r,...o,ref:t})});nn.displayName=Dt;var bt="DropdownMenuCheckboxItem",tn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(lt,{...r,...o,ref:t})});tn.displayName=bt;var It="DropdownMenuRadioGroup",Et=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(pt,{...r,...o,ref:t})});Et.displayName=It;var Pt="DropdownMenuRadioItem",St=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(ft,{...r,...o,ref:t})});St.displayName=Pt;var yt="DropdownMenuItemIndicator",on=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(mt,{...r,...o,ref:t})});on.displayName=yt;var Nt="DropdownMenuSeparator",rn=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(vt,{...r,...o,ref:t})});rn.displayName=Nt;var At="DropdownMenuArrow",Tt=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(Mt,{...r,...o,ref:t})});Tt.displayName=At;var jt="DropdownMenuSubTrigger",Ot=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(gt,{...r,...o,ref:t})});Ot.displayName=jt;var kt="DropdownMenuSubContent",Lt=s.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=x(n);return a.jsx(wt,{...r,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Lt.displayName=kt;var Ft=Ye,Gt=We,Kt=Je,$t=Ze,Ut=qe,Bt=en,Vt=nn,zt=tn,Xt=on,Yt=rn;function no({...e}){return a.jsx(Ft,{"data-slot":"dropdown-menu",...e})}function to({...e}){return a.jsx(Gt,{"data-slot":"dropdown-menu-trigger",...e})}function oo({className:e,sideOffset:t=4,...n}){return a.jsx(Kt,{children:a.jsx($t,{"data-slot":"dropdown-menu-content",sideOffset:t,className:A("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n})})}function ro({...e}){return a.jsx(Ut,{"data-slot":"dropdown-menu-group",...e})}function ao({className:e,inset:t,variant:n="default",...o}){return a.jsx(Vt,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:A("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o})}function so({className:e,children:t,checked:n,...o}){return a.jsxs(zt,{"data-slot":"dropdown-menu-checkbox-item",className:A("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:n,...o,children:[a.jsx("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:a.jsx(Xt,{children:a.jsx(Sn,{className:"size-4"})})}),t]})}function co({className:e,inset:t,...n}){return a.jsx(Bt,{"data-slot":"dropdown-menu-label","data-inset":t,className:A("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function uo({className:e,...t}){return a.jsx(Yt,{"data-slot":"dropdown-menu-separator",className:A("bg-border -mx-1 my-1 h-px",e),...t})}function io({className:e,...t}){return a.jsx("span",{"data-slot":"dropdown-menu-shortcut",className:A("text-muted-foreground ml-auto text-xs tracking-widest",e),...t})}export{no as D,to as a,oo as b,ao as c,co as d,uo as e,ro as f,io as g,We as h,so as i};
