import{r as c,I as T,A as _,j as a,C as I,P as g,F as H,af as M,c as x}from"./index-B283E1a3.js";import{u as z}from"./index-DY0KH0l4.js";var v="Switch",[A,W]=I(v),[B,q]=A(v),S=c.forwardRef((e,r)=>{const{__scopeSwitch:t,name:s,checked:o,defaultChecked:l,required:i,disabled:n,value:d="on",onCheckedChange:f,form:k,...p}=e,[u,j]=c.useState(null),E=T(r,b=>j(b)),m=c.useRef(!1),w=u?k||!!u.closest("form"):!0,[h=!1,R]=_({prop:o,defaultProp:l,onChange:f});return a.jsxs(B,{scope:t,checked:h,disabled:n,children:[a.jsx(g.button,{type:"button",role:"switch","aria-checked":h,"aria-required":i,"data-state":y(h),"data-disabled":n?"":void 0,disabled:n,value:d,...p,ref:E,onClick:H(e.onClick,b=>{R(N=>!N),w&&(m.current=b.isPropagationStopped(),m.current||b.stopPropagation())})}),w&&a.jsx(F,{control:u,bubbles:!m.current,name:s,value:d,checked:h,required:i,disabled:n,form:k,style:{transform:"translateX(-100%)"}})]})});S.displayName=v;var C="SwitchThumb",P=c.forwardRef((e,r)=>{const{__scopeSwitch:t,...s}=e,o=q(C,t);return a.jsx(g.span,{"data-state":y(o.checked),"data-disabled":o.disabled?"":void 0,...s,ref:r})});P.displayName=C;var F=e=>{const{control:r,checked:t,bubbles:s=!0,...o}=e,l=c.useRef(null),i=z(t),n=M(r);return c.useEffect(()=>{const d=l.current,f=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(f,"checked").set;if(i!==t&&p){const u=new Event("click",{bubbles:s});p.call(d,t),d.dispatchEvent(u)}},[i,t,s]),a.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...o,tabIndex:-1,ref:l,style:{...e.style,...n,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function y(e){return e?"checked":"unchecked"}var O=S,D=P;function X({className:e,...r}){return a.jsx(O,{"data-slot":"switch",className:x("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:a.jsx(D,{"data-slot":"switch-thumb",className:x("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}export{X as S};
