import{u as h}from"./useQuery-Cc4LgMzN.js";import{a3 as I}from"./index-B283E1a3.js";import{u as f}from"./useMutation-Bf5OzDko.js";import{e as u}from"./vietqr-api-BbJFOv9v.js";import{utils as l,write as b}from"./xlsx-DkH2s96g.js";import{a as p}from"./pos-api-C7RsFAun.js";import{Q as y}from"./query-keys-3lmd-xp6.js";const S=async(t={})=>{var c;const e=localStorage.getItem("pos_user_data"),s=localStorage.getItem("pos_brands_data");let r="",o="";if(e)try{r=JSON.parse(e).company_uid||""}catch{}if(s)try{const a=JSON.parse(s);Array.isArray(a)&&a.length>0&&(o=a[0].id||"")}catch{}if(!r||!o)throw new Error("Company or brand UID not found in localStorage");const i=new URLSearchParams({company_uid:r,brand_uid:o,page:(t.page||1).toString()});t.searchTerm&&i.append("search",t.searchTerm);const d=`/mdata/v1/item-types?${i.toString()}`,n=await p.get(d);return(c=n.data)!=null&&c.data?n.data.data.map(m=>u(m)):[]},w=async t=>{const e=localStorage.getItem("pos_user_data"),s=localStorage.getItem("pos_brands_data");let r="",o="";if(e)try{r=JSON.parse(e).company_uid||""}catch{}if(s)try{const a=JSON.parse(s);Array.isArray(a)&&a.length>0&&(o=a[0].id||"")}catch{}if(!r||!o)throw new Error("Company or brand UID not found in localStorage");const i=()=>{const a="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";let m="";for(let g=0;g<4;g++)m+=a.charAt(Math.floor(Math.random()*a.length));return`ITEM_TYPE-${m}`},d={item_type_name:t.name,company_uid:r,brand_uid:o,item_type_id:t.code&&t.code.trim()?t.code:i(),...t.sort!==void 0&&{sort:t.sort},...t.selectedItems&&t.selectedItems.length>0&&{list_item:t.selectedItems},...t.parentId&&{item_type_parent_id:t.parentId},...t.store_uid&&{store_uid:t.store_uid}};return(await p.post("/mdata/v1/item-type",d)).data.data},D=async t=>{const e=localStorage.getItem("pos_user_data"),s=localStorage.getItem("pos_brands_data");let r="",o="";if(e)try{r=JSON.parse(e).company_uid||""}catch{}if(s)try{const n=JSON.parse(s);Array.isArray(n)&&n.length>0&&(o=n[0].id||"")}catch{}if(!r||!o)throw new Error("Company or brand UID not found in localStorage");const i=t.map((n,c)=>({company_uid:r,brand_uid:o,item_type_name:n.item_type_name,item_type_id:n.item_type_id,sort:n.sort||c+1}));await p.post("/mdata/v1/item-types",i)},U=async(t,e)=>{const s=localStorage.getItem("pos_user_data"),r=localStorage.getItem("pos_brands_data");let o="",i="";if(s)try{o=JSON.parse(s).company_uid||""}catch{}if(r)try{const c=JSON.parse(r);Array.isArray(c)&&c.length>0&&(i=c[0].id||"")}catch{}if(!o||!i)throw new Error("Company or brand UID not found in localStorage");const d={company_uid:o,brand_uid:i,...e.name&&{item_type_name:e.name},...e.parentId&&{item_type_parent_id:e.parentId},...e.store_uid&&{store_uid:e.store_uid}},n=`/mdata/v1/item-types/${t}`;await p.put(n,d)},C=async t=>{const e={...t,list_item:[]};await p.put("/mdata/v1/item-type",e)},E=async t=>{const e=localStorage.getItem("pos_user_data"),s=localStorage.getItem("pos_brands_data");let r="",o="";if(e)try{r=JSON.parse(e).company_uid||""}catch{}if(s)try{const n=JSON.parse(s);Array.isArray(n)&&n.length>0&&(o=n[0].id||"")}catch{}if(!r||!o)throw new Error("Company or brand UID not found in localStorage");const d=`/mdata/v1/item-type?${new URLSearchParams({company_uid:r,brand_uid:o,id:t}).toString()}`;await p.delete(d)},A=async t=>{const e=new FormData;e.append("file",t),await p.post("/mdata/v1/item-types/import",e)},v=async t=>{const e=await p.get("/mdata/v1/item-type",{params:{id:t}});return u(e.data.data)},T=async(t={})=>{var c;const e=localStorage.getItem("pos_user_data"),s=localStorage.getItem("pos_brands_data");let r="",o="";if(e)try{r=JSON.parse(e).company_uid||""}catch{}if(s)try{const a=JSON.parse(s);Array.isArray(a)&&a.length>0&&(o=a[0].id||"")}catch{}if(!r||!o)throw new Error("Company or brand UID not found in localStorage");const i=new URLSearchParams({skip_limit:"true",company_uid:r,brand_uid:o});t.store_uid&&i.append("store_uid",t.store_uid),t.apply_with_store&&i.append("apply_with_store","1");const d=`/mdata/v1/item-types?${i.toString()}`,n=await p.get(d);if((c=n.data)!=null&&c.data){const a=n.data.data.map(m=>u(m));return B(a)}else throw new Error("No categories data received from API")},B=t=>{const e=[["Báo cáo nhóm món"],["#","Mã nhóm","Tên nhóm","Thứ tự","Trạng thái"],...t.map((a,m)=>[m+1,a.item_type_id,a.item_type_name,a.sort||0,a.active===1?"Active":"Deactive"])],s=l.book_new(),r=l.aoa_to_sheet(e),o=[{wch:5},{wch:20},{wch:30},{wch:10},{wch:15}];r["!cols"]=o,r["!merges"]=[{s:{r:0,c:0},e:{r:0,c:4}}],r.A1&&(r.A1.s={font:{bold:!0,sz:14},alignment:{horizontal:"center",vertical:"center"}}),["A2","B2","C2","D2","E2"].forEach(a=>{r[a]&&(r[a].s={font:{bold:!0},alignment:{horizontal:"center",vertical:"center"},fill:{fgColor:{rgb:"E6E6E6"}}})}),l.book_append_sheet(s,r,"Báo cáo nhóm món");const d=b(s,{bookType:"xlsx",type:"binary"}),n=new ArrayBuffer(d.length),c=new Uint8Array(n);for(let a=0;a<d.length;++a)c[a]=d.charCodeAt(a)&255;return new Blob([n],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})},_={getItemCategories:S,getItemCategoryById:v,createItemCategory:w,bulkCreateItemCategories:D,updateItemCategory:U,updateItemCategoryStatus:C,deleteItemCategory:E,importCategories:A,exportCategories:T};function M(t,e=!0){return h({queryKey:[y.ITEM_CATEGORIES,"single",t],queryFn:()=>_.getItemCategoryById(t),enabled:e&&!!t,staleTime:5*60*1e3,gcTime:10*60*1e3})}function Q(){const t=I();return f({mutationFn:e=>_.createItemCategory(e),onSuccess:()=>{t.invalidateQueries({queryKey:[y.ITEM_CATEGORIES]}),t.invalidateQueries({queryKey:[y.ITEM_TYPES]})}})}function R(){return f({mutationFn:t=>_.exportCategories(t)})}export{Q as a,M as b,R as u};
