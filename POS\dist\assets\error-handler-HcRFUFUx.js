import{u as w}from"./useQuery-Cc4LgMzN.js";import{a3 as g,u as S,l as b}from"./index-B283E1a3.js";import{u as _}from"./useMutation-Bf5OzDko.js";import{d as p}from"./vietqr-api-BbJFOv9v.js";import{a as u}from"./pos-api-C7RsFAun.js";import{Q as c}from"./query-keys-3lmd-xp6.js";const E=async(e={})=>{var d;const t=localStorage.getItem("pos_user_data"),a=localStorage.getItem("pos_brands_data");let s="",r="";if(t)try{s=JSON.parse(t).company_uid||""}catch{}if(a)try{const n=JSON.parse(a);Array.isArray(n)&&n.length>0&&(r=n[0].id||"")}catch{}if(!s||!r)throw new Error("Company or brand UID not found in localStorage");const o=new URLSearchParams({company_uid:s,brand_uid:r});e.skip_limit?o.append("skip_limit","true"):o.append("page",(e.page||1).toString()),e.searchTerm&&o.append("search",e.searchTerm),e.storeId&&o.append("store_uid",e.storeId);const i=await u.get(`/mdata/v1/channels?${o.toString()}`);return(d=i.data)!=null&&d.data?i.data.data.map(n=>p(n)):[]},A=async e=>{var n,l,f,m,h,C;let t=localStorage.getItem("auth-storage");if(!t&&(t=localStorage.getItem("auth")||localStorage.getItem("authData")||localStorage.getItem("user"),!t))throw new Error("Authentication data not found");const{state:a}=JSON.parse(t),s=(l=(n=a==null?void 0:a.auth)==null?void 0:n.company)==null?void 0:l.id,r=(h=(m=(f=a==null?void 0:a.auth)==null?void 0:f.brands)==null?void 0:m[0])==null?void 0:h.id;if(!s||!r)throw new Error("Company or brand UID not found in localStorage");const i=`/mdata/v1/channel?${new URLSearchParams({company_uid:s,brand_uid:r,id:e}).toString()}`,d=await u.get(i);if((C=d.data)!=null&&C.data)return p(d.data.data);throw new Error("Channel not found")},U=async e=>{var t,a,s;try{const r=!Array.isArray(e)&&"id"in e;let o,i;r?(o=e,i="/mdata/v1/channel",console.log("Updating channel with payload:",o)):(o=Array.isArray(e)?e:[e],i="/mdata/v1/channels",console.log("Creating channels with payload:",o));const d=await u.post(i,o);return console.log("API Response status:",d.status),console.log("API Response data:",d.data),console.log(r?"Channel updated successfully!":"Channel created successfully!"),[]}catch(r){throw console.error("API Error details:",{message:r.message,status:(t=r.response)==null?void 0:t.status,statusText:(a=r.response)==null?void 0:a.statusText,data:(s=r.response)==null?void 0:s.data,config:r.config}),new Error(`Failed to create channel: ${r.message}`)}},D=async e=>{const t={id:e.id,created_at:e.created_at,created_by:e.created_by,updated_at:e.updated_at,updated_by:e.updated_by,deleted:e.deleted,deleted_at:e.deleted_at,deleted_by:e.deleted_by,source_id:e.source_id,source_name:e.source_name,source_type:e.source_type,description:e.description,extra_data:e.extra_data,is_fb:e.is_fb,active:e.active,revision:e.revision,brand_uid:e.brand_uid,company_uid:e.company_uid,sort:e.sort,is_fabi:e.is_fabi,store_uid:e.store_uid,partner_config:e.partner_config,stores:e.stores},a=await u.put("/mdata/v1/channel",t);if(a.data)return p(a.data);throw new Error("Failed to update channel")},v=async e=>{const t=localStorage.getItem("pos_user_data"),a=localStorage.getItem("pos_brands_data");let s="",r="";if(t)try{s=JSON.parse(t).company_uid||""}catch{}if(a)try{const i=JSON.parse(a);Array.isArray(i)&&i.length>0&&(r=i[0].id||"")}catch{}if(!s||!r)throw new Error("Company or brand UID not found in localStorage");const o=new URLSearchParams({company_uid:s,brand_uid:r,id:e});await u.delete(`/mdata/v1/channel?${o.toString()}`)},P=async(e,t,a)=>{const{channels:s,targetStoreIds:r}=e,o=r.map(async i=>{const d=s.map(n=>({source_id:n.source_id,source_name:n.source_name,source_type:n.source_type,description:n.description,extra_data:n.extra_data,active:n.active,sort:n.sort,is_fabi:n.is_fabi,company_uid:t,brand_uid:a,store_uid:i}));return u.post("/mdata/v1/channels",d)});await Promise.all(o)},y={getChannels:E,getChannelById:A,createChannel:U,updateChannel:D,deleteChannel:v,copyChannels:P};function F(e={}){const{enabled:t=!0,...a}=e,s={skip_limit:!0,...a};return w({queryKey:[c.CHANNELS,s],queryFn:()=>y.getChannels(s),enabled:t,staleTime:5*60*1e3,gcTime:10*60*1e3})}function M(e){const{company:t}=S(s=>s.auth),{selectedBrand:a}=b();return w({queryKey:[c.CHANNELS,"detail",e,t==null?void 0:t.id,a==null?void 0:a.id],queryFn:async()=>{var i;if(!(t!=null&&t.id)||!(a!=null&&a.id)||!e)throw new Error("Missing required auth data or channelId");const r=`/mdata/v1/channel?${new URLSearchParams({company_uid:t.id,brand_uid:a.id,id:e}).toString()}`,o=await u.get(r);if((i=o.data)!=null&&i.data)return p(o.data.data);throw new Error("Channel not found")},enabled:!!(e&&(t!=null&&t.id)&&(a!=null&&a.id)),staleTime:5*60*1e3,gcTime:10*60*1e3})}function Q(){const e=g();return _({mutationFn:y.createChannel,onSuccess:()=>{e.invalidateQueries({queryKey:[c.CHANNELS]})}})}function R(){const e=g();return _({mutationFn:y.updateChannel,onSuccess:()=>{e.invalidateQueries({queryKey:[c.CHANNELS]})}})}function k(e){const t=g(),{company:a,brands:s}=S(o=>o.auth),r=s==null?void 0:s[0];return _({mutationFn:async({channelIds:o,targetStoreIds:i,channels:d})=>{if(!(a!=null&&a.id)||!(r!=null&&r.id))throw new Error("Missing authentication data");const n=d.filter(l=>o.includes(l.id));if(n.length===0)throw new Error("No channels selected");if(i.length===0)throw new Error("No target stores selected");return y.copyChannels({channels:n,targetStoreIds:i},a.id,r.id)},onSuccess:()=>{t.invalidateQueries({queryKey:[c.CHANNELS]}),e!=null&&e.onSuccess&&e.onSuccess()},onError:o=>{e!=null&&e.onError&&o instanceof Error&&e.onError(o)}})}const K=e=>{var a,s;const t=e;if((a=t==null?void 0:t.response)!=null&&a.data&&typeof t.response.data=="object"){const r=t.response.data;if((s=r.error)!=null&&s.message)return r.error.message;if("message"in r&&typeof r.message=="string"&&r.message)return r.message}return t!=null&&t.message?t.message:"Đã xảy ra lỗi không xác định"},H=e=>{var a;const t=e;return(a=t==null?void 0:t.response)!=null&&a.data&&typeof t.response.data=="object"&&t.response.data.track_id||null};export{H as a,k as b,y as c,Q as d,R as e,M as f,K as g,F as u};
