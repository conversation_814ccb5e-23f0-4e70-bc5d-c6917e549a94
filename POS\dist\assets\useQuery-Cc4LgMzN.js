var lt=e=>{throw TypeError(e)};var J=(e,t,i)=>t.has(e)||lt("Cannot "+i);var s=(e,t,i)=>(J(e,t,"read from private field"),i?i.call(e):t.get(e)),b=(e,t,i)=>t.has(e)?lt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,i),c=(e,t,i,r)=>(J(e,t,"write to private field"),r?r.call(e,i):t.set(e,i),i),d=(e,t,i)=>(J(e,t,"access private method"),i);import{a6 as St,ag as dt,ah as O,a7 as X,ai as K,aj as Et,ak as Y,al as ft,am as Qt,an as It,ao as wt,ap as pt,aa as vt,r as S,a3 as xt}from"./index-B283E1a3.js";import{s as Tt,n as bt}from"./utils-km2FGkQ4.js";var y,a,H,g,x,D,E,Q,z,M,P,T,F,I,_,n,A,Z,tt,et,st,it,rt,at,Ct,Rt,Ft=(Rt=class extends St{constructor(t,i){super();b(this,n);b(this,y);b(this,a);b(this,H);b(this,g);b(this,x);b(this,D);b(this,E);b(this,Q);b(this,z);b(this,M);b(this,P);b(this,T);b(this,F);b(this,I);b(this,_,new Set);this.options=i,c(this,y,t),c(this,Q,null),c(this,E,dt()),this.options.experimental_prefetchInRender||s(this,E).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(i)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(s(this,a).addObserver(this),gt(s(this,a),this.options)?d(this,n,A).call(this):this.updateResult(),d(this,n,st).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return nt(s(this,a),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return nt(s(this,a),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,d(this,n,it).call(this),d(this,n,rt).call(this),s(this,a).removeObserver(this)}setOptions(t){const i=this.options,r=s(this,a);if(this.options=s(this,y).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof O(this.options.enabled,s(this,a))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");d(this,n,at).call(this),s(this,a).setOptions(this.options),i._defaulted&&!X(this.options,i)&&s(this,y).getQueryCache().notify({type:"observerOptionsUpdated",query:s(this,a),observer:this});const l=this.hasListeners();l&&yt(s(this,a),r,this.options,i)&&d(this,n,A).call(this),this.updateResult(),l&&(s(this,a)!==r||O(this.options.enabled,s(this,a))!==O(i.enabled,s(this,a))||K(this.options.staleTime,s(this,a))!==K(i.staleTime,s(this,a)))&&d(this,n,Z).call(this);const o=d(this,n,tt).call(this);l&&(s(this,a)!==r||O(this.options.enabled,s(this,a))!==O(i.enabled,s(this,a))||o!==s(this,I))&&d(this,n,et).call(this,o)}getOptimisticResult(t){const i=s(this,y).getQueryCache().build(s(this,y),t),r=this.createResult(i,t);return Dt(this,r)&&(c(this,g,r),c(this,D,this.options),c(this,x,s(this,a).state)),r}getCurrentResult(){return s(this,g)}trackResult(t,i){const r={};return Object.keys(t).forEach(l=>{Object.defineProperty(r,l,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(l),i==null||i(l),t[l])})}),r}trackProp(t){s(this,_).add(t)}getCurrentQuery(){return s(this,a)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const i=s(this,y).defaultQueryOptions(t),r=s(this,y).getQueryCache().build(s(this,y),i);return r.fetch().then(()=>this.createResult(r,i))}fetch(t){return d(this,n,A).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),s(this,g)))}createResult(t,i){var ut;const r=s(this,a),l=this.options,o=s(this,g),h=s(this,x),w=s(this,D),v=t!==r?t.state:s(this,H),{state:L}=t;let u={...L},k=!1,f;if(i._optimisticResults){const R=this.hasListeners(),W=!R&&gt(t,i),U=R&&yt(t,r,i,l);(W||U)&&(u={...u,...wt(L.data,t.options)}),i._optimisticResults==="isRestoring"&&(u.fetchStatus="idle")}let{error:j,errorUpdatedAt:B,status:m}=u;f=u.data;let V=!1;if(i.placeholderData!==void 0&&f===void 0&&m==="pending"){let R;o!=null&&o.isPlaceholderData&&i.placeholderData===(w==null?void 0:w.placeholderData)?(R=o.data,V=!0):R=typeof i.placeholderData=="function"?i.placeholderData((ut=s(this,P))==null?void 0:ut.state.data,s(this,P)):i.placeholderData,R!==void 0&&(m="success",f=pt(o==null?void 0:o.data,R,i),k=!0)}if(i.select&&f!==void 0&&!V)if(o&&f===(h==null?void 0:h.data)&&i.select===s(this,z))f=s(this,M);else try{c(this,z,i.select),f=i.select(f),f=pt(o==null?void 0:o.data,f,i),c(this,M,f),c(this,Q,null)}catch(R){c(this,Q,R)}s(this,Q)&&(j=s(this,Q),f=s(this,M),B=Date.now(),m="error");const $=u.fetchStatus==="fetching",q=m==="pending",G=m==="error",ot=q&&$,ct=f!==void 0,C={status:m,fetchStatus:u.fetchStatus,isPending:q,isSuccess:m==="success",isError:G,isInitialLoading:ot,isLoading:ot,data:f,dataUpdatedAt:u.dataUpdatedAt,error:j,errorUpdatedAt:B,failureCount:u.fetchFailureCount,failureReason:u.fetchFailureReason,errorUpdateCount:u.errorUpdateCount,isFetched:u.dataUpdateCount>0||u.errorUpdateCount>0,isFetchedAfterMount:u.dataUpdateCount>v.dataUpdateCount||u.errorUpdateCount>v.errorUpdateCount,isFetching:$,isRefetching:$&&!q,isLoadingError:G&&!ct,isPaused:u.fetchStatus==="paused",isPlaceholderData:k,isRefetchError:G&&ct,isStale:ht(t,i),refetch:this.refetch,promise:s(this,E)};if(this.options.experimental_prefetchInRender){const R=N=>{C.status==="error"?N.reject(C.error):C.data!==void 0&&N.resolve(C.data)},W=()=>{const N=c(this,E,C.promise=dt());R(N)},U=s(this,E);switch(U.status){case"pending":t.queryHash===r.queryHash&&R(U);break;case"fulfilled":(C.status==="error"||C.data!==U.value)&&W();break;case"rejected":(C.status!=="error"||C.error!==U.reason)&&W();break}}return C}updateResult(){const t=s(this,g),i=this.createResult(s(this,a),this.options);if(c(this,x,s(this,a).state),c(this,D,this.options),s(this,x).data!==void 0&&c(this,P,s(this,a)),X(i,t))return;c(this,g,i);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:l}=this.options,o=typeof l=="function"?l():l;if(o==="all"||!o&&!s(this,_).size)return!0;const h=new Set(o??s(this,_));return this.options.throwOnError&&h.add("error"),Object.keys(s(this,g)).some(w=>{const p=w;return s(this,g)[p]!==t[p]&&h.has(p)})};d(this,n,Ct).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&d(this,n,st).call(this)}},y=new WeakMap,a=new WeakMap,H=new WeakMap,g=new WeakMap,x=new WeakMap,D=new WeakMap,E=new WeakMap,Q=new WeakMap,z=new WeakMap,M=new WeakMap,P=new WeakMap,T=new WeakMap,F=new WeakMap,I=new WeakMap,_=new WeakMap,n=new WeakSet,A=function(t){d(this,n,at).call(this);let i=s(this,a).fetch(this.options,t);return t!=null&&t.throwOnError||(i=i.catch(Et)),i},Z=function(){d(this,n,it).call(this);const t=K(this.options.staleTime,s(this,a));if(Y||s(this,g).isStale||!ft(t))return;const r=Qt(s(this,g).dataUpdatedAt,t)+1;c(this,T,setTimeout(()=>{s(this,g).isStale||this.updateResult()},r))},tt=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(s(this,a)):this.options.refetchInterval)??!1},et=function(t){d(this,n,rt).call(this),c(this,I,t),!(Y||O(this.options.enabled,s(this,a))===!1||!ft(s(this,I))||s(this,I)===0)&&c(this,F,setInterval(()=>{(this.options.refetchIntervalInBackground||It.isFocused())&&d(this,n,A).call(this)},s(this,I)))},st=function(){d(this,n,Z).call(this),d(this,n,et).call(this,d(this,n,tt).call(this))},it=function(){s(this,T)&&(clearTimeout(s(this,T)),c(this,T,void 0))},rt=function(){s(this,F)&&(clearInterval(s(this,F)),c(this,F,void 0))},at=function(){const t=s(this,y).getQueryCache().build(s(this,y),this.options);if(t===s(this,a))return;const i=s(this,a);c(this,a,t),c(this,H,t.state),this.hasListeners()&&(i==null||i.removeObserver(this),t.addObserver(this))},Ct=function(t){vt.batch(()=>{t.listeners&&this.listeners.forEach(i=>{i(s(this,g))}),s(this,y).getQueryCache().notify({query:s(this,a),type:"observerResultsUpdated"})})},Rt);function Ut(e,t){return O(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function gt(e,t){return Ut(e,t)||e.state.data!==void 0&&nt(e,t,t.refetchOnMount)}function nt(e,t,i){if(O(t.enabled,e)!==!1){const r=typeof i=="function"?i(e):i;return r==="always"||r!==!1&&ht(e,t)}return!1}function yt(e,t,i,r){return(e!==t||O(r.enabled,e)===!1)&&(!i.suspense||e.state.status!=="error")&&ht(e,i)}function ht(e,t){return O(t.enabled,e)!==!1&&e.isStaleByTime(K(t.staleTime,e))}function Dt(e,t){return!X(e.getCurrentResult(),t)}var Ot=S.createContext(!1),Mt=()=>S.useContext(Ot);Ot.Provider;function Pt(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var _t=S.createContext(Pt()),Lt=()=>S.useContext(_t),kt=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},jt=e=>{S.useEffect(()=>{e.clearReset()},[e])},Bt=({result:e,errorResetBoundary:t,throwOnError:i,query:r,suspense:l})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(l&&e.data===void 0||Tt(i,[e.error,r])),At=e=>{const t=e.staleTime;e.suspense&&(e.staleTime=typeof t=="function"?(...i)=>Math.max(t(...i),1e3):Math.max(t??1e3,1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},Ht=(e,t)=>e.isLoading&&e.isFetching&&!t,zt=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,mt=(e,t,i)=>t.fetchOptimistic(e).catch(()=>{i.clearReset()});function Vt(e,t,i){var u,k,f,j,B;const r=xt(),l=Mt(),o=Lt(),h=r.defaultQueryOptions(e);(k=(u=r.getDefaultOptions().queries)==null?void 0:u._experimental_beforeQuery)==null||k.call(u,h),h._optimisticResults=l?"isRestoring":"optimistic",At(h),kt(h,o),jt(o);const w=!r.getQueryCache().get(h.queryHash),[p]=S.useState(()=>new t(r,h)),v=p.getOptimisticResult(h),L=!l&&e.subscribed!==!1;if(S.useSyncExternalStore(S.useCallback(m=>{const V=L?p.subscribe(vt.batchCalls(m)):bt;return p.updateResult(),V},[p,L]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),S.useEffect(()=>{p.setOptions(h)},[h,p]),zt(h,v))throw mt(h,p,o);if(Bt({result:v,errorResetBoundary:o,throwOnError:h.throwOnError,query:r.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw v.error;if((j=(f=r.getDefaultOptions().queries)==null?void 0:f._experimental_afterQuery)==null||j.call(f,h,v),h.experimental_prefetchInRender&&!Y&&Ht(v,l)){const m=w?mt(h,p,o):(B=r.getQueryCache().get(h.queryHash))==null?void 0:B.promise;m==null||m.catch(bt).finally(()=>{p.updateResult()})}return h.notifyOnChangeProps?v:p.trackResult(v)}function qt(e,t){return Vt(e,Ft)}export{Ft as Q,Mt as a,Lt as b,kt as c,jt as d,At as e,mt as f,Bt as g,zt as s,qt as u,Ht as w};
