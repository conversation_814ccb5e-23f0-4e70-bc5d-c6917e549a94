import{r as b,j as e,B as g,c as h}from"./index-B283E1a3.js";import{C as w,a as y,b as N,c as P,d as k,e as l}from"./command-C1ySvjo8.js";import{P as E,a as F,b as O}from"./popover-CCXriU_R.js";import{C as B}from"./chevrons-up-down-DYKAIzJg.js";import{C as D}from"./check-CjIon4B5.js";function R({value:s,onValueChange:p,options:r,isLoading:n=!1,placeholder:x="Chọn...",className:u="w-48",allOptionLabel:j="Tất cả",loadingText:C="Đang tải...",emptyText:t="Không có dữ liệu",showAllOption:o=!0,searchPlaceholder:f="<PERSON>ìm kiếm..."}){const[m,d]=b.useState(!1),i=o?[{value:"all",label:j},...r]:r,c=i.find(a=>a.value===s);return e.jsxs(E,{open:m,onOpenChange:d,children:[e.jsx(F,{asChild:!0,children:e.jsxs(g,{variant:"outline",role:"combobox","aria-expanded":m,className:h("justify-between",u,!s&&"text-muted-foreground"),children:[c?c.label:x,e.jsx(B,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(O,{className:"w-full p-0",style:{width:"var(--radix-popover-trigger-width)"},children:e.jsxs(w,{children:[e.jsx(y,{placeholder:f}),e.jsxs(N,{children:[e.jsx(P,{children:t}),e.jsx(k,{children:n?e.jsx(l,{disabled:!0,children:C}):e.jsxs(e.Fragment,{children:[i.map(a=>e.jsxs(l,{value:a.value,onSelect:v=>{p(v),d(!1)},children:[e.jsx(D,{className:h("mr-2 h-4 w-4",s===a.value?"opacity-100":"opacity-0")}),a.label]},a.value)),!n&&r.length===0&&!o&&e.jsx(l,{disabled:!0,children:t})]})})]})]})})]})}export{R as F};
