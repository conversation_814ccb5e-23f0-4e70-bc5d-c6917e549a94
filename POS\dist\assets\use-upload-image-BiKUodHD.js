import{r as t}from"./index-B283E1a3.js";import{i}from"./images-api-CKiFhZ_l.js";const g=()=>{const[o,r]=t.useState(!1),[n,s]=t.useState(null);return{uploadImage:async l=>{r(!0),s(null);try{const a=(await i.uploadImage(l)).data.image_url;return{image_path:a,image_path_thumb:`${a}?width185`}}catch(e){const a=e instanceof Error?e.message:"Upload failed";return s(a),null}finally{r(!1)}},isUploading:o,error:n}};export{g as u};
