import{r as h,h as ze,j as e,B as w,c as Se,T as Ue,o as Ke,p as Ee,q as qe,R as ke,a4 as K,u as Le,l as $e}from"./index-B283E1a3.js";import"./pos-api-C7RsFAun.js";import"./vietqr-api-BbJFOv9v.js";import{u as Ge}from"./use-customizations-BFIcwtLL.js";import"./user-Cxn8z8PZ.js";import"./crm-api-CE_jLH-z.js";import{H as Xe}from"./header-BZ_7I_4c.js";import{M as We}from"./main-BlYSJOOd.js";import{P as Je}from"./profile-dropdown-DhwpuuhW.js";import{S as Qe,T as Ye}from"./search-eyocbSug.js";import{u as ie,a as Ze,b as es,g as Ie,c as ss,d as ts,e as as,f as is,I as ns,h as ls,i as cs,j as rs,C as os,B as ds}from"./customization-dialog-BV7oJgJP.js";import"./exceljs.min-C48dW61m.js";import{h as ms,x as ge,G as _e,H as hs,J as us,K as xs,i as ps,A as fs,a as gs,C as _s,B as js}from"./react-icons.esm-CwfFxlzT.js";import{D as ys,a as ws,b as bs,c as G}from"./dropdown-menu-JDsssJHk.js";import{D as q}from"./data-table-column-header-CWpQ6mZS.js";import{B as Te}from"./badge-DZXns0dL.js";import{S as vs}from"./status-badge-DMRPoBl5.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{C as De}from"./checkbox-DtNgKdj2.js";import{S as se}from"./settings-C9fDD83q.js";import{I as Ns}from"./IconCopy-Bx-S4wCr.js";import{I as Cs}from"./IconTrash-C3WF0IhL.js";import{u as Re,g as Ss,a as ks,b as Is,d as Ts,e as Be,f as te}from"./index-AD4tjDOS.js";import{S as je,a as Y}from"./scroll-area-BlxlVxpe.js";import{T as ye,a as we,b as L,c as ae,d as be,e as X}from"./table-vHuVXp9f.js";import{C as Fe}from"./confirm-dialog-CZ8uf1iG.js";import{a as Ds,C as Ms}from"./chevron-right-CVT48KKP.js";import{u as ne}from"./use-item-types-ClqdZoOZ.js";import{u as le}from"./use-removed-items-D-t1fwGe.js";import{I as zs}from"./input-Bx4sCRS0.js";import{S as he,a as ue,b as xe,c as pe,d as Q}from"./select-BzVwefGp.js";import{M as Rs}from"./multi-select-7h94IHsg.js";import{T as Ve}from"./trash-2-C_rH0_Af.js";import{I as Bs}from"./IconFilter-B9BLN9Lb.js";import{X as Fs}from"./calendar-AxR9kFpj.js";import{S as o}from"./skeleton-BwMfFUqN.js";import{read as Pe,utils as He}from"./xlsx-DkH2s96g.js";import{u as ve}from"./use-item-classes-TuVgnk5f.js";import{u as Ne}from"./use-units-5I1VqZ6k.js";import{D as ce,a as re,b as oe,c as de}from"./dialog-BTZKnesd.js";import{C as fe}from"./combobox-Db0SwfY4.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./query-keys-3lmd-xp6.js";import"./separator-DLHnMAQ0.js";import"./avatar-CfLE65or.js";import"./search-context-CZoJZmsi.js";import"./command-C1ySvjo8.js";import"./search-BKvg0ovQ.js";import"./createLucideIcon-D6RMy2u2.js";import"./createReactComponent-WabRa4kY.js";import"./IconChevronRight-jxL9ONfH.js";import"./IconSearch-S0sgK6Kj.js";import"./use-dialog-state-DpGk-Lpu.js";import"./modal-COeiv6He.js";import"./zod-BOoGjb2n.js";import"./index-Df0XEEuz.js";import"./index-CqlrRQAb.js";import"./index-BhFEt02S.js";import"./check-CjIon4B5.js";import"./popover-CCXriU_R.js";import"./isSameMonth-C8JQo-AN.js";import"./index-DY0KH0l4.js";import"./alert-dialog-BJpiIdrl.js";import"./circle-x-CQicUDeF.js";import"./chevrons-up-down-DYKAIzJg.js";function Vs(){const[n,s]=h.useState(!1),[i,l]=h.useState(!1),[a,c]=h.useState(null),[d,f]=h.useState(!1),[_,z]=h.useState([]),[M,S]=h.useState("all"),[C,j]=h.useState("all"),[u,b]=h.useState([]),[I,g]=h.useState("all");return{isCustomizationDialogOpen:n,isBuffetItem:i,selectedMenuItem:a,isBuffetConfigModalOpen:d,selectedBuffetMenuItem:_,selectedItemTypeUid:M,selectedCityUid:C,selectedDaysOfWeek:u,selectedStatus:I,setIsCustomizationDialogOpen:s,setIsBuffetItem:l,setSelectedMenuItem:c,setIsBuffetConfigModalOpen:f,setSelectedBuffetMenuItem:z,setSelectedItemTypeUid:S,setSelectedCityUid:j,setSelectedDaysOfWeek:b,setSelectedStatus:g}}function Ps(){const{setOpen:n}=ie(),s=ze(),i=()=>{s({to:"/menu/items/items-in-city/detail"})},l=()=>{n("export-dialog")},a=()=>{n("import")},c=()=>{},d=()=>{},f=()=>{},_=()=>{};return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(ys,{children:[e.jsx(ws,{asChild:!0,children:e.jsxs(w,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(ms,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(bs,{align:"end",className:"w-56",children:[e.jsxs(G,{onClick:l,children:[e.jsx(ge,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs(G,{onClick:a,children:[e.jsx(_e,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs(G,{onClick:c,children:[e.jsx(hs,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs(G,{onClick:d,children:[e.jsx(us,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs(G,{onClick:f,children:[e.jsx(xs,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs(G,{onClick:_,children:[e.jsx(ps,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(w,{variant:"default",size:"sm",onClick:i,children:"Tạo món"})]})})}function Me({column:n,title:s,className:i,defaultSort:l="desc"}){if(!n.getCanSort())return e.jsx("div",{className:Se(i),children:s});const a=()=>{const c=n.getIsSorted();c?c==="desc"?n.toggleSorting(!1):n.toggleSorting(!0):n.toggleSorting(l==="desc")};return e.jsx("div",{className:Se("flex items-center space-x-2",i),children:e.jsxs(w,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:a,children:[e.jsx("span",{children:s}),n.getIsSorted()==="desc"?e.jsx(fs,{className:"ml-2 h-4 w-4"}):n.getIsSorted()==="asc"?e.jsx(gs,{className:"ml-2 h-4 w-4"}):e.jsx(_s,{className:"ml-2 h-4 w-4"})]})})}const Hs=({onBuffetConfigClick:n})=>[{id:"select",header:({table:s})=>e.jsx(De,{checked:s.getIsAllPageRowsSelected(),onCheckedChange:i=>s.toggleAllPageRowsSelected(!!i),"aria-label":"Select all"}),cell:({row:s})=>e.jsx(De,{checked:s.getIsSelected(),onCheckedChange:i=>s.toggleSelected(!!i),"aria-label":"Select row",onClick:i=>i.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:s})=>e.jsx(q,{column:s,title:"Mã món"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:s})=>e.jsx(q,{column:s,title:"Tên món"}),cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:s.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:s})=>e.jsx(q,{column:s,title:"Giá"}),cell:({row:s})=>{const i=s.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(i)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:s})=>e.jsx(q,{column:s,title:"VAT (%)"}),cell:({row:s})=>{const i=s.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:i*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:s})=>e.jsx(q,{column:s,title:"Nhóm món"}),cell:({row:s})=>e.jsx(Te,{variant:"outline",className:"text-xs",children:s.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:s})=>e.jsx(q,{column:s,title:"Loại món"}),cell:({row:s})=>s.getValue("itemClass")&&e.jsx(Te,{variant:"outline",className:"text-center text-xs",children:s.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:s})=>e.jsx(q,{column:s,title:"Đơn vị tính"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:s})=>e.jsx(Me,{column:s,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:s})=>{const i=s.getValue("sideItems");if(!i)return e.jsx("div",{children:"Món chính"});const l=i==="Món ăn kèm"?"Món ăn kèm":i;return e.jsx(Ue,{children:e.jsxs(Ke,{children:[e.jsx(Ee,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:l})}),e.jsx(qe,{children:e.jsx("p",{className:"max-w-[300px]",children:l})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:s})=>e.jsx(q,{column:s,title:"Thành phố"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:s})=>e.jsx(q,{column:s,title:"Cấu hình buffet"}),cell:({row:s})=>{var a;const i=s.original;return((a=i.extra_data)==null?void 0:a.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(w,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-6 px-2 text-xs",children:e.jsx(se,{className:"h-3 w-3"})})]}):e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>n(i),className:"h-7 px-2 text-xs",children:[e.jsx(se,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:s})=>e.jsx(q,{column:s,title:"Customization"}),cell:({row:s,table:i})=>{var f;const l=s.original,a=i.options.meta,c=l.customization_uid,d=(f=a==null?void 0:a.customizations)==null?void 0:f.find(_=>_.id===c);return d?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:d.name}),e.jsx(w,{variant:"outline",size:"sm",onClick:()=>{var _;return(_=a==null?void 0:a.onCustomizationClick)==null?void 0:_.call(a,l)},className:"h-6 px-2 text-xs",children:e.jsx(se,{className:"h-3 w-3"})})]}):e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>{var _;return(_=a==null?void 0:a.onCustomizationClick)==null?void 0:_.call(a,l)},className:"h-7 px-2 text-xs",children:[e.jsx(se,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:s,table:i})=>{const l=s.original,a=i.options.meta;return e.jsxs(w,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:c=>{var d;c.stopPropagation(),(d=a==null?void 0:a.onCopyClick)==null||d.call(a,l)},children:[e.jsx(Ns,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",l.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:s})=>e.jsx(Me,{column:s,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:s,table:i})=>{const l=s.original,a=s.getValue("isActive"),c=i.options.meta;return e.jsx("div",{onClick:d=>{var f;d.stopPropagation(),(f=c==null?void 0:c.onToggleStatus)==null||f.call(c,l)},className:"cursor-pointer",children:e.jsx(vs,{isActive:a,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:s,table:i})=>{const l=s.original,a=i.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(w,{variant:"ghost",size:"sm",onClick:c=>{var d;c.stopPropagation(),(d=a==null?void 0:a.onDeleteClick)==null||d.call(a,l)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(Cs,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",l.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Os=Hs;function As({currentPage:n,onPageChange:s,hasNextPage:i}){const l=()=>{n>1&&s(n-1)},a=()=>{i&&s(n+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(w,{variant:"outline",size:"sm",onClick:l,disabled:n===1,className:"flex items-center gap-2",children:[e.jsx(Ds,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:n}),e.jsxs(w,{variant:"outline",size:"sm",onClick:a,disabled:!i,className:"flex items-center gap-2",children:["Sau",e.jsx(Ms,{className:"h-4 w-4"})]})]})}const Us=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],Ks=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function Es({table:n,selectedItemTypeUid:s="all",onItemTypeChange:i,selectedCityUid:l="all",onCityChange:a,selectedDaysOfWeek:c=[],onDaysOfWeekChange:d,selectedStatus:f="all",onStatusChange:_,onDeleteSelected:z}){var m;const[M,S]=h.useState(!1),{data:C=[]}=ne(),{data:j=[]}=le(),u=j.filter(t=>t.active===1),b=u.map(t=>({label:t.city_name,value:t.id})),I=u.map(t=>t.id).join(",");h.useEffect(()=>{l==="all"&&I&&a&&a(I)},[l,I,a]);const g=n.getState().columnFilters.length>0,v=n.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[v>0&&e.jsxs(w,{variant:"destructive",size:"sm",onClick:z,className:"h-9",children:[e.jsx(Ve,{}),"Xóa món (",v,")"]}),e.jsx(zs,{placeholder:"Tìm kiếm món ăn...",value:((m=n.getColumn("name"))==null?void 0:m.getFilterValue())??"",onChange:t=>{var x;return(x=n.getColumn("name"))==null?void 0:x.setFilterValue(t.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(he,{value:l,onValueChange:t=>{a&&a(t)},children:[e.jsx(ue,{className:"h-10 w-[180px]",children:e.jsx(xe,{placeholder:"Chọn thành phố"})}),e.jsxs(pe,{children:[e.jsx(Q,{value:I,children:"Tất cả thành phố"}),b.map(t=>e.jsx(Q,{value:t.value,children:t.label},t.value))]})]}),e.jsxs(he,{value:f,onValueChange:_,children:[e.jsx(ue,{className:"h-10 w-[180px]",children:e.jsx(xe,{placeholder:"Chọn Trạng thái"})}),e.jsx(pe,{children:Ks.map(t=>e.jsx(Q,{value:t.value,children:t.label},t.value))})]}),e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>S(!M),className:"h-9",children:[e.jsx(Bs,{className:"h-4 w-4"}),"Nâng cao"]}),g&&e.jsxs(w,{variant:"ghost",onClick:()=>n.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(Fs,{className:"ml-2 h-4 w-4"})]})]})}),M&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(he,{value:s,onValueChange:i,children:[e.jsx(ue,{className:"h-10 w-[180px]",children:e.jsx(xe,{placeholder:"Chọn loại món"})}),e.jsxs(pe,{children:[e.jsx(Q,{value:"all",children:"Tất cả nhóm món"}),C.filter(t=>t.active===1).map(t=>({label:t.item_type_name,value:t.id})).map(t=>e.jsx(Q,{value:t.value,children:t.label},t.value))]})]}),e.jsx(Rs,{options:Us,value:c,onValueChange:d||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function qs({columns:n,data:s,onCustomizationClick:i,onCopyClick:l,onToggleStatus:a,onRowClick:c,onDeleteClick:d,customizations:f,selectedItemTypeUid:_,onItemTypeChange:z,selectedCityUid:M,onCityChange:S,selectedDaysOfWeek:C,onDaysOfWeekChange:j,selectedStatus:u,onStatusChange:b,hasNextPageOverride:I,currentPage:g,onPageChange:v}){var $;const[m,t]=h.useState({}),[x,r]=h.useState({}),[T,V]=h.useState([]),[U,p]=h.useState([]),[y,F]=h.useState(!1),{deleteMultipleItemsAsync:E}=Ze(),P=()=>{F(!0)},H=async()=>{try{const k=D.getFilteredSelectedRowModel().rows.map(A=>A.original.id);await E(k),F(!1),D.resetRowSelection()}catch{}},O=(R,k)=>{const A=k.target;A.closest('input[type="checkbox"]')||A.closest("button")||A.closest('[role="button"]')||A.closest(".badge")||A.tagName==="BUTTON"||c==null||c(R)},D=Re({data:s,columns:n,state:{sorting:U,columnVisibility:x,rowSelection:m,columnFilters:T},enableRowSelection:!0,onRowSelectionChange:t,onSortingChange:p,onColumnFiltersChange:V,onColumnVisibilityChange:r,getCoreRowModel:Be(),getFilteredRowModel:Ts(),getSortedRowModel:Is(),getFacetedRowModel:ks(),getFacetedUniqueValues:Ss(),meta:{onCustomizationClick:i,onCopyClick:l,onToggleStatus:a,onDeleteClick:d,customizations:f}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(Es,{table:D,selectedItemTypeUid:_,onItemTypeChange:z,selectedCityUid:M,onCityChange:S,selectedDaysOfWeek:C,onDaysOfWeekChange:j,selectedStatus:u,onStatusChange:b,onDeleteSelected:P}),e.jsxs(je,{className:"rounded-md border",children:[e.jsxs(ye,{className:"relative",children:[e.jsx(we,{children:D.getHeaderGroups().map(R=>e.jsx(L,{children:R.headers.map(k=>e.jsx(ae,{colSpan:k.colSpan,children:k.isPlaceholder?null:te(k.column.columnDef.header,k.getContext())},k.id))},R.id))}),e.jsx(be,{children:($=D.getRowModel().rows)!=null&&$.length?D.getRowModel().rows.map(R=>e.jsx(L,{"data-state":R.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:k=>O(R.original,k),children:R.getVisibleCells().map(k=>e.jsx(X,{children:te(k.column.columnDef.cell,k.getContext())},k.id))},R.id)):e.jsx(L,{children:e.jsx(X,{colSpan:n.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(Y,{orientation:"horizontal"})]}),e.jsx(As,{currentPage:g??1,onPageChange:R=>v&&v(R),hasNextPage:!!I}),e.jsx(Fe,{open:y,onOpenChange:F,title:`Bạn có chắc muốn xóa ${D.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:H,destructive:!0})]})}function Ls(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-[250px]"}),e.jsx(o,{className:"h-8 w-[100px]"}),e.jsx(o,{className:"h-8 w-[100px]"}),e.jsx(o,{className:"h-8 w-[100px]"})]}),e.jsx(o,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(o,{className:"h-4 w-8"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-32"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((n,s)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(o,{className:"h-4 w-8"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-32"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-16"})]})},s))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(o,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-[100px]"}),e.jsx(o,{className:"h-8 w-8"}),e.jsx(o,{className:"h-8 w-8"})]})]})]})}function $s({open:n,onOpenChange:s,data:i,onSave:l}){var C;const[a,c]=h.useState(i);ke.useEffect(()=>{c(i)},[i]);const d=()=>{l(a),K.success("Data saved successfully"),s(!1)},f=()=>{s(!1)},_=ke.useCallback(j=>{const u=a.filter((b,I)=>I!==j);c(u)},[a]),{tableData:z,columns:M}=h.useMemo(()=>{if(!a||a.length===0)return{tableData:[],columns:[]};const j=a[0]||[],u=a.slice(1),b=[{id:"actions",header:"-",cell:({row:g})=>e.jsx(w,{variant:"ghost",size:"sm",onClick:()=>_(g.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(js,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...j.map((g,v)=>({id:`col_${v}`,accessorKey:`col_${v}`,header:String(g),cell:({row:m})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:m.getValue(`col_${v}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:u.map((g,v)=>{const m={_originalIndex:v+1};return g.forEach((t,x)=>{m[`col_${x}`]=t}),m}),columns:b}},[a,_]),S=Re({data:z,columns:M,getCoreRowModel:Be()});return!a||a.length===0?null:e.jsx(ce,{open:n,onOpenChange:s,children:e.jsxs(re,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(oe,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(de,{className:"text-xl font-semibold",children:"Thêm món từ file"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(je,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(ye,{children:[e.jsx(we,{className:"sticky top-0 z-10 bg-white",children:S.getHeaderGroups().map(j=>e.jsx(L,{children:j.headers.map(u=>{var b;return e.jsx(ae,{className:((b=u.column.columnDef.meta)==null?void 0:b.className)||"",children:u.isPlaceholder?null:te(u.column.columnDef.header,u.getContext())},u.id)})},j.id))}),e.jsx(be,{children:(C=S.getRowModel().rows)!=null&&C.length?S.getRowModel().rows.map(j=>e.jsx(L,{className:"hover:bg-muted/50",children:j.getVisibleCells().map(u=>{var b;return e.jsx(X,{className:((b=u.column.columnDef.meta)==null?void 0:b.className)||"",children:te(u.column.columnDef.cell,u.getContext())},u.id)})},j.id)):e.jsx(L,{children:e.jsx(X,{colSpan:M.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(Y,{orientation:"horizontal"}),e.jsx(Y,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(w,{variant:"outline",onClick:f,children:"Đóng"}),e.jsx(w,{onClick:d,className:"bg-green-600 hover:bg-green-700",children:"Lưu"})]})]})]})})}function Gs(){const{open:n,setOpen:s}=ie(),[i,l]=h.useState(!1),[a,c]=h.useState([]),d=h.useRef(null),{data:f=[]}=ne(),{data:_=[]}=ve(),{data:z=[]}=Ne(),{data:M=[]}=le(),{downloadImportTemplateAsync:S,isPending:C}=es(),j=async()=>{try{await S({itemTypes:f,itemClasses:_,units:z,cities:M})}catch{K.error("Lỗi khi tải template")}},u=()=>{var g;(g=d.current)==null||g.click()},b=g=>{var t;const v=(t=g.target.files)==null?void 0:t[0];if(!v)return;const m=new FileReader;m.onload=x=>{var r;try{const T=new Uint8Array((r=x.target)==null?void 0:r.result),V=Pe(T,{type:"array"}),U=V.SheetNames[0],p=V.Sheets[U],y=He.sheet_to_json(p,{defval:"",raw:!1});if(y.length===0){K.error("File không có dữ liệu");return}const F=new Set,E=y.map(P=>{const H=(P["Mã món"]??"").toString().trim();H&&F.add(H);let O=H;if(!O){let D=Ie();for(;F.has(D);)D=Ie();F.add(D),O=D}return{...P,"Mã món":O}});if(E.length>0){const P=Object.keys(E[0]),H=[P,...E.map(O=>P.map(D=>O[D]||""))];c(H)}else c([]);s(null),l(!0),d.current&&(d.current.value="")}catch{K.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},m.readAsArrayBuffer(v)},I=()=>{K.success("Dữ liệu đã được lưu thành công!"),l(!1),s(null)};return e.jsxs(e.Fragment,{children:[e.jsx(ce,{open:n==="import",onOpenChange:g=>s(g?"import":null),children:e.jsxs(re,{className:"max-w-2xl",children:[e.jsx(oe,{children:e.jsx(de,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(w,{variant:"outline",size:"sm",onClick:j,disabled:C,className:"flex items-center gap-2",children:C?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(ge,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(w,{variant:"outline",size:"sm",onClick:u,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(_e,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:d,type:"file",accept:".xlsx,.xls",onChange:b,style:{display:"none"}}),e.jsx($s,{open:i,onOpenChange:l,data:a,onSave:I})]})}function Xs({open:n,onOpenChange:s,data:i}){const[l,a]=h.useState(i),[c,d]=h.useState(!1),{user:f,company:_}=Le(m=>m.auth),{selectedBrand:z}=$e(),{mutate:M,isPending:S}=ss(),{data:C=[]}=ne({skip_limit:!0}),{data:j=[]}=ve({skip_limit:!0}),{data:u=[]}=Ne(),{data:b=[]}=le();h.useEffect(()=>{a(i)},[i]);const I=m=>{a(t=>t.filter((x,r)=>r!==m))},g=async()=>{if(!_||!z){K.error("Thiếu thông tin cần thiết để cập nhật");return}d(!0);const m=l.map(t=>{const x=u.find(y=>y.unit_id===t.unit_id),r=b.find(y=>y.city_name===t.city_name),T=C.find(y=>y.item_type_id===t.item_type_id||y.item_type_name===t.item_type_name),V=j.find(y=>y.item_class_id===t.item_class_id||y.item_class_name===t.item_class_name),U=u.find(y=>y.unit_id==="MON"),p=C.find(y=>y.item_type_name==="LOẠI KHÁC");return{item_id:t.item_id,item_name:t.item_name,description:t.description||"",ots_price:t.ots_price||0,ots_tax:(t.ots_tax||0)/100,ta_price:t.ots_price||0,ta_tax:(t.ots_tax||0)/100,time_sale_hour_day:String(t.time_sale_hour_day??0),time_sale_date_week:String(t.time_sale_date_week??0),allow_take_away:1,is_eat_with:t.is_eat_with||0,image_path:t.image_path||"",image_path_thumb:t.image_path?`${t.image_path}?width=185`:"",item_color:"",list_order:t.list_order||0,is_service:t.is_item_service||0,is_material:0,active:t.active||1,user_id:"",is_foreign:0,quantity_default:0,price_change:t.price_change||0,currency_type_id:"",point:0,is_gift:0,is_fc:0,show_on_web:0,show_price_on_web:0,cost_price:0,is_print_label:t.is_print_label||0,quantity_limit:0,is_kit:0,time_cooking:(t.time_cooking||0)*6e4,item_id_barcode:t.item_id_barcode||"",process_index:0,is_allow_discount:t.is_allow_discount||0,quantity_per_day:0,item_id_eat_with:"",is_parent:0,is_sub:0,item_id_mapping:String(t.sku||""),effective_date:0,expire_date:0,sort:t.list_order||1,sort_online:1e3,extra_data:{cross_price:t.cross_price||[],formula_qrcode:t.inqr_formula||"",is_buffet_item:t.is_buffet_item||0,up_size_buffet:[],is_item_service:t.is_item_service||0,is_virtual_item:t.is_virtual_item||0,price_by_source:t.price_by_source||[],enable_edit_price:t.price_change||0,exclude_items_buffet:t.exclude_items_buffet||[],no_update_quantity_toping:t.no_update_quantity_toping||0},revision:0,unit_uid:(x==null?void 0:x.id)||(U==null?void 0:U.id)||"",unit_secondary_uid:null,item_type_uid:(T==null?void 0:T.id)||(p==null?void 0:p.id)||"",item_class_uid:(V==null?void 0:V.id)||void 0,source_uid:null,brand_uid:z.id,city_uid:(r==null?void 0:r.id)||"",company_uid:_.id,customization_uid:t.customization_uid||"",is_fabi:1,deleted:!1,created_by:(f==null?void 0:f.email)||"",updated_by:(f==null?void 0:f.email)||"",deleted_by:null,created_at:t.created_at||Math.floor(Date.now()/1e3),updated_at:Math.floor(Date.now()/1e3),deleted_at:null,cities:r?[{id:r.id,city_id:r.city_id||"",fb_city_id:r.fb_city_id||"",city_name:r.city_name,image_path:r.image_path,description:r.description||"",active:r.active||1,extra_data:r.extra_data,revision:r.revision||0,sort:r.sort||0,created_by:r.created_by,updated_by:r.updated_by,deleted_by:r.deleted_by,created_at:r.created_at||0,updated_at:r.updated_at||0,deleted_at:r.deleted_at,items_cities:{item_uid:t.id,city_uid:r.id}}]:[],id:t.id}});M(m,{onSuccess:()=>{d(!1),s(!1)},onError:t=>{console.error("Error updating items:",t),K.error(`Có lỗi xảy ra khi cập nhật món ăn: ${t}`),d(!1)}})},v=[{key:"item_id",label:"Mã món",width:"120px"},{key:"city_name",label:"Thành phố",width:"120px"},{key:"item_name",label:"Tên",width:"200px"},{key:"ots_price",label:"Giá",width:"100px"},{key:"active",label:"Trạng thái",width:"100px"},{key:"item_id_barcode",label:"Mã barcode",width:"120px"},{key:"is_eat_with",label:"Món ăn kèm",width:"120px"},{key:"no_update_quantity_toping",label:"Không cập nhật số lượng",width:"180px"},{key:"unit_name",label:"Đơn vị",width:"100px"},{key:"item_type_id",label:"Nhóm",width:"120px"},{key:"item_type_name",label:"Tên nhóm",width:"150px"},{key:"item_class_id",label:"Loại món",width:"120px"},{key:"item_class_name",label:"Tên loại",width:"150px"},{key:"description",label:"Mô tả",width:"200px"},{key:"sku",label:"SKU",width:"100px"},{key:"ots_tax",label:"VAT (%)",width:"80px"},{key:"time_cooking",label:"Thời gian chế biến (phút)",width:"180px"},{key:"price_change",label:"Cho phép sửa giá khi bán",width:"180px"},{key:"is_virtual_item",label:"Cấu hình món ảo",width:"150px"},{key:"is_item_service",label:"Cấu hình món dịch vụ",width:"180px"},{key:"is_buffet_item",label:"Cấu hình món ăn là vé buffet",width:"200px"},{key:"time_sale_hour_day",label:"Giờ",width:"80px"},{key:"time_sale_date_week",label:"Ngày",width:"80px"},{key:"list_order",label:"Thứ tự",width:"80px"},{key:"image_path",label:"Hình ảnh",width:"120px"},{key:"inqr_formula",label:"Công thức inQR cho máy pha trà",width:"220px"}];return e.jsx(ce,{open:n,onOpenChange:s,children:e.jsxs(re,{className:"max-h-[90vh] max-w-7xl sm:max-w-4xl",children:[e.jsx(oe,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:e.jsx(de,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})}),e.jsxs("div",{className:"space-y-4 overflow-hidden",children:[e.jsxs(je,{className:"h-[60vh] w-full rounded-md border",children:[e.jsxs(ye,{children:[e.jsx(we,{className:"sticky top-0 z-10 bg-white",children:e.jsxs(L,{children:[e.jsx(ae,{className:"w-12"}),v.map(m=>e.jsx(ae,{style:{width:m.width},children:m.label},m.key))]})}),e.jsx(be,{children:l.map((m,t)=>e.jsxs(L,{children:[e.jsx(X,{children:e.jsx(w,{variant:"ghost",size:"icon",onClick:()=>I(t),className:"h-8 w-8 text-red-500 hover:text-red-700",children:e.jsx(Ve,{className:"h-4 w-4"})})}),v.map(x=>e.jsx(X,{style:{width:x.width},children:(()=>{var T;const r=m[x.key];return x.key==="ots_price"?e.jsxs("span",{className:"text-right",children:[((T=Number(r))==null?void 0:T.toLocaleString("vi-VN"))||0," ₫"]}):x.key==="active"?e.jsx("span",{children:r}):x.key==="item_id"||x.key==="item_id_barcode"?e.jsx("span",{className:"font-mono text-sm",children:r||""}):x.key==="item_name"?e.jsx("span",{className:"font-medium",children:r||""}):["is_eat_with","no_update_quantity_toping","price_change","is_virtual_item","is_item_service","is_buffet_item"].includes(x.key)?e.jsx("span",{className:"text-center",children:r}):e.jsx("span",{children:r||""})})()},x.key))]},t))})]}),e.jsx(Y,{orientation:"horizontal"}),e.jsx(Y,{orientation:"vertical"})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx(w,{variant:"outline",onClick:()=>s(!1),children:"Đóng"}),e.jsx(w,{onClick:g,disabled:c||S,children:c||S?"Đang lưu...":"Lưu"})]})]})]})})}function Ws({open:n,onOpenChange:s}){const[i,l]=h.useState("all"),[a,c]=h.useState("all"),[d,f]=h.useState("all"),[_,z]=h.useState([]),[M,S]=h.useState(!1),C=h.useRef(null),{data:j=[]}=ne(),{data:u=[]}=ve(),{data:b=[]}=Ne(),{data:I=[]}=le(),{fetchItemsDataAsync:g,isPending:v}=ts(),m=[{label:"Tất cả nhóm món",value:"all"},...j.filter(p=>p.active===1).map(p=>({label:p.item_type_name,value:p.id}))],t=[{label:"Tất cả thành phố",value:"all"},...I.filter(p=>p.active===1).map(p=>({label:p.city_name,value:p.id}))],x=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}],r=async()=>{try{const p=await g({city_uid:i!=="all"?i:void 0,item_type_uid:a!=="all"?a:void 0,active:d!=="all"?d:void 0});await as({itemTypes:j,itemClasses:u,units:b},p),K.success("Tải file thành công!")}catch{K.error("Lỗi khi tải file")}},T=p=>({ID:"id","Mã món":"item_id","Thành phố":"city_name",Tên:"item_name",Giá:"ots_price","Trạng thái":"active","Mã barcode":"item_id_barcode","Món ăn kèm":"is_eat_with","Không cập nhật số lượng món ăn kèm":"no_update_quantity_toping","Đơn vị":"unit_name",Nhóm:"item_type_id","Tên nhóm":"item_type_name","Loại món":"item_class_id","Tên loại":"item_class_name","Mô tả":"description",SKU:"sku","VAT (%)":"ots_tax","Thời gian chế biến (phút)":"time_cooking","Cho phép sửa giá khi bán":"price_change","Cấu hình món ảo":"is_virtual_item","Cấu hình món dịch vụ":"is_item_service","Cấu hình món ăn là vé buffet":"is_buffet_item",Giờ:"time_sale_hour_day",Ngày:"time_sale_date_week","Thứ tự":"list_order","Hình ảnh":"image_path","Công thức inQR cho máy pha trà":"inqr_formula"})[p]||p.toLowerCase().replace(/\s+/g,"_"),V=p=>{var E;const y=(E=p.target.files)==null?void 0:E[0];if(!y)return;const F=new FileReader;F.onload=P=>{var H;try{const O=new Uint8Array((H=P.target)==null?void 0:H.result),D=Pe(O,{type:"array"}),$=D.SheetNames[0],R=D.Sheets[$],k=He.sheet_to_json(R,{header:1});if(k.length>0){const A=k,N=A[0]||[],Z=A.slice(1).map((Oe,me)=>{const J={id:`temp_${me}`};return N.forEach((Ce,Ae)=>{const B=T(String(Ce)),ee=Oe[Ae];me===0&&console.log(`Header: "${Ce}" -> Key: "${B}", Value: "${ee}"`),B==="ots_price"||B==="ots_tax"||B==="time_cooking"||B==="time_sale_hour_day"||B==="time_sale_date_week"||B==="list_order"||B==="active"||B==="is_eat_with"||B==="no_update_quantity_toping"||B==="price_change"||B==="is_virtual_item"||B==="is_item_service"||B==="is_buffet_item"?J[B]=Number(ee)||0:J[B]=ee||""}),me===0&&console.log("First item after processing:",J),J});z(Z),S(!0),K.success("File uploaded successfully")}}catch{K.error("Error parsing file")}},F.readAsArrayBuffer(y)},U=()=>{var p;(p=C.current)==null||p.click()};return e.jsxs(e.Fragment,{children:[e.jsx(ce,{open:n,onOpenChange:s,children:e.jsxs(re,{className:"max-w-2xl lg:max-w-xl",children:[e.jsx(oe,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(de,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(fe,{options:t,value:i,onValueChange:l,placeholder:"Tất cả thành phố",searchPlaceholder:"Tìm thành phố...",className:"flex-1"}),e.jsx(fe,{options:m,value:a,onValueChange:c,placeholder:"Tất cả nhóm món",searchPlaceholder:"Tìm nhóm món...",className:"flex-1"}),e.jsx(fe,{options:x,value:d,onValueChange:f,placeholder:"Tất cả trạng thái",searchPlaceholder:"Tìm trạng thái...",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsxs(w,{variant:"outline",size:"sm",onClick:r,disabled:v,className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-4 w-4"}),v&&"Đang tải..."]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(w,{variant:"outline",size:"sm",onClick:U,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(_e,{className:"h-4 w-4"})]}),e.jsx("input",{ref:C,type:"file",accept:".xlsx,.xls",onChange:V,style:{display:"none"}})]})]})]})]})}),e.jsx(Xs,{open:M,onOpenChange:S,data:_})]})}function Js(){const{open:n,setOpen:s,currentRow:i,setCurrentRow:l}=ie(),{deleteItemAsync:a}=is();return e.jsxs(e.Fragment,{children:[e.jsx(Ws,{open:n==="export-dialog",onOpenChange:()=>s(null)}),e.jsx(Gs,{}),i&&e.jsx(e.Fragment,{children:e.jsx(Fe,{destructive:!0,open:n==="delete",onOpenChange:c=>{c||(s(null),setTimeout(()=>{l(null)},500))},handleConfirm:async()=>{s(null),setTimeout(()=>{l(null)},500),await a(i.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})]})}function Qs(){const n=ze(),[s,i]=h.useState(1),{setOpen:l,setCurrentRow:a}=ie(),{updateStatusAsync:c}=ls(),{updateItemAsync:d}=cs(),{isCustomizationDialogOpen:f,isBuffetItem:_,isBuffetConfigModalOpen:z,setIsCustomizationDialogOpen:M,setIsBuffetItem:S,selectedMenuItem:C,setSelectedMenuItem:j,setIsBuffetConfigModalOpen:u,selectedBuffetMenuItem:b,setSelectedBuffetMenuItem:I,selectedItemTypeUid:g,setSelectedItemTypeUid:v,selectedCityUid:m,setSelectedCityUid:t,selectedDaysOfWeek:x,setSelectedDaysOfWeek:r,selectedStatus:T,setSelectedStatus:V}=Vs(),U=h.useMemo(()=>({...g!=="all"&&{item_type_uid:g},...m!=="all"&&{city_uid:m},...x.length>0&&{time_sale_date_week:x.join(",")},...T!=="all"&&{active:parseInt(T,10)},page:s}),[g,m,x,T,s]);h.useEffect(()=>{i(1)},[g,m,x,T]);const{data:p=[],isLoading:y,error:F,hasNextPage:E}=rs({params:U}),{data:P=[]}=Ge({skip_limit:!0,list_city_uid:m!=="all"?[m]:void 0}),H=N=>{j(N),M(!0)},O=N=>{var W,Z;j(N),I(((W=N==null?void 0:N.extra_data)==null?void 0:W.exclude_items_buffet)||[]),S(((Z=N==null?void 0:N.extra_data)==null?void 0:Z.is_buffet_item)===1),u(!0)},D=N=>{n({to:"/menu/items/items-in-city/detail",search:{id:N.id||""}})},$=N=>{a(N),l("delete")},R=N=>{n({to:"/menu/items/items-in-city/detail/$id",params:{id:N.id||""}})},k=async N=>{const W=N.active?0:1;await c({id:N.id||"",active:W})},A=Os({onBuffetConfigClick:O});return F?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:F&&`Món ăn: ${(F==null?void 0:F.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[e.jsx(Xe,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Qe,{}),e.jsx(Ye,{}),e.jsx(Je,{})]})}),e.jsxs(We,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(Ps,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[y&&e.jsx(Ls,{}),!y&&e.jsx(qs,{columns:A,data:p,onCustomizationClick:H,onCopyClick:D,onToggleStatus:k,onRowClick:R,onDeleteClick:$,customizations:P,selectedItemTypeUid:g,onItemTypeChange:v,selectedCityUid:m,onCityChange:t,selectedDaysOfWeek:x,onDaysOfWeekChange:r,selectedStatus:T,onStatusChange:V,hasNextPageOverride:E,currentPage:s,onPageChange:i})]})]}),e.jsx(Js,{}),f&&C&&e.jsx(os,{open:f,onOpenChange:M,item:C,customizations:P}),z&&b&&e.jsx(ds,{itemsBuffet:b,open:z,onOpenChange:u,onItemsChange:async N=>{await d({...C,extra_data:{is_buffet_item:_?1:0,exclude_items_buffet:N}})},items:p,hide:!1,enable:_,onEnableChange:S})]})}function Ys(){return e.jsx(ns,{children:e.jsx(Qs,{})})}const ua=Ys;export{ua as component};
