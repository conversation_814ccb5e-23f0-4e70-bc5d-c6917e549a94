import{convertApiSourceToSource as r}from"./sources-CfiQ7039.js";import{a as i}from"./pos-api-C7RsFAun.js";const n={getSources:async e=>{var s;const t=new URLSearchParams;if(e.company_uid&&t.append("company_uid",e.company_uid),e.brand_uid&&t.append("brand_uid",e.brand_uid),e.city_uid&&t.append("city_uid",e.city_uid),e.store_uid&&t.append("store_uid",e.store_uid),e.list_store_uid){const d=Array.isArray(e.list_store_uid)?e.list_store_uid.join(","):e.list_store_uid;d&&t.append("list_store_uid",d)}return e.skip_limit&&t.append("skip_limit","true"),((s=(await i.get(`/mdata/v1/sources?${t.toString()}`)).data.data)==null?void 0:s.map(r))||[]},updateSource:async e=>{const t=await i.put(`/mdata/v1/sources/${e.id}`,e);return r(t.data.data)},deleteSource:async e=>{await i.delete(`/mdata/v1/sources/${e}`)},getSourcesForAutocomplete:async()=>{var t;return((t=(await i.get("/mdata/v1/sources?skip_limit=true&is_fb=1")).data.data)==null?void 0:t.map(o=>({source_id:o.source_id,source_name:o.source_name})))||[]}};export{n as s};
