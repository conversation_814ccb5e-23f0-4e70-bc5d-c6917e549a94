import { useState, useEffect } from 'react'

import { useAuthStore } from '@/stores/authStore'
import { toast } from 'sonner'

import { useItemTypesData, useItemClassesData, useUnitsData, useCitiesData } from '@/hooks/api'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

import { useBulkCreateItemsInCity, type BulkCreateItemInCityRequest } from '../../../hooks'

export interface ImportItemData {
  'Mã món': string
  'Tên': string
  'Thành phố': string
  'Giá': number
  'Mã barcode'?: string
  'Món ăn kèm'?: string
  'Nhóm'?: string
  'Loại món'?: string
  'Mô tả'?: string
  'SKU'?: string
  'Đơn vị'?: string
  'VAT (%)'?: number
  'Thời gian chế biến (phút)'?: number
  'Cho phép sửa giá khi bán'?: string
  'Cấu hình món ảo'?: string
  'Cấu hình món dịch vụ'?: string
  'Cấu hình món ăn là vé buffet'?: string
  'Ngày'?: number
  'Giờ'?: number
  'Thứ tự'?: number
  'Hình ảnh'?: string
  [key: string]: any
}

interface ExcelPreviewImportDialogWithApiProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: (string | number)[][]
}

export function ExcelPreviewImportDialogWithApi({ 
  open, 
  onOpenChange, 
  data 
}: ExcelPreviewImportDialogWithApiProps) {
  const [importData, setImportData] = useState<ImportItemData[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { bulkCreateItemsInCity, isBulkCreating } = useBulkCreateItemsInCity()

  // Get reference data for mapping
  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true
  })
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true
  })
  const { data: units = [] } = useUnitsData()
  const { data: cities = [] } = useCitiesData()

  useEffect(() => {
    if (data && data.length > 0) {
      const headers = data[0] || []
      const rows = data.slice(1)
      
      const transformedData: ImportItemData[] = rows.map(row => {
        const item: ImportItemData = {}
        headers.forEach((header, index) => {
          item[String(header)] = row[index] || ''
        })
        return item
      })
      
      setImportData(transformedData)
    }
  }, [data])

  const parseBooleanStatus = (value: any): number => {
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase().trim()
      return lowerValue === 'có' || lowerValue === 'yes' || lowerValue === '1' || lowerValue === 'true' ? 1 : 0
    }
    return value ? 1 : 0
  }

  const handleConfirm = async () => {
    if (!company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    if (importData.length === 0) {
      toast.error('Không có dữ liệu để import')
      return
    }

    setIsProcessing(true)

    try {
      const transformedData: BulkCreateItemInCityRequest[] = importData.map(item => {
        // Find city by name
        const city = cities.find(c => c.city_name === item['Thành phố'])
        if (!city) {
          throw new Error(`Không tìm thấy thành phố: ${item['Thành phố']}`)
        }

        // Find unit by ID or default to 'MON'
        const unit = units.find(u => u.unit_id === item['Đơn vị'])
        const defaultUnit = units.find(u => u.unit_id === 'MON')

        // Find item type by ID or name
        const itemType = itemTypes.find(
          it => it.item_type_id === item['Nhóm'] || it.item_type_name === item['Nhóm']
        )
        const defaultItemType = itemTypes.find(it => it.item_type_name === 'LOẠI KHÁC')

        // Find item class by ID or name
        const itemClass = itemClasses.find(
          ic => ic.item_class_id === item['Loại món'] || ic.item_class_name === item['Loại món']
        )

        return {
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          city_uid: city.id,
          item_id: item['Mã món'],
          unit_uid: unit?.id || defaultUnit?.id || '',
          ots_price: Number(item['Giá']) || 0,
          ta_price: Number(item['Giá']) || 0,
          ots_tax: (Number(item['VAT (%)']) || 0) / 100,
          ta_tax: (Number(item['VAT (%)']) || 0) / 100,
          item_name: item['Tên'],
          item_id_barcode: item['Mã barcode'] || '',
          is_eat_with: parseBooleanStatus(item['Món ăn kèm']),
          item_type_uid: itemType?.id || defaultItemType?.id || '',
          item_class_uid: itemClass?.id || null,
          description: item['Mô tả'] || '',
          item_id_mapping: String(item['SKU'] || ''),
          time_cooking: (Number(item['Thời gian chế biến (phút)']) || 0) * 60000,
          time_sale_date_week: Number(item['Ngày']) || 0,
          time_sale_hour_day: Number(item['Giờ']) || 0,
          sort: Number(item['Thứ tự']) || 1,
          image_path_thumb: '',
          image_path: item['Hình ảnh'] || '',
          extra_data: {
            no_update_quantity_toping: parseBooleanStatus(item['Không cập nhật số lượng món ăn kèm']),
            enable_edit_price: parseBooleanStatus(item['Cho phép sửa giá khi bán']),
            is_virtual_item: parseBooleanStatus(item['Cấu hình món ảo']),
            is_item_service: parseBooleanStatus(item['Cấu hình món dịch vụ']),
            is_buffet_item: parseBooleanStatus(item['Cấu hình món ăn là vé buffet'])
          }
        }
      })

      await bulkCreateItemsInCity(transformedData)
      onOpenChange(false)
    } catch (error) {
      console.error('Error creating items:', error)
      toast.error(error instanceof Error ? error.message : 'Có lỗi xảy ra khi tạo món ăn')
    } finally {
      setIsProcessing(false)
    }
  }

  const columns = [
    { key: 'Tên', label: 'Tên', width: '200px' },
    { key: 'Giá', label: 'Giá', width: '100px' },
    { key: 'Mã món', label: 'Mã món', width: '120px' },
    { key: 'Thành phố', label: 'Thành phố', width: '120px' },
    { key: 'Mã barcode', label: 'Mã barcode', width: '120px' },
    { key: 'Món ăn kèm', label: 'Món ăn kèm', width: '120px' },
    { key: 'Nhóm', label: 'Nhóm', width: '120px' },
    { key: 'Loại món', label: 'Loại món', width: '120px' },
    { key: 'Mô tả', label: 'Mô tả', width: '200px' },
    { key: 'SKU', label: 'SKU', width: '100px' },
    { key: 'Đơn vị', label: 'Đơn vị', width: '100px' },
    { key: 'VAT (%)', label: 'VAT (%)', width: '80px' },
    { key: 'Thời gian chế biến (phút)', label: 'Thời gian chế biến (phút)', width: '180px' },
    { key: 'Cho phép sửa giá khi bán', label: 'Cho phép sửa giá khi bán', width: '180px' },
    { key: 'Cấu hình món ảo', label: 'Cấu hình món ảo', width: '150px' },
    { key: 'Cấu hình món dịch vụ', label: 'Cấu hình món dịch vụ', width: '180px' },
    { key: 'Cấu hình món ăn là vé buffet', label: 'Cấu hình món ăn là vé buffet', width: '200px' },
    { key: 'Ngày', label: 'Ngày', width: '80px' },
    { key: 'Giờ', label: 'Giờ', width: '80px' },
    { key: 'Thứ tự', label: 'Thứ tự', width: '80px' },
    { key: 'Hình ảnh', label: 'Hình ảnh', width: '120px' }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Thêm mới</DialogTitle>
        </DialogHeader>

        <div className='space-y-4 overflow-hidden'>
          <ScrollArea className='h-[60vh] w-full rounded-md border'>
            <Table>
              <TableHeader className='sticky top-0 z-10 bg-white'>
                <TableRow>
                  <TableHead className='w-16 text-center'>Thứ tự</TableHead>
                  {columns.map(column => (
                    <TableHead key={column.key} style={{ width: column.width }}>
                      {column.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {importData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className='text-center font-medium'>{index + 1}</TableCell>
                    {columns.map(column => (
                      <TableCell key={column.key} style={{ width: column.width }}>
                        {column.key === 'Giá' ? (
                          <span className='text-right'>{Number(item[column.key])?.toLocaleString('vi-VN')} ₫</span>
                        ) : column.key === 'Mã món' || column.key === 'Mã barcode' ? (
                          <span className='font-mono text-sm'>{item[column.key]}</span>
                        ) : column.key === 'Tên' ? (
                          <span className='font-medium'>{item[column.key]}</span>
                        ) : (
                          <span>{item[column.key] || ''}</span>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <ScrollBar orientation='horizontal' />
            <ScrollBar orientation='vertical' />
          </ScrollArea>

          <div className='flex items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Đóng
            </Button>
            <Button onClick={handleConfirm} disabled={isProcessing || isBulkCreating}>
              {isProcessing || isBulkCreating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
