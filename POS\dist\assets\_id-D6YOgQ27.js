import{aZ as r,j as t}from"./index-B283E1a3.js";import{S as i}from"./index-rKodQ7Gq.js";import"./use-service-charge-form-B5_0hRaf.js";import"./date-utils-DBbLjCz0.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./pos-api-C7RsFAun.js";import"./query-keys-3lmd-xp6.js";import"./discount-toggle-button-B8ho4Bx3.js";import"./date-range-picker-B7aoXHt3.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";import"./popover-CCXriU_R.js";import"./select-BzVwefGp.js";import"./index-Df0XEEuz.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./form-dNL1hWKC.js";import"./input-Bx4sCRS0.js";import"./tabs-CaBi0l4C.js";import"./index-BhFEt02S.js";import"./textarea-hyg9uNcq.js";import"./checkbox-DtNgKdj2.js";import"./modal-COeiv6He.js";import"./dialog-BTZKnesd.js";import"./collapsible-lV085WFw.js";import"./calendar-ALtyELt3.js";import"./circle-help-Q6UK66s-.js";import"./switch-D1TRW_5X.js";const G=function(){const{id:o}=r.useParams();return console.log("🔥 Service Charge Detail Page - URL Params:"),console.log("🔥 serviceChargeId:",o),t.jsx(i,{serviceChargeId:o})};export{G as component};
