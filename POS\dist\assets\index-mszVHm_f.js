import{j as e,h as b,u as E,r as S,B as y,R as C}from"./index-B283E1a3.js";import"./pos-api-C7RsFAun.js";import{u as H}from"./use-stores-C2ErR-pC.js";import"./vietqr-api-BbJFOv9v.js";import{u as I,a as F}from"./use-payment-methods-7l0pPaQU.js";import"./user-Cxn8z8PZ.js";import"./crm-api-CE_jLH-z.js";import{H as R}from"./header-BZ_7I_4c.js";import{M as L}from"./main-BlYSJOOd.js";import{P as O}from"./profile-dropdown-DhwpuuhW.js";import{S as A,T as B}from"./search-eyocbSug.js";import{T as Q}from"./table-pagination-CQwCsWP1.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{I as $}from"./input-Bx4sCRS0.js";import{S as z,a as K,b as U,c as q,d as P}from"./select-BzVwefGp.js";import{a as V,b as f,c as u,T as w,d as v,e as a}from"./table-vHuVXp9f.js";import{C as Y}from"./index-BQauI_Wp.js";import{u as W}from"./useQuery-Cc4LgMzN.js";import{p as X}from"./payment-methods-api-B3LIUPfM.js";import{Q as G}from"./query-keys-3lmd-xp6.js";import{u as J}from"./use-pos-data-D7fYs1vU.js";import{D as Z,a as ee,b as se,c as te}from"./dialog-BTZKnesd.js";import{S as d}from"./skeleton-BwMfFUqN.js";import{E as ae}from"./ellipsis-oL1R8peb.js";import{T as ne}from"./trash-2-C_rH0_Af.js";import{S as re}from"./search-BKvg0ovQ.js";import{P as oe}from"./plus-CKQNSsha.js";import"./useMutation-Bf5OzDko.js";import"./utils-km2FGkQ4.js";import"./stores-api-BC3ZNTx9.js";import"./separator-DLHnMAQ0.js";import"./avatar-CfLE65or.js";import"./dropdown-menu-JDsssJHk.js";import"./index-Df0XEEuz.js";import"./index-CqlrRQAb.js";import"./index-BhFEt02S.js";import"./check-CjIon4B5.js";import"./createLucideIcon-D6RMy2u2.js";import"./search-context-CZoJZmsi.js";import"./command-C1ySvjo8.js";import"./calendar-AxR9kFpj.js";import"./isSameMonth-C8JQo-AN.js";import"./createReactComponent-WabRa4kY.js";import"./scroll-area-BlxlVxpe.js";import"./IconChevronRight-jxL9ONfH.js";import"./IconSearch-S0sgK6Kj.js";import"./pagination-Dms6Uzom.js";import"./react-icons.esm-CwfFxlzT.js";import"./chevron-right-CVT48KKP.js";import"./popover-CCXriU_R.js";import"./index-DY0KH0l4.js";import"./use-auth-Cjjp-n-O.js";function k(){return e.jsx(V,{children:e.jsxs(f,{children:[e.jsx(u,{className:"w-[60px]",children:"#"}),e.jsx(u,{className:"w-[120px]",children:"Mã PTTT"}),e.jsx(u,{className:"min-w-[200px]",children:"Tên PTTT"}),e.jsx(u,{className:"w-[140px]",children:"Phí cà thẻ (%)"}),e.jsx(u,{className:"min-w-[180px]",children:"Cửa hàng áp dụng PTTT"}),e.jsx(u,{className:"w-[80px]"})]})})}function ie({open:s,onOpenChange:n,paymentMethodId:h,paymentMethodName:x,storeUids:p}){const{getCityById:c}=J(),{data:l,isLoading:m,error:g}=W({queryKey:[G.PAYMENT_METHODS_DETAIL,h],queryFn:async()=>await X.getPaymentMethodWithStores(p),enabled:s&&p.length>0,staleTime:5*60*1e3}),j=(l==null?void 0:l.stores)||[],o=r=>{const i=c(r);return(i==null?void 0:i.city_name)||r};return e.jsx(Z,{open:s,onOpenChange:n,children:e.jsxs(ee,{className:"max-w-2xl",children:[e.jsx(se,{children:e.jsx(te,{children:"Danh sách cửa hàng áp dụng"})}),e.jsx("div",{className:"space-y-4",children:m?e.jsx("div",{className:"space-y-3",children:Array.from({length:3}).map((r,i)=>e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(d,{className:"h-4 w-48"}),e.jsx(d,{className:"h-4 w-32"})]},i))}):g?e.jsx("p",{className:"text-sm text-red-600",children:"Có lỗi xảy ra khi tải danh sách cửa hàng"}):j.length===0?e.jsx("p",{className:"text-muted-foreground py-8 text-center text-sm",children:"Không có cửa hàng nào áp dụng phương thức thanh toán này"}):e.jsx("div",{className:"rounded-md border",children:e.jsx(w,{children:e.jsx(v,{children:j.map(r=>e.jsxs(f,{children:[e.jsx(a,{className:"font-medium",children:r.store_name}),e.jsx(a,{className:"font-mono text-sm",children:o(r.city_uid)})]},r.id))})})})})]})})}function ce({paymentMethod:s,index:n}){const h=b(),{company:x,brands:p}=E(t=>t.auth),c=p==null?void 0:p[0],[l,m]=S.useState(!1),[g,j]=S.useState(!1),{deletePaymentMethod:o,isDeleting:r}=I(),i=()=>{o(s.id),m(!1)},N=t=>t>0?`${(t*100).toFixed(0)}`:"0",D=t=>t>0?`${t} cửa hàng`:"Chưa áp dụng",M=()=>{j(!0)},_=()=>{const t=(x==null?void 0:x.id)||"",T=(c==null?void 0:c.id)||"";console.log("Row click data:",{paymentMethodId:s.id,paymentMethodCode:s.code,companyUid:t,brandUid:T}),t&&T&&h({to:"/setting/payment-method/detail/$paymentMethodId",params:{paymentMethodId:s.id},search:{company_uid:t,brand_uid:T,payment_method_id:s.code.toString()}})};return e.jsxs(f,{className:"cursor-pointer hover:bg-gray-50",onClick:_,children:[e.jsx(a,{className:"font-medium",children:n+1}),e.jsx(a,{className:"font-mono text-sm",children:s.code}),e.jsx(a,{children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:s.name}),s.description&&e.jsx("span",{className:"text-muted-foreground text-sm",children:s.description})]})}),e.jsx(a,{className:"text-center",children:e.jsx("span",{className:"font-medium",children:N(s.cardProcessingFee)})}),e.jsx(a,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm",children:D(s.storeCount)}),s.storeCount>0&&e.jsx(y,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:t=>{t.stopPropagation(),M()},children:e.jsx(ae,{className:"h-4 w-4"})})]})}),e.jsxs(a,{className:"text-right",children:[e.jsx(y,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700",onClick:t=>{t.stopPropagation(),m(!0)},children:e.jsx(ne,{className:"h-4 w-4"})}),e.jsx(Y,{open:l,onOpenChange:m,title:"Xác nhận xóa",content:`Bạn có chắc chắn muốn xóa phương thức thanh toán "${s.name}"? Hành động này không thể hoàn tác.`,onConfirm:i,confirmText:"Xóa",cancelText:"Hủy",isLoading:r}),e.jsx(ie,{open:g,onOpenChange:j,paymentMethodId:s.id,paymentMethodName:s.name,storeUids:s.storeUids})]})]})}function le(){return e.jsxs(e.Fragment,{children:[e.jsx(k,{}),e.jsx(v,{children:Array.from({length:5}).map((s,n)=>e.jsxs(f,{children:[e.jsx(a,{children:e.jsx(d,{className:"h-4 w-8"})}),e.jsx(a,{children:e.jsx(d,{className:"h-4 w-20"})}),e.jsx(a,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(d,{className:"h-4 w-32"}),e.jsx(d,{className:"h-3 w-24"})]})}),e.jsx(a,{children:e.jsx(d,{className:"h-4 w-12 mx-auto"})}),e.jsx(a,{children:e.jsx(d,{className:"h-4 w-24"})}),e.jsx(a,{children:e.jsx(d,{className:"h-8 w-8 ml-auto"})})]},n))})]})}function me({searchQuery:s,storeFilter:n}){const h=s||n&&n!=="all";return e.jsxs("div",{className:"flex flex-col items-center justify-center py-12 text-center",children:[e.jsx("div",{className:"text-muted-foreground mb-4",children:e.jsx("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})})}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:h?"Không tìm thấy phương thức thanh toán":"Chưa có phương thức thanh toán"}),e.jsx("p",{className:"text-muted-foreground text-sm max-w-sm",children:h?"Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm để xem kết quả khác.":"Bắt đầu bằng cách tạo phương thức thanh toán đầu tiên cho thương hiệu của bạn."})]})}function de(){const s=b(),[n,h]=C.useState(""),[x,p]=C.useState("all"),{data:c,isLoading:l,error:m}=F({searchTerm:n,storeUid:x}),{data:g=[]}=H(),j=()=>{s({to:"/setting/payment-method/detail"})};return e.jsxs(e.Fragment,{children:[e.jsx(R,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(A,{}),e.jsx(B,{}),e.jsx(O,{})]})}),e.jsx(L,{children:e.jsxs("div",{className:"container mx-auto space-y-6 py-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Danh sách PTTT"}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"text-muted-foreground absolute top-3 left-3 h-4 w-4"}),e.jsx($,{placeholder:"Tìm kiếm PTTT",value:n,onChange:o=>h(o.target.value),className:"pl-10"})]})}),e.jsxs(z,{value:x,onValueChange:p,children:[e.jsx(K,{className:"w-[200px]",children:e.jsx(U,{placeholder:"Tất cả các điểm"})}),e.jsxs(q,{children:[e.jsx(P,{value:"all",children:"Tất cả các điểm"}),g.map(o=>e.jsx(P,{value:o.id,children:o.name},o.id))]})]}),e.jsxs(y,{onClick:j,children:[e.jsx(oe,{className:"mr-2 h-4 w-4"}),"Tạo phương thức mới"]})]}),m&&e.jsx("p",{className:"text-sm text-red-600",children:m instanceof Error?m.message:"Có lỗi xảy ra"}),l&&e.jsx("div",{className:"rounded-md border",children:e.jsx(w,{children:e.jsx(le,{})})}),!l&&c.length===0&&e.jsx(me,{searchQuery:n,storeFilter:x}),!l&&c.length>0&&e.jsx(Q,{data:c,pageSize:20,children:(o,r)=>e.jsx("div",{className:"rounded-md border",children:e.jsxs(w,{children:[e.jsx(k,{}),e.jsx(v,{children:o.map((i,N)=>e.jsx(ce,{paymentMethod:i,index:r.startIndex+N},i.id))})]})})})]})})]})}const ds=function(){return e.jsx(de,{})};export{ds as component};
