import{aA as o,j as t}from"./index-B283E1a3.js";import{I as p}from"./item-detail-form-CN-7t28S.js";import"./form-dNL1hWKC.js";import"./pos-api-C7RsFAun.js";import{b as e}from"./use-items-in-store-data-BqQj7vLM.js";import"./user-Cxn8z8PZ.js";import"./vietqr-api-BbJFOv9v.js";import"./crm-api-CE_jLH-z.js";import"./header-BZ_7I_4c.js";import"./main-BlYSJOOd.js";import"./search-context-CZoJZmsi.js";import"./date-range-picker-B7aoXHt3.js";import"./price-source-dialog-D5GvY_-t.js";import"./multi-select-7h94IHsg.js";import"./exceljs.min-C48dW61m.js";import"./core.esm-BN1vgNRx.js";import"./zod-BOoGjb2n.js";import"./use-upload-image-BiKUodHD.js";import"./images-api-CKiFhZ_l.js";import"./use-item-types-ClqdZoOZ.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./query-keys-3lmd-xp6.js";import"./use-item-classes-TuVgnk5f.js";import"./use-units-5I1VqZ6k.js";import"./use-items-m6ZLkQ_w.js";import"./item-api-B8o6KNpl.js";import"./use-removed-items-D-t1fwGe.js";import"./use-customizations-BFIcwtLL.js";import"./use-customization-by-id-CI3Gk9XA.js";import"./use-sources-BglgZuAU.js";import"./sources-api-BYBGrmU9.js";import"./sources-CfiQ7039.js";import"./useCanGoBack-Dw8taFBS.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-DtNgKdj2.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./input-Bx4sCRS0.js";import"./textarea-hyg9uNcq.js";import"./combobox-Db0SwfY4.js";import"./command-C1ySvjo8.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";import"./popover-CCXriU_R.js";import"./chevrons-up-down-DYKAIzJg.js";import"./upload--UGrZUoh.js";import"./collapsible-lV085WFw.js";import"./confirm-dialog-CZ8uf1iG.js";import"./alert-dialog-BJpiIdrl.js";import"./circle-help-Q6UK66s-.js";import"./select-BzVwefGp.js";import"./index-Df0XEEuz.js";import"./chevron-right-CVT48KKP.js";import"./items-in-store-api-BbbXWOGT.js";import"./xlsx-DkH2s96g.js";import"./separator-DLHnMAQ0.js";import"./createReactComponent-WabRa4kY.js";import"./scroll-area-BlxlVxpe.js";import"./IconChevronRight-jxL9ONfH.js";import"./react-icons.esm-CwfFxlzT.js";import"./use-dialog-state-DpGk-Lpu.js";import"./modal-COeiv6He.js";import"./date-picker-zVQMDD0k.js";import"./calendar-ALtyELt3.js";import"./badge-DZXns0dL.js";import"./circle-x-CQicUDeF.js";const Rt=function(){const{id:r}=o({strict:!1}),{data:i,isLoading:m}=e(r,!!r);return m?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):i!=null&&i.data?t.jsx(p,{currentRow:i.data}):t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Không tìm thấy món ăn"})})})};export{Rt as component};
