import{u as n}from"./useQuery-Cc4LgMzN.js";import"./user-Cxn8z8PZ.js";import{l as o}from"./index-B283E1a3.js";import{c as s}from"./crm-api-CE_jLH-z.js";import"./pos-api-C7RsFAun.js";import{C as a}from"./query-keys-DQo7uRnN.js";import{c as i}from"./settings-api-bwwcX3l7.js";const g={getRegisterPageConfig:async e=>(await s.get("/marketing/get-config-register-page",{params:{pos_parent:e}})).data,saveRegisterPageConfig:async e=>(await s.post("/marketing/save-config-register-page",e)).data};function C(e={}){const{selectedBrand:r}=o(),t=e.pos_parent||(r==null?void 0:r.brandId)||"";return n({queryKey:["crm",a.REGISTER_PAGE_CONFIG,t],queryFn:async()=>g.getRegisterPageConfig(t),enabled:!!t})}function R(e){return n({queryKey:[a.POS_PARENT_SETTINGS,e],queryFn:()=>i.getCrmSettings(e.pos_parent),enabled:!!e.pos_parent})}export{R as a,g as m,C as u};
