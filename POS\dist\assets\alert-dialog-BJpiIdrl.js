import{r as s,j as t,I as A,C as w,F as O,as as $,c as i,n as D}from"./index-B283E1a3.js";import{d as m,R as M,W as I,a as F,T as z,D as L,b as v,P as G,O as W,c as H}from"./calendar-AxR9kFpj.js";var x="AlertDialog",[V,ge]=w(x,[m]),n=m(),N=e=>{const{__scopeAlertDialog:a,...o}=e,r=n(a);return t.jsx(M,{...r,...o,modal:!0})};N.displayName=x;var Y="AlertDialogTrigger",k=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(H,{...l,...r,ref:a})});k.displayName=Y;var q="AlertDialogPortal",y=e=>{const{__scopeAlertDialog:a,...o}=e,r=n(a);return t.jsx(G,{...r,...o})};y.displayName=q;var B="AlertDialogOverlay",j=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(W,{...l,...r,ref:a})});j.displayName=B;var c="AlertDialogContent",[J,K]=V(c),Q=$("AlertDialogContent"),_=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,children:r,...l}=e,p=n(o),g=s.useRef(null),T=A(a,g),u=s.useRef(null);return t.jsx(I,{contentName:c,titleName:b,docsSlug:"alert-dialog",children:t.jsx(J,{scope:o,cancelRef:u,children:t.jsxs(F,{role:"alertdialog",...p,...l,ref:T,onOpenAutoFocus:O(l.onOpenAutoFocus,d=>{var f;d.preventDefault(),(f=u.current)==null||f.focus({preventScroll:!0})}),onPointerDownOutside:d=>d.preventDefault(),onInteractOutside:d=>d.preventDefault(),children:[t.jsx(Q,{children:r}),t.jsx(X,{contentRef:g})]})})})});_.displayName=c;var b="AlertDialogTitle",h=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(z,{...l,...r,ref:a})});h.displayName=b;var C="AlertDialogDescription",R=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(L,{...l,...r,ref:a})});R.displayName=C;var U="AlertDialogAction",E=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(v,{...l,...r,ref:a})});E.displayName=U;var P="AlertDialogCancel",S=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,{cancelRef:l}=K(P,o),p=n(o),g=A(a,l);return t.jsx(v,{...p,...r,ref:g})});S.displayName=P;var X=({contentRef:e})=>{const a=`\`${c}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${c}\` by passing a \`${C}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${c}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null},Z=N,ee=y,ae=j,te=_,oe=E,re=S,le=h,se=R;function pe({...e}){return t.jsx(Z,{"data-slot":"alert-dialog",...e})}function ne({...e}){return t.jsx(ee,{"data-slot":"alert-dialog-portal",...e})}function ie({className:e,...a}){return t.jsx(ae,{"data-slot":"alert-dialog-overlay",className:i("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 bg-navy-blue-950/50 fixed inset-0 z-50",e),...a})}function ue({className:e,...a}){return t.jsxs(ne,{children:[t.jsx(ie,{}),t.jsx(te,{"data-slot":"alert-dialog-content",className:i("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...a})]})}function fe({className:e,...a}){return t.jsx("div",{"data-slot":"alert-dialog-header",className:i("flex flex-col gap-2 text-center sm:text-left",e),...a})}function Ae({className:e,...a}){return t.jsx("div",{"data-slot":"alert-dialog-footer",className:i("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function De({className:e,...a}){return t.jsx(le,{"data-slot":"alert-dialog-title",className:i("text-lg font-semibold",e),...a})}function me({className:e,...a}){return t.jsx(se,{"data-slot":"alert-dialog-description",className:i("text-muted-foreground text-sm",e),...a})}function ve({className:e,...a}){return t.jsx(oe,{className:i(D(),e),...a})}function xe({className:e,...a}){return t.jsx(re,{className:i(D({variant:"outline"}),e),...a})}export{pe as A,ue as a,fe as b,De as c,me as d,Ae as e,xe as f,ve as g};
