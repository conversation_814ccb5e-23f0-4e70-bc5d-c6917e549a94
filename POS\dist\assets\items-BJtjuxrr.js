import{j as e,B as M,r as N,z as _,a3 as Q,a4 as V}from"./index-B283E1a3.js";import{g as fe}from"./error-utils-CItCmAST.js";import{c as ve,a as Ie,D as we}from"./data-table-Ch4anmfN.js";import{u as J,F as Y,a as T,b as S,c as I,d as C,e as P}from"./form-dNL1hWKC.js";import{s as Z}from"./zod-BOoGjb2n.js";import{s as Te,t as Se,w as Ce,x as Me}from"./date-range-picker-B7aoXHt3.js";import{I as w}from"./input-Bx4sCRS0.js";import{T as D}from"./textarea-hyg9uNcq.js";import{C as H}from"./checkbox-DtNgKdj2.js";import{U as R}from"./upload--UGrZUoh.js";import{X as F}from"./calendar-AxR9kFpj.js";import{u as B}from"./useQuery-Cc4LgMzN.js";import{u as X}from"./use-items-m6ZLkQ_w.js";import{M as Pe}from"./multi-select-dropdown-CzM1yLux.js";import{C as ee}from"./combobox-Db0SwfY4.js";import{S as G,a as U,b as W,c as q,d as y}from"./select-BzVwefGp.js";import{P as ke}from"./plus-CKQNSsha.js";import{c as z}from"./crm-api-CE_jLH-z.js";import{u as te}from"./useMutation-Bf5OzDko.js";import"./table-pagination-CQwCsWP1.js";import"./pagination-Dms6Uzom.js";import"./react-icons.esm-CwfFxlzT.js";import"./table-vHuVXp9f.js";import"./badge-DZXns0dL.js";import"./isSameMonth-C8JQo-AN.js";import"./createLucideIcon-D6RMy2u2.js";import"./chevron-right-CVT48KKP.js";import"./popover-CCXriU_R.js";import"./index-CqlrRQAb.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./utils-km2FGkQ4.js";import"./item-api-B8o6KNpl.js";import"./pos-api-C7RsFAun.js";import"./query-keys-3lmd-xp6.js";import"./search-BKvg0ovQ.js";import"./command-C1ySvjo8.js";import"./dialog-BTZKnesd.js";import"./chevrons-up-down-DYKAIzJg.js";import"./index-Df0XEEuz.js";function se({open:t,onOpenChange:s,title:n,children:i,isLoading:r=!1,onSave:p,onCancel:g,saveText:j="Lưu thay đổi",cancelText:l="Hủy",showActions:d=!0}){const m=()=>{g?g():s(!1)};return e.jsx(Te,{open:t,onOpenChange:s,children:e.jsxs(Se,{className:"w-[55vw] max-w-none overflow-y-auto",side:"right",hideCloseButton:!0,children:[e.jsx(Ce,{className:"border-b pb-4",children:e.jsxs(Me,{className:"flex items-center justify-between gap-2",children:[e.jsx("span",{className:"text-lg font-semibold",children:n}),e.jsx(M,{variant:"default",size:"sm",onClick:p,disabled:r,children:r?"Đang cập nhật...":"Cập nhật"})]})}),e.jsx("div",{children:i}),d&&e.jsxs("div",{className:"mt-6 flex justify-end gap-3 border-t pt-4",children:[e.jsx(M,{variant:"outline",onClick:m,disabled:r,children:l}),e.jsx(M,{onClick:p,disabled:r,className:"bg-blue-600 hover:bg-blue-700",children:r?"Đang lưu...":j})]})]})})}const Ae=_.object({combo_id:_.string().min(1,"Mã combo là bắt buộc"),combo_name:_.string().min(1,"Tên combo là bắt buộc"),description:_.string().optional(),ots_price:_.number().min(0,"Giá tại chỗ phải lớn hơn hoặc bằng 0"),ta_price:_.number().min(0,"Giá mang đi phải lớn hơn hoặc bằng 0"),sort:_.number().min(1,"Thứ tự hiển thị phải lớn hơn 0"),allow_take_away:_.boolean(),ban_tai_cho:_.boolean(),ban_mang_di:_.boolean(),combo_image_path:_.string().optional()});function Ee({item:t,open:s,onOpenChange:n,onSave:i,isLoading:r,comboData:p=null}){const[g,j]=N.useState([]),l=J({resolver:Z(Ae),defaultValues:{combo_id:"",combo_name:"",description:"",ots_price:0,ta_price:0,sort:1e3,allow_take_away:!0,ban_tai_cho:!0,ban_mang_di:!0,combo_image_path:""}}),d=async()=>{const a=document.createElement("input");a.type="file",a.accept="image/*",a.onchange=async h=>{var x;const b=(x=h.target.files)==null?void 0:x[0];if(b)try{const k=URL.createObjectURL(b);l.setValue("combo_image_path",k)}catch{}},a.click()},m=()=>{l.setValue("combo_image_path","")};N.useEffect(()=>{j(t&&s&&p?p.combo_details||[]:[])},[t,s,p]),N.useEffect(()=>{t&&s&&l.reset({combo_id:t.Item_Id,combo_name:t.Item_Name,description:t.Description||"",ots_price:t.Ots_Price,ta_price:t.Ta_Price,sort:t.Sort,allow_take_away:t.Allow_Take_Away===1,ban_tai_cho:!0,ban_mang_di:t.Allow_Take_Away===1,combo_image_path:t.Item_Image_Path||""})},[t,s,l]);const o=a=>{i(a)},u=()=>{l.reset(),n(!1)},f=()=>{l.handleSubmit(o)()};return t?e.jsx(se,{open:s,onOpenChange:n,title:"CHỈNH SỬA",isLoading:r,onSave:f,onCancel:u,children:e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:e.jsxs("p",{className:"flex items-center gap-3 text-sm text-yellow-800",children:[e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-yellow-500 text-xs font-bold text-white",children:"!"}),"Nội dung hiện thi sẽ được cập nhật trên 3 cửa hàng"]})}),e.jsx(Y,{...l,children:e.jsxs("form",{onSubmit:l.handleSubmit(o),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(T,{control:l.control,name:"combo_id",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"MÃ COMBO"}),e.jsx(C,{children:e.jsx(w,{...a,placeholder:"Nhập mã combo",className:"h-10 bg-gray-100",readOnly:!0})}),e.jsx(P,{})]})}),e.jsx(T,{control:l.control,name:"combo_name",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"TÊN COMBO"}),e.jsx(C,{children:e.jsx(w,{...a,placeholder:"Nhập tên combo",className:"h-10"})}),e.jsx(P,{})]})}),e.jsx(T,{control:l.control,name:"description",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"MÔN ĐẠI DIỆN"}),e.jsx(C,{children:e.jsx(D,{...a,placeholder:"Nhập mô tả combo",className:"min-h-[80px] resize-none"})}),e.jsx(P,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(T,{control:l.control,name:"ban_tai_cho",render:({field:a})=>e.jsxs(S,{className:"flex flex-row items-center space-y-0 space-x-3",children:[e.jsx(C,{children:e.jsx(H,{checked:a.value,onCheckedChange:a.onChange})}),e.jsx(I,{className:"text-sm font-medium",children:"Bán tại chỗ"})]})}),e.jsx(T,{control:l.control,name:"ban_mang_di",render:({field:a})=>e.jsxs(S,{className:"flex flex-row items-center space-y-0 space-x-3",children:[e.jsx(C,{children:e.jsx(H,{checked:a.value,onCheckedChange:a.onChange})}),e.jsx(I,{className:"text-sm font-medium",children:"Bán mang đi"})]})})]})]}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"ẢNH MÓN"}),e.jsx("div",{className:"flex h-48 w-full items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50",children:l.watch("combo_image_path")?e.jsx("img",{src:l.watch("combo_image_path"),alt:l.watch("combo_name"),className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"text-center",children:[e.jsx(R,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Tải ảnh lên"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(M,{type:"button",variant:"outline",size:"sm",className:"flex-1",onClick:d,children:[e.jsx(R,{className:"mr-2 h-4 w-4"}),"Tải ảnh lên"]}),e.jsx(M,{type:"button",variant:"outline",size:"sm",onClick:m,children:e.jsx(F,{className:"h-4 w-4"})})]})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"MÓN TRONG COMBO"}),e.jsxs("div",{className:"rounded-lg border",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-4 bg-gray-50 p-4 text-sm font-medium text-gray-700",children:[e.jsx("div",{children:"Tên nhóm"}),e.jsx("div",{children:"Giá tại chỗ"}),e.jsx("div",{children:"Thứ tự"}),e.jsx("div",{children:"Giảm giá tại chỗ"}),e.jsx("div",{children:"Giá mang về"}),e.jsx("div",{children:"Giảm giá mang về"})]}),g.length>0?g.map((a,h)=>e.jsxs("div",{className:"grid grid-cols-6 gap-4 border-t p-4 text-sm",children:[e.jsx("div",{children:e.jsx(w,{value:`Nhóm ${h+1}`,className:"h-8 bg-gray-100",readOnly:!0})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{value:"0",className:"h-8 bg-gray-100",readOnly:!0}),e.jsx("span",{className:"text-xs text-gray-500",children:"VNĐ"})]}),e.jsx("div",{children:e.jsx(w,{value:a.min_permitted.toString(),className:"h-8 bg-gray-100",readOnly:!0})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{value:"0",className:"h-8 bg-gray-100",readOnly:!0}),e.jsx("span",{className:"text-xs text-gray-500",children:"%"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{value:"0",className:"h-8 bg-gray-100",readOnly:!0}),e.jsx("span",{className:"text-xs text-gray-500",children:"VNĐ"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{value:"0",className:"h-8 bg-gray-100",readOnly:!0}),e.jsx("span",{className:"text-xs text-gray-500",children:"%"})]})]},h)):e.jsx("div",{className:"border-t p-8 text-center text-gray-500",children:"Chưa có nhóm món nào trong combo"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"text-sm font-semibold text-gray-700",children:"Danh sách món"}),e.jsxs("div",{className:"rounded-lg border",children:[e.jsxs("div",{className:"grid grid-cols-5 gap-4 bg-gray-50 p-4 text-sm font-medium text-gray-700",children:[e.jsx("div",{className:"col-span-2",children:"Tên món"}),e.jsx("div",{children:"Giá tại chỗ"}),e.jsx("div",{children:"Giảm giá"}),e.jsx("div",{children:"Giá mang về"})]}),g.length>0?g.map((a,h)=>a.items.map((b,x)=>e.jsxs("div",{className:"grid grid-cols-5 gap-4 border-t p-4 text-sm",children:[e.jsxs("div",{className:"col-span-2 font-medium",children:[b.store_item_id," - ",b.name]}),e.jsxs("div",{children:[b.ots_price||0," VNĐ"]}),e.jsx("div",{children:"0%"}),e.jsxs("div",{children:[b.ta_price||0," VNĐ"]})]},`${h}-${x}`))):e.jsx("div",{className:"border-t p-8 text-center text-gray-500",children:"Chưa có món nào trong combo"})]})]})]})]})})]})}):null}const Oe={getAllItemTypes:async(t={})=>{try{const s=new URLSearchParams;s.append("page",(t.page||0).toString()),s.append("pos_parent",t.pos_parent||"default");const n={data:[]};return!n.data||!Array.isArray(n.data)?(console.warn("Invalid response format from CRM item types API:",n.data),[]):n.data}catch(s){return console.error("Error fetching CRM item types:",s),[]}}},Le=(t={},s=!0)=>B({queryKey:["crm-item-types",t],queryFn:()=>Oe.getAllItemTypes(t),enabled:s,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2}),ae=(t={})=>{const{data:s,...n}=Le(t),i=(s==null?void 0:s.map(r=>({value:r.Item_Type_Id,label:r.Item_Type_Name,data:r})))||[];return{...n,data:s,options:i}},ze=_.object({item_id:_.string().min(1,"Mã món không được để trống"),item_name:_.string().min(1,"Tên món không được để trống"),description:_.string().optional(),ots_price:_.number().min(0,"Giá tại chỗ phải >= 0"),ta_price:_.number().min(0,"Giá mang về phải >= 0"),sort:_.number().min(0,"Thứ tự hiển thị phải >= 0"),item_type_id:_.string().min(1,"Nhóm món không được để trống"),customizations:_.array(_.string()).optional(),allow_take_away:_.boolean(),allow_self_order:_.boolean(),is_eat_with:_.boolean(),item_image_path:_.string().optional()});function Ge({customizationData:t,isEatWith:s}){const{data:n}=X(),i=m=>{if(!m)return[];try{return JSON.parse(m).LstItem_Options||[]}catch{return[]}},[r,p]=N.useState(()=>{const m=i(t);return m.length>0?m:[]});N.useEffect(()=>{const m=i(t);p(m)},[t]);const g=()=>{p([...r,{Name:"",Min_Permitted:0,Max_Permitted:0,LstItem_Id:[]}])},j=m=>{p(r.filter((o,u)=>u!==m))},l=(m,o,u)=>{p(r.map((f,a)=>a===m?{...f,[o]:u}:f))},d=(m,o)=>{p(r.map((u,f)=>f===m?{...u,LstItem_Id:u.LstItem_Id.filter(a=>a!==o)}:u))};return s?null:e.jsxs("div",{className:"w-full space-y-6",children:[r.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("h3",{className:"text-sm font-semibold text-gray-700",children:"CUSTOMIZE"}),r.map((m,o)=>e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-5",children:[e.jsx("div",{className:"flex items-center",children:e.jsx(M,{type:"button",variant:"ghost",size:"sm",onClick:()=>j(o),className:"ml-auto h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700",children:e.jsx(F,{className:"h-4 w-4"})})}),e.jsxs("div",{className:"flex items-start justify-center gap-4",children:[e.jsxs("div",{className:"flex w-full flex-col gap-2",children:[e.jsx(w,{value:m.Name,onChange:u=>l(o,"Name",u.target.value),placeholder:"Tên nhóm",className:"h-10"}),e.jsx(w,{type:"number",value:m.Min_Permitted===0?"":m.Min_Permitted,onChange:u=>l(o,"Min_Permitted",Number(u.target.value)),placeholder:"Yêu cầu",className:"h-10"}),e.jsx(w,{type:"number",value:m.Max_Permitted===0?"":m.Max_Permitted,onChange:u=>l(o,"Max_Permitted",Number(u.target.value)),placeholder:"Tối đa",className:"h-10"})]}),e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"item-center flex justify-between",children:e.jsxs("label",{className:"block text-sm font-semibold text-gray-700",children:["DANH SÁCH MÓN ",e.jsx("span",{className:"text-red-600",children:"*"})]})}),e.jsx("div",{className:"min-h-[100px] rounded-lg border border-gray-200 bg-white p-4",children:e.jsx("div",{className:"flex flex-wrap gap-2",children:m.LstItem_Id.length>0?m.LstItem_Id.map(u=>{const f=n==null?void 0:n.find(h=>h.item_id===u),a=(f==null?void 0:f.item_name)||u;return e.jsxs("span",{className:"inline-flex items-center gap-2 rounded-full bg-blue-100 px-3 py-1.5 text-sm text-blue-800",children:[a,e.jsx(F,{className:"h-3 w-3 cursor-pointer transition-colors hover:text-red-500",onClick:()=>d(o,u)})]},u)}):e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"})})})]})]})]},o))]}),e.jsxs(M,{type:"button",variant:"outline",size:"sm",onClick:g,className:"flex h-9 items-center gap-2",children:[e.jsx(ke,{className:"h-4 w-4"}),"Thêm customize"]})]})}function Ue({item:t,open:s,onOpenChange:n,onSave:i,isLoading:r}){const{data:p=[]}=X(),{options:g,isLoading:j}=ae(),l=J({resolver:Z(ze),defaultValues:{item_id:"",item_name:"",description:"",ots_price:0,ta_price:0,sort:1e3,item_type_id:"",customizations:[],allow_take_away:!0,allow_self_order:!0,is_eat_with:!1,item_image_path:""}}),d=async()=>{const a=document.createElement("input");a.type="file",a.accept="image/*",a.onchange=async h=>{var x;const b=(x=h.target.files)==null?void 0:x[0];if(b)try{const k=URL.createObjectURL(b);l.setValue("item_image_path",k)}catch{}},a.click()},m=()=>{l.setValue("item_image_path","")};N.useEffect(()=>{if(t&&s){let a=[];if(t.Customizations)try{const h=JSON.parse(t.Customizations);h.LstItem_Options&&Array.isArray(h.LstItem_Options)&&(a=h.LstItem_Options.flatMap(b=>Array.isArray(b.LstItem_Id)?b.LstItem_Id:[]))}catch{}l.reset({item_id:t.Item_Id,item_name:t.Item_Name,description:t.Description||"",ots_price:t.Ots_Price,ta_price:t.Ta_Price,sort:t.Sort,item_type_id:t.Item_Type_Id,customizations:a,allow_take_away:t.Allow_Take_Away===1,allow_self_order:t.Allow_Self_Order===1,is_eat_with:t.Is_Eat_With===1,item_image_path:t.Item_Image_Path||""})}},[t,s,l]);const o=a=>{const h=a.customizations&&a.customizations.length>0?JSON.stringify({LstItem_Options:[{id:"CUS_GROUP_DEFAULT",Name:"Món ăn kèm",LstItem_Id:a.customizations,Max_Permitted:0,Min_Permitted:0}]}):"",b={...a,customizations:h};i(b)},u=()=>{l.reset(),n(!1)},f=()=>{l.handleSubmit(o)()};return t?e.jsx(se,{open:s,onOpenChange:n,title:"CHỈNH SỬA",isLoading:r,onSave:f,onCancel:u,children:e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:e.jsxs("p",{className:"flex items-center gap-3 text-sm text-yellow-800",children:[e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-yellow-500 text-xs font-bold text-white",children:"!"}),"Nội dung hiện thi sẽ được cập nhật trên 1 cửa hàng"]})}),e.jsx(Y,{...l,children:e.jsxs("form",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-2",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-4",children:e.jsxs("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"block font-medium text-gray-600",children:"MÃ MÓN"}),e.jsx("div",{className:"mt-2 font-mono font-semibold text-blue-600",children:t.Item_Id})]}),e.jsxs("div",{children:[e.jsx("span",{className:"block font-medium text-gray-600",children:"GIÁ TẠI CHỖ"}),e.jsxs("div",{className:"mt-2 font-semibold text-green-600",children:[t.Ots_Price.toLocaleString("vi-VN")," đ"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"block font-medium text-gray-600",children:"GIÁ MANG VỀ"}),e.jsxs("div",{className:"mt-2 font-semibold text-green-600",children:[t.Ta_Price.toLocaleString("vi-VN")," đ"]})]})]})}),e.jsx(T,{control:l.control,name:"item_name",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"TÊN MÓN *"}),e.jsx(C,{children:e.jsx(w,{...a,placeholder:"Nhập tên món",className:"h-10"})}),e.jsx(P,{})]})}),e.jsx(T,{control:l.control,name:"description",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"MÔ TẢ"}),e.jsx(C,{children:e.jsx(D,{...a,rows:4,placeholder:"Nhập mô tả món ăn",className:"resize-none"})}),e.jsx(P,{})]})}),e.jsx(T,{control:l.control,name:"sort",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"THỨ TỰ HIỂN THỊ"}),e.jsx(C,{children:e.jsx(w,{type:"number",...a,onChange:h=>a.onChange(Number(h.target.value)),className:"h-10"})}),e.jsx(P,{})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(T,{control:l.control,name:"allow_self_order",render:({field:a})=>e.jsxs(S,{className:"flex flex-row items-center space-y-0 space-x-3",children:[e.jsx(C,{children:e.jsx(H,{checked:a.value,onCheckedChange:a.onChange})}),e.jsx(I,{className:"text-sm font-medium text-gray-700",children:"Bán tại chỗ"})]})}),e.jsx(T,{control:l.control,name:"allow_take_away",render:({field:a})=>e.jsxs(S,{className:"flex flex-row items-center space-y-0 space-x-3",children:[e.jsx(C,{children:e.jsx(H,{checked:a.value,onCheckedChange:a.onChange})}),e.jsx(I,{className:"text-sm font-medium text-gray-700",children:"Bán mang đi"})]})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex h-48 w-full items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50",children:l.watch("item_image_path")?e.jsx("img",{src:l.watch("item_image_path"),alt:l.watch("item_name"),className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"text-center",children:[e.jsx(R,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Tải ảnh lên"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(M,{type:"button",variant:"outline",size:"sm",className:"flex-1",onClick:d,children:[e.jsx(R,{className:"mr-2 h-4 w-4"}),"Tải ảnh lên"]}),e.jsx(M,{type:"button",variant:"outline",size:"sm",onClick:m,children:e.jsx(F,{className:"h-4 w-4"})})]})]}),e.jsx(T,{control:l.control,name:"item_type_id",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"NHÓM MÓN"}),e.jsx(C,{children:e.jsx(ee,{options:g.map(h=>({value:h.value,label:h.label})),value:a.value,onValueChange:a.onChange,placeholder:j?"Đang tải...":"Chọn nhóm món",searchPlaceholder:"Tìm kiếm nhóm món...",emptyText:"Không tìm thấy nhóm món",disabled:j,className:"w-full"})}),e.jsx(P,{})]})}),e.jsx(T,{control:l.control,name:"is_eat_with",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"LOẠI MÓN"}),e.jsxs(G,{onValueChange:h=>a.onChange(h==="eat_with"),value:a.value?"eat_with":"normal",children:[e.jsx(C,{children:e.jsx(U,{className:"h-10 w-full",children:e.jsx(W,{})})}),e.jsxs(q,{children:[e.jsx(y,{value:"normal",children:"Món thường"}),e.jsx(y,{value:"eat_with",children:"Món ăn kèm"}),e.jsx(y,{value:"parent",children:"Món cha"}),e.jsx(y,{value:"child",children:"Món con"})]})]}),e.jsx(P,{})]})}),e.jsx(T,{control:l.control,name:"customizations",render:({field:a})=>e.jsxs(S,{children:[e.jsx(I,{className:"text-sm font-semibold text-gray-700",children:"DANH SÁCH MÓN ĂN KÈM"}),e.jsx(C,{children:e.jsx(Pe,{options:p.map(h=>({value:h.item_id,label:h.item_name})),value:a.value||[],onValueChange:a.onChange,placeholder:"Chọn món ăn kèm...",searchPlaceholder:"Tìm kiếm món ăn...",emptyText:"Không tìm thấy món ăn",className:"h-10"})}),e.jsx(P,{})]})})]})]}),e.jsx(Ge,{customizationData:t==null?void 0:t.Customizations,isEatWith:l.watch("is_eat_with")})]})})]})}):null}function We({filters:t,onFilterChange:s}){const{options:n,isLoading:i}=ae();return e.jsxs("div",{className:"mb-4 space-y-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx(M,{variant:t.activeTab==="menu"?"default":"outline",size:"sm",onClick:()=>s("activeTab","menu"),children:"Danh sách món"}),e.jsx(M,{variant:t.activeTab==="combo"?"default":"outline",size:"sm",onClick:()=>s("activeTab","combo"),children:"Danh sách combo"}),e.jsx(M,{variant:t.activeTab==="special-combo"?"default":"outline",size:"sm",onClick:()=>s("activeTab","special-combo"),children:"Danh sách combo tùy chỉnh"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"TÊN/MÃ MÓN"}),e.jsx(w,{placeholder:"Tìm kiếm tên món hoặc mã món (VD: Cà phê hoặc ITEM-123)",value:t.itemName,onChange:r=>s("itemName",r.target.value)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"NHÓM MÓN"}),e.jsx(ee,{options:[{value:"all",label:"Tất cả"},...n.map(r=>({value:r.value,label:r.label}))],value:t.itemGroup,onValueChange:r=>s("itemGroup",r||"all"),placeholder:i?"Đang tải...":"Chọn nhóm món",searchPlaceholder:"Tìm kiếm nhóm món...",emptyText:"Không tìm thấy nhóm món",disabled:i,className:"w-full"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"LOẠI MÓN"}),e.jsxs(G,{value:t.itemType,onValueChange:r=>s("itemType",r),children:[e.jsx(U,{className:"w-full",children:e.jsx(W,{placeholder:"Chọn loại món"})}),e.jsxs(q,{children:[e.jsx(y,{value:"all",children:"Tất cả"}),e.jsx(y,{value:"parent",children:"Món cha"}),e.jsx(y,{value:"child",children:"Món con"}),e.jsx(y,{value:"normal",children:"Món thường"}),e.jsx(y,{value:"eat_with",children:"Món ăn kèm"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"BÁN TẠI CHỖ"}),e.jsxs(G,{value:t.allowTakeAway,onValueChange:r=>s("allowTakeAway",r),children:[e.jsx(U,{className:"w-full",children:e.jsx(W,{placeholder:"Chọn"})}),e.jsxs(q,{children:[e.jsx(y,{value:"all",children:"Tất cả"}),e.jsx(y,{value:"yes",children:"Có"}),e.jsx(y,{value:"no",children:"Không"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:"BÁN MANG ĐI"}),e.jsxs(G,{value:t.allowSelfOrder,onValueChange:r=>s("allowSelfOrder",r),children:[e.jsx(U,{className:"w-full",children:e.jsx(W,{placeholder:"Chọn"})}),e.jsxs(q,{children:[e.jsx(y,{value:"all",children:"Tất cả"}),e.jsx(y,{value:"yes",children:"Có"}),e.jsx(y,{value:"no",children:"Không"})]})]})]})]})]})}const ne={getNormalCombos:async(t=1,s)=>{try{const n=new URLSearchParams;n.append("page",t.toString()),n.append("pos_parent",s||"");const i=await z.get(`/settings/get-normal-combos-by-pos-parent?${n.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from normal combos API");return i.data}catch{return await new Promise(i=>setTimeout(i,500)),{data:[],count:0,totalPage:0}}},getSpecialCombos:async(t=1,s)=>{try{const n=new URLSearchParams;n.append("page",t.toString()),n.append("pos_parent",s||"");const i=await z.get(`/settings/get-special-combos-by-pos-parent?${n.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from special combos API");return i.data}catch{return await new Promise(i=>setTimeout(i,500)),{data:[],count:0,totalPage:0}}}},qe=(t=1,s,n)=>B({queryKey:["normal-combos",t,s],queryFn:()=>ne.getNormalCombos(t,s),enabled:(n==null?void 0:n.enabled)!==!1&&!0,staleTime:0,gcTime:0,retry:!1,refetchOnWindowFocus:!1}),Ve=(t=1,s,n)=>B({queryKey:["special-combos",t,s],queryFn:()=>ne.getSpecialCombos(t,s),enabled:(n==null?void 0:n.enabled)!==!1&&!0,staleTime:0,gcTime:0,retry:!1,refetchOnWindowFocus:!1}),K={getMenuItems:async(t=0,s)=>{try{const n=new URLSearchParams;n.append("page",t.toString()),n.append("pos_parent",s||"");const i=await z.get(`/settings/get-items-by-pos-parent?${n.toString()}`);if(!i.data||typeof i.data!="object")throw new Error("Invalid response format from menu items API");return i.data}catch{return await new Promise(i=>setTimeout(i,500)),{count:0,totalPage:0,list_item:[]}}},updateMenuItem:async t=>{try{const s=new URLSearchParams;s.append("pos_parent",t.pos_parent||"");const n=await z.post(`/settings/post-sync?${s.toString()}`,t);if(!n.data||typeof n.data!="object")throw new Error("Invalid response format from update menu item API");return n.data}catch(s){throw s}},updateCombo:async t=>{try{const s=new URLSearchParams;s.append("pos_parent",t.pos_parent||"");const n=await z.post(`/settings/post-sync?${s.toString()}`,t);if(!n.data||typeof n.data!="object")throw new Error("Invalid response format from update combo API");return n.data}catch(s){throw s}}},He=(t=0,s,n)=>B({queryKey:["menu-items",t,s],queryFn:()=>K.getMenuItems(t,s),enabled:(n==null?void 0:n.enabled)!==!1&&!0,staleTime:5*60*1e3,retry:!1}),Re=()=>{const t=Q();return te({mutationFn:s=>K.updateMenuItem(s),onSuccess:()=>{V.success("Cập nhật món ăn thành công"),t.invalidateQueries({queryKey:["menu-items"]})},onError:()=>{V.error("Có lỗi xảy ra khi cập nhật món ăn")}})},Fe=()=>{const t=Q();return te({mutationFn:s=>K.updateCombo(s),onSuccess:()=>{V.success("Cập nhật combo thành công"),t.invalidateQueries({queryKey:["menu-items"]}),t.invalidateQueries({queryKey:["normal-combos"]}),t.invalidateQueries({queryKey:["special-combos"]})},onError:()=>{V.error("Có lỗi xảy ra khi cập nhật combo")}})},Be=t=>({Id:0,Pos_Id:0,Item_Name:t.combo_name,Item_Id:t.combo_id,Item_Type_Id:t.type,Item_Master_Id:0,Item_Type_Master_Id:0,Item_Image_Path:"",Item_Image_Path_Thumb:"",Last_Updated:t.update_at,Description:"",Description_Fb:"",Ots_Price:t.ots_value,Ta_Price:t.ta_value,Point:0,Is_Gift:0,Allow_Take_Away:t.allow_take_away,Show_On_Web:0,Special_Type:0,Show_Price_On_Web:0,Active:t.active,Is_Eat_With:0,Require_Eat_With:0,Item_Id_Eat_With:"",Sort:t.sort,Is_Featured:0,Is_Parent:0,Is_Sub:0,Time_Sale_Date_Week:0,Time_Sale_Hour_Day:0,Customizations:"",Allow_Self_Order:0}),Ke=t=>({Id:0,Pos_Id:0,Item_Name:t.combo_name,Item_Id:t.combo_id,Item_Type_Id:t.type,Item_Master_Id:0,Item_Type_Master_Id:0,Item_Image_Path:t.image||"",Item_Image_Path_Thumb:"",Last_Updated:t.update_at,Description:"",Description_Fb:"",Ots_Price:t.ots_value,Ta_Price:t.ta_value,Point:0,Is_Gift:0,Allow_Take_Away:t.allow_take_away,Show_On_Web:0,Special_Type:0,Show_Price_On_Web:0,Active:t.active,Is_Eat_With:0,Require_Eat_With:0,Item_Id_Eat_With:"",Sort:t.sort,Is_Featured:0,Is_Parent:0,Is_Sub:0,Time_Sale_Date_Week:t.date_time_week||0,Time_Sale_Hour_Day:t.hour_time_day||0,Customizations:"",Allow_Self_Order:0}),$e=(t,s)=>{if(!s)return!0;const n=s.toLowerCase(),i=t.Item_Name.toLowerCase().includes(n),r=t.Item_Id.toLowerCase().includes(n);return i||r},Qe=(t,s)=>!s||s==="all"?!0:t.Item_Type_Id===s,Je=(t,s)=>{if(!s||s==="all")return!0;const n=t.Is_Eat_With===1,i=t.Is_Eat_With===0&&t.Is_Parent===0&&t.Is_Sub===0,r=t.Is_Parent===1,p=t.Is_Sub===1;switch(s){case"eat_with":return n;case"normal":return i;case"parent":return r;case"child":return p;default:return!0}},Ye=(t,s)=>{if(!s||s==="all")return!0;const n=t.Allow_Take_Away===1;return s==="yes"?n:!n},Ze=(t,s)=>{if(!s||s==="all")return!0;const n=t.Allow_Self_Order===1;return s==="yes"?n:!n},De=t=>{const[s,n]=N.useState(0),[i,r]=N.useState(null),[p,g]=N.useState(null),[j,l]=N.useState(!1),[d,m]=N.useState({itemName:"",itemGroup:"all",itemType:"all",allowTakeAway:"all",allowSelfOrder:"all",activeTab:"menu"}),{data:o,isLoading:u,error:f}=He(s,t,{enabled:d.activeTab==="menu"}),{data:a,isLoading:h,error:b}=qe(s+1,t,{enabled:d.activeTab==="combo"}),{data:x,isLoading:k,error:re}=Ve(s+1,t,{enabled:d.activeTab==="special-combo"}),{mutate:le,isPending:ie}=Re(),{mutate:ce,isPending:oe}=Fe(),me=ie||oe,de=u||h||k,he=f||b||re,A=N.useMemo(()=>{const c=d.activeTab;return c==="menu"?(o==null?void 0:o.list_item)||[]:c==="combo"?((a==null?void 0:a.data)||[]).map(Be):c==="special-combo"?((x==null?void 0:x.data)||[]).map(Ke):[]},[d.activeTab,o==null?void 0:o.list_item,a==null?void 0:a.data,x==null?void 0:x.data]),ue=N.useMemo(()=>A.length?A.filter(c=>{const v=$e(c,d.itemName),E=Qe(c,d.itemGroup),O=Je(c,d.itemType),L=Ye(c,d.allowTakeAway),Ne=Ze(c,d.allowSelfOrder);return v&&E&&O&&L&&Ne}):[],[A,d]),_e=c=>{var v,E;if(r(c),d.activeTab==="combo"){const O=(v=a==null?void 0:a.data)==null?void 0:v.find(L=>L.combo_id===c.Item_Id);g(O)}else if(d.activeTab==="special-combo"){const O=(E=x==null?void 0:x.data)==null?void 0:E.find(L=>L.combo_id===c.Item_Id);g(O)}else g(null);l(!0)},pe=c=>{if(!i)return;const v={data:{Item_Id:c.item_id,Ots_Price:c.ots_price,Ta_Price:c.ta_price,Item_Name:c.item_name,Description:c.description||"@",Sort:c.sort,Item_Type_Id:c.item_type_id,Customizations:c.customizations||"",Is_Parent:0,Is_Sub:0,Is_Eat_With:c.is_eat_with?1:0,Item_Id_Eat_With:" ",Allow_Take_Away:c.allow_take_away?1:0,Allow_Self_Order:c.allow_self_order?1:0,Item_Image_Path:c.item_image_path||"",Item_Image_Path_Thumb:""},id_value:c.item_id,table_name:"DM_ITEM",list_pos:i.Pos_Id.toString(),pos_parent:t,sync_fields:"Item_Name,Description,Sort,Allow_Self_Order,Allow_Take_Away,Item_Type_Id,Is_Eat_With,Is_Parent,Is_Sub,Item_Id_Eat_With,Customizations,Item_Image_Path,Item_Image_Path_Thumb"};le(v,{onSuccess:()=>{l(!1),r(null)}})},xe=c=>{if(!i)return;const v={data:{Item_Id:c.combo_id,Ots_Price:c.ots_price,Ta_Price:c.ta_price,Item_Name:c.combo_name,Description:c.description||"@",Sort:c.sort,Item_Type_Id:i.Item_Type_Id,Customizations:"",Is_Parent:0,Is_Sub:0,Is_Eat_With:0,Item_Id_Eat_With:" ",Allow_Take_Away:c.allow_take_away?1:0,Allow_Self_Order:1,Item_Image_Path:c.combo_image_path||"",Item_Image_Path_Thumb:""},id_value:c.combo_id,table_name:"DM_ITEM",list_pos:i.Pos_Id.toString(),pos_parent:t,sync_fields:"Item_Name,Description,Sort,Allow_Self_Order,Allow_Take_Away,Item_Type_Id,Is_Eat_With,Is_Parent,Is_Sub,Item_Id_Eat_With,Customizations,Item_Image_Path,Item_Image_Path_Thumb"};ce(v,{onSuccess:()=>{l(!1),r(null)}})},ge=()=>{l(!1),r(null)},be=(c,v)=>{m(E=>({...E,[c]:v}))},je=()=>{m({itemName:"",itemGroup:"all",itemType:"all",allowTakeAway:"all",allowSelfOrder:"all",activeTab:"menu"})},ye=N.useMemo(()=>A.length?[...new Set(A.map(v=>v.Item_Type_Id))].filter(Boolean):[],[A]),$=N.useMemo(()=>d.activeTab==="menu"?(o==null?void 0:o.count)||0:d.activeTab==="combo"?(a==null?void 0:a.count)||0:d.activeTab==="special-combo"&&(x==null?void 0:x.count)||0,[d.activeTab,o==null?void 0:o.count,a==null?void 0:a.count,x==null?void 0:x.count]);return{items:ue,totalItems:$,totalPages:Math.ceil($/20),itemGroups:ye,isLoading:de,isUpdating:me,error:he,currentPage:s,setCurrentPage:n,filters:d,updateFilter:be,clearFilters:je,selectedItem:i,selectedComboData:p,isEditDialogOpen:j,handleEditItem:_e,handleSaveItem:pe,handleSaveCombo:xe,handleCloseEditDialog:ge}},Xe="BRAND-953H",et=20,tt="Đang tải...",st="Không có dữ liệu",at="Đang tải dữ liệu thực đơn...",nt=(t,s,n,i,r)=>{if(t)return e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:fe(t)})});if(s)return e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:at})});const p=i.activeTab==="menu"?ve(r):Ie(r);return e.jsx(we,{data:n,columns:p,isLoading:s,pageSize:et,emptyMessage:st,loadingMessage:tt})},rt=(t,s,n,i,r,p,g,j)=>{if(t.activeTab==="menu")return e.jsx(Ue,{item:s,open:n,onOpenChange:i,onSave:r,isLoading:g});const d=t.activeTab==="combo"?"combo":"special-combo";return e.jsx(Ee,{item:s,open:n,onOpenChange:i,onSave:p,isLoading:g,comboType:d,comboData:j})};function lt(){const{items:t,isLoading:s,isUpdating:n,error:i,filters:r,updateFilter:p,selectedItem:g,selectedComboData:j,isEditDialogOpen:l,handleEditItem:d,handleSaveItem:m,handleSaveCombo:o,handleCloseEditDialog:u}=De(Xe);return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"mb-4 flex items-center justify-between",children:e.jsx("h1",{className:"text-xl font-semibold",children:"Biên tập nội dung thực đơn"})}),e.jsx(We,{filters:r,onFilterChange:p}),nt(i,s,t,r,d),rt(r,g,l,u,m,o,n,j)]})}const $t=lt;export{$t as component};
