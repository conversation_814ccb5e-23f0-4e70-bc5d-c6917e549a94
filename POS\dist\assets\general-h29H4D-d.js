import{a as ee,b as Ne,a3 as Me,r as m,j as e,c as je,B as O,l as Ue}from"./index-B283E1a3.js";import{T as qe,a as $e,c as le,b as me}from"./tabs-CaBi0l4C.js";import{H as ze}from"./header-BZ_7I_4c.js";import{M as We}from"./main-BlYSJOOd.js";import{g as be,a as Ye,u as Be,c as Se,R as Qe,d as Xe,T as Je}from"./use-all-stores-Cl6GI3PI.js";import{P as Ze}from"./profile-dropdown-DhwpuuhW.js";import{S as es,T as ss}from"./search-eyocbSug.js";import{u as ts}from"./useQuery-Cc4LgMzN.js";import{r as Fe}from"./revenue-api-B-wLOX80.js";import{B as q}from"./badge-DZXns0dL.js";import{C as B,a as F,b as k,c as V,d as _}from"./card-Cd40a-ap.js";import{c as xe}from"./createLucideIcon-D6RMy2u2.js";import{R as ke,X as _e,Y as Ee,B as Ae,E as Pe,t as Ve}from"./generateCategoricalChart-BW0evHlU.js";import{B as Oe}from"./BarChart-B4P2-vOk.js";import{C as Le}from"./CartesianGrid-CQ-F-q-2.js";import{L as Z}from"./form-dNL1hWKC.js";import{S as ns,a as rs,b as as,c as cs,d as ge}from"./select-BzVwefGp.js";import{C as we}from"./calendar-AxR9kFpj.js";import{P as Te,a as De,b as Ce}from"./popover-CCXriU_R.js";import{C as Ie}from"./calendar-ALtyELt3.js";import{f as de,v as pe,l as os}from"./isSameMonth-C8JQo-AN.js";import{v as W}from"./date-range-picker-B7aoXHt3.js";import{u as ve}from"./useQueries-Dp1bFxoJ.js";import{s as is}from"./sale-sources-api-Di_47CuX.js";import{f as ls,a as ms}from"./stores-api-BC3ZNTx9.js";import{s as ds}from"./sales-api-JZqmL4cN.js";import{D as us}from"./dollar-sign-DZhJzamI.js";import"./index-BhFEt02S.js";import"./index-Df0XEEuz.js";import"./separator-DLHnMAQ0.js";import"./dropdown-menu-JDsssJHk.js";import"./index-CqlrRQAb.js";import"./check-CjIon4B5.js";import"./createReactComponent-WabRa4kY.js";import"./avatar-CfLE65or.js";import"./search-context-CZoJZmsi.js";import"./command-C1ySvjo8.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";import"./pos-api-C7RsFAun.js";import"./scroll-area-BlxlVxpe.js";import"./IconChevronRight-jxL9ONfH.js";import"./IconSearch-S0sgK6Kj.js";import"./utils-km2FGkQ4.js";import"./index-Chjiymov.js";import"./index-DY0KH0l4.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=[["line",{x1:"19",x2:"5",y1:"5",y2:"19",key:"1x9vlm"}],["circle",{cx:"6.5",cy:"6.5",r:"2.5",key:"4mh3h7"}],["circle",{cx:"17.5",cy:"17.5",r:"2.5",key:"1mdrzq"}]],X=xe("percent",hs);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xs=[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]],J=xe("ticket",xs);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]],Re=xe("trending-down",fs);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const js=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],He=xe("trending-up",js),he={all:["stores-discount"],lists:()=>[...he.all,"list"],list:s=>[...he.lists(),s]};function gs(s={}){const{dateRange:t,selectedStores:n=["all-stores"],autoFetch:c=!0}=s,{selectedBrand:h,currentBrandStores:x}=ee(),{company:o}=Ne(),i=Me(),j=m.useMemo(()=>{var d;return(d=t==null?void 0:t.from)==null?void 0:d.getTime()},[t==null?void 0:t.from]),y=m.useMemo(()=>{var d;return(d=t==null?void 0:t.to)==null?void 0:d.getTime()},[t==null?void 0:t.to]),S=h==null?void 0:h.id,l=o==null?void 0:o.id;m.useEffect(()=>{const d=()=>{i.invalidateQueries({queryKey:he.all})};return window.addEventListener("brandChanged",d),()=>{window.removeEventListener("brandChanged",d)}},[i]);const u=m.useMemo(()=>{if(n&&n.length>0&&!n.includes("all-stores"))return n.filter(d=>d!=="all-stores"&&d!=="no-stores");{const d=x.filter(p=>p.active===1);return d.length>0?d.map(p=>p.id):void 0}},[n,x]),{data:b,isLoading:w,error:v,refetch:I}=ts({queryKey:he.list({companyUid:l,brandUid:S,startDate:j,endDate:y,storeUids:u}),queryFn:async()=>{if(!S||!l||!j||!y)throw new Error("Brand, company, or date range not selected");return await Fe.getRevenueSummary({companyUid:l,brandUid:S,startDate:j,endDate:y,storeUids:u,byDays:0,limit:1e3})},enabled:c&&!!S&&!!l&&!!j&&!!y,staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(d,p)=>{var P,M;return(P=p==null?void 0:p.message)!=null&&P.includes("401")||(M=p==null?void 0:p.message)!=null&&M.includes("403")?!1:d<3}}),N=m.useMemo(()=>b!=null&&b.data?b.data.filter(d=>d.revenue_gross>0).map(d=>{const p=d.revenue_gross>0?d.discount_amount/d.revenue_gross*100:0;return{storeUid:d.store_uid,storeName:d.store_name,totalSales:d.total_sales||0,revenueGross:d.revenue_gross||0,discountAmount:d.discount_amount||0,discountRate:Math.round(p*100)/100,revenueNet:d.revenue_net||0,discountExtraAmount:d.discount_extra_amount||0,voucherAmount:d.voucher_amount||0,partnerMarketingAmount:d.partner_marketing_amount||0}}).sort((d,p)=>p.discountRate-d.discountRate):[],[b==null?void 0:b.data]),T=m.useMemo(()=>N.length,[N]),H=m.useMemo(()=>{if(N.length===0)return 0;const d=N.reduce((p,P)=>p+P.discountRate,0);return Math.round(d/N.length*100)/100},[N]);return{storesData:N,isLoading:w,error:(v==null?void 0:v.message)||null,totalStores:T,averageDiscountRate:H,refetch:()=>{I()}}}const ps=s=>s>=15?"#ef4444":s>=10?"#f97316":s>=5?"#eab308":"#22c55e",vs=({active:s,payload:t,label:n})=>{if(s&&t&&t.length){const c=t[0].payload;return e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white p-3 shadow-lg",children:[e.jsx("p",{className:"mb-2 font-medium text-gray-900",children:n}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-3 w-3 text-blue-500"}),e.jsxs("span",{children:["Tỷ lệ chiết khấu: ",e.jsxs("strong",{children:[c.discountRate,"%"]})]})]}),e.jsxs("p",{className:"text-gray-600",children:["Doanh thu gốc:"," ",new Intl.NumberFormat("vi-VN").format(c.revenueGross)," VNĐ"]}),e.jsxs("p",{className:"text-gray-600",children:["Chiết khấu:"," ",new Intl.NumberFormat("vi-VN").format(c.discountAmount)," VNĐ"]}),e.jsxs("p",{className:"text-gray-600",children:["Số đơn: ",new Intl.NumberFormat("vi-VN").format(c.totalSales)]})]})]})}return null};function Ns({dateRange:s,selectedStores:t,className:n}){const{storesData:c,isLoading:h,error:x,totalStores:o,averageDiscountRate:i}=gs({dateRange:s,selectedStores:t,autoFetch:!0}),j=m.useMemo(()=>c.map(l=>({name:l.storeName.length>15?`${l.storeName.substring(0,15)}...`:l.storeName,fullName:l.storeName,discountRate:l.discountRate,discountAmount:l.discountAmount,revenueGross:l.revenueGross,totalSales:l.totalSales,color:ps(l.discountRate)})),[c]),y=m.useMemo(()=>c.length>0?c[0].discountRate:0,[c]),S=m.useMemo(()=>c.length>0?c[c.length-1].discountRate:0,[c]);return h?e.jsxs(B,{className:n,children:[e.jsxs(F,{children:[e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-5 w-5"}),"Tỷ lệ chiết khấu theo cửa hàng"]}),e.jsx(V,{children:"Biểu đồ tỷ lệ chiết khấu của các cửa hàng"})]}),e.jsx(_,{children:e.jsx("div",{className:"flex h-80 items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-primary mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Đang tải dữ liệu..."})]})})})]}):x?e.jsxs(B,{className:n,children:[e.jsx(F,{children:e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-5 w-5"}),"Tỷ lệ chiết khấu theo cửa hàng"]})}),e.jsx(_,{children:e.jsx("div",{className:"flex h-80 items-center justify-center",children:e.jsxs("div",{className:"text-center text-red-500",children:[e.jsx("p",{className:"font-medium",children:"Lỗi tải dữ liệu"}),e.jsx("p",{className:"text-sm",children:x})]})})})]}):j.length===0?e.jsxs(B,{className:n,children:[e.jsx(F,{children:e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-5 w-5"}),"Tỷ lệ chiết khấu theo cửa hàng"]})}),e.jsx(_,{children:e.jsx("div",{className:"flex h-80 items-center justify-center",children:e.jsxs("div",{className:"text-muted-foreground text-center",children:[e.jsx("p",{className:"font-medium",children:"Không có dữ liệu"}),e.jsx("p",{className:"text-sm",children:"Chọn khoảng thời gian khác để xem dữ liệu"})]})})})]}):e.jsxs(B,{className:n,children:[e.jsxs(F,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(X,{className:"h-5 w-5"}),"Tỷ lệ chiết khấu theo cửa hàng"]}),e.jsx(V,{children:"Tất cả cửa hàng được sắp xếp theo tỷ lệ chiết khấu cao nhất"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(q,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(He,{className:"h-3 w-3 text-red-500"}),"Cao nhất: ",y,"%"]}),e.jsxs(q,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(Re,{className:"h-3 w-3 text-green-500"}),"Thấp nhất: ",S,"%"]})]})]}),e.jsxs("div",{className:"text-muted-foreground flex items-center gap-4 text-sm",children:[e.jsxs("span",{children:["Tổng số cửa hàng: ",o]}),e.jsxs("span",{children:["Tỷ lệ trung bình: ",i,"%"]})]})]}),e.jsxs(_,{children:[e.jsx("div",{className:"h-96 min-h-[400px]",children:e.jsx(ke,{width:"100%",height:"100%",children:e.jsxs(Oe,{data:j,margin:{top:20,right:30,left:20,bottom:80},children:[e.jsx(Le,{strokeDasharray:"3 3",className:"opacity-30"}),e.jsx(_e,{dataKey:"name",angle:-45,textAnchor:"end",height:100,fontSize:11,interval:0,tick:{fontSize:11}}),e.jsx(Ee,{fontSize:12,tickFormatter:l=>`${l}%`}),e.jsx(Ae,{content:e.jsx(vs,{})}),e.jsx(Pe,{dataKey:"discountRate",radius:[4,4,0,0],children:j.map((l,u)=>e.jsx(Ve,{fill:l.color},`cell-${u}`))})]})})}),e.jsxs("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-red-500"}),e.jsx("span",{children:"Cao (≥15%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-orange-500"}),e.jsx("span",{children:"Trung bình cao (10-15%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-yellow-500"}),e.jsx("span",{children:"Trung bình (5-10%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-green-500"}),e.jsx("span",{children:"Thấp (<5%)"})]})]})]})]})}function ys({dateRange:s,onDateRangeChange:t,filterType:n="daily",className:c}){const h=o=>{t(o(s))},x=(o,i)=>{if(o)if(n==="monthly")if(i==="from"){const j=pe(o);h(y=>({from:j,to:y.to}))}else{const j=os(o);h(y=>({from:y.from,to:j}))}else h(j=>({...j,[i]:o}))};return e.jsxs("div",{className:je("space-y-4",c),children:[e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Z,{className:"text-sm font-medium",children:n==="monthly"?"Từ tháng":"Từ ngày"}),e.jsxs(Te,{children:[e.jsx(De,{asChild:!0,children:e.jsxs(O,{variant:"outline",className:je("w-fit justify-start text-left font-normal",!s.from&&"text-muted-foreground"),children:[e.jsx(Ie,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:s.from?n==="monthly"?de(s.from,"MM/yyyy",{locale:W}):de(s.from,"dd/MM/yyyy",{locale:W}):n==="monthly"?"Chọn tháng bắt đầu":"Chọn ngày bắt đầu"})]})}),e.jsx(Ce,{className:"w-auto p-0",align:"start",children:e.jsx(we,{mode:"single",selected:s.from,onSelect:o=>x(o,"from"),initialFocus:!0,locale:W})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Z,{className:"text-sm font-medium",children:n==="monthly"?"Đến tháng":"Đến ngày"}),e.jsxs(Te,{children:[e.jsx(De,{asChild:!0,children:e.jsxs(O,{variant:"outline",className:je("w-fit justify-start text-left font-normal",!s.to&&"text-muted-foreground"),children:[e.jsx(Ie,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:s.to?n==="monthly"?de(s.to,"MM/yyyy",{locale:W}):de(s.to,"dd/MM/yyyy",{locale:W}):n==="monthly"?"Chọn tháng kết thúc":"Chọn ngày kết thúc"})]})}),e.jsx(Ce,{className:"w-auto p-0",align:"start",children:e.jsx(we,{mode:"single",selected:s.to,onSelect:o=>x(o,"to"),initialFocus:!0,locale:W,disabled:o=>n==="monthly"?pe(o)<pe(s.from):o<s.from})})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(Z,{className:"text-sm font-medium",children:"Chọn nhanh"}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:flex sm:flex-wrap",children:n==="daily"?e.jsxs(e.Fragment,{children:[e.jsx(O,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>t(be(7)),children:"7 ngày"}),e.jsx(O,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>t(be(28)),children:"28 ngày"}),e.jsx(O,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const o=new Date,i=new Date(o.getFullYear(),o.getMonth(),1);t({from:i,to:o})},children:"Tháng này"})]}):e.jsxs(e.Fragment,{children:[e.jsx(O,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>t(Ye()),children:"3 tháng gần đây"}),e.jsx(O,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const o=new Date,i=o.getFullYear(),j=new Date(i,o.getMonth()-6,1);t({from:j,to:o})},children:"6 tháng gần đây"})]})})]})]})}function bs({dateRange:s,onDateRangeChange:t,selectedStores:n,onStoreChange:c,filterType:h,onFilterTypeChange:x,className:o}){const{currentBrandStores:i}=ee(),{stores:j}=Be(),y=j.length>0?j:i,S=m.useMemo(()=>y.filter(l=>l.active===1),[y]);return e.jsx("div",{className:o,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(Z,{className:"text-sm font-medium",children:"Loại bộ lọc"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(O,{variant:h==="monthly"?"default":"outline",size:"sm",onClick:()=>x("monthly"),children:"Theo tháng"}),e.jsx(O,{variant:h==="daily"?"default":"outline",size:"sm",onClick:()=>x("daily"),children:"Theo ngày"})]})]}),e.jsx(ys,{dateRange:s,onDateRangeChange:t,filterType:h}),e.jsx("div",{className:"flex flex-wrap gap-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(Z,{htmlFor:"store-select",className:"text-sm font-medium",children:"Cửa hàng"}),e.jsxs(ns,{value:n[0]||"all-stores",onValueChange:l=>c([l]),children:[e.jsx(rs,{id:"store-select",className:"w-[180px]",children:e.jsx(as,{placeholder:"Chọn cửa hàng"})}),e.jsxs(cs,{children:[e.jsx(ge,{value:"all-stores",children:"Tất cả cửa hàng"}),S.map(l=>e.jsx(ge,{value:l.id,children:l.store_name},l.id)),S.length===0&&e.jsx(ge,{value:"no-stores",disabled:!0,children:"Không có cửa hàng"})]})]})]})})]})})}const Ss=["10000232","GRAPDHI"],ws=["BEFOOD","BE_TUTIMI","BE-DHI"],Ts=["10000169","NOWDHI"],Ds=["10000172"],ue={grab:Ss,be:ws,shopee:Ts,"tai-cho":Ds};function Cs({dateRange:s,selectedStores:t=["all-stores"],filterType:n="monthly",autoFetch:c=!0}){const{selectedBrand:h,currentBrandStores:x,currentBrandApiStores:o,setApiStores:i}=ee(),{company:j}=Ne(),y=Me(),S="269717a1-7bb6-4fa3-9150-dea2f709c081",l="8b8f15f9-6986-4c5a-86bc-f7dba8966659",u=(j==null?void 0:j.id)||S,b=(h==null?void 0:h.id)||l,w=m.useMemo(()=>b,[b]),v=m.useMemo(()=>u,[u]),I=m.useMemo(()=>n,[n]);m.useEffect(()=>{(async()=>{if(v&&w&&o.length===0)try{const f=await ls(v,w);i(f)}catch(f){console.log(f)}})()},[v,w,o.length,i]),m.useEffect(()=>{const a=()=>{y.invalidateQueries({queryKey:["sources-by-store-all"]})};return window.addEventListener("brandChanged",a),()=>{window.removeEventListener("brandChanged",a)}},[y]);const N=s!=null&&s.from?s.from.getTime():null,T=s!=null&&s.to?s.to.getTime():null,H=m.useMemo(()=>{var f;const a=o.length>0?o:x;return t.includes("all-stores")?(f=a==null?void 0:a.filter(E=>E.active===1))==null?void 0:f.slice(0,20):a==null?void 0:a.filter(E=>t.includes(E.id))},[t,x,o]),d=I==="daily"?1:0,p=ve({queries:(H||[]).map(a=>({queryKey:["sources-by-store-all",v,w,a.id,N,T,d,I],queryFn:async()=>{if(!w||!v||!N||!T)throw new Error("Missing required parameters");const f=await is.getSourcesSummary({companyUid:v,brandUid:w,startDate:N,endDate:T,storeUids:[a.id],byDays:d,limit:100});return{storeId:a.id,storeName:a.store_name||`Store ${a.id}`,sources:f.data||[]}},enabled:c&&!!w&&!!v&&!!N&&!!T,staleTime:2*60*1e3,gcTime:10*60*1e3,refetchOnWindowFocus:!1,retry:(f,E)=>{var $,L;return($=E==null?void 0:E.message)!=null&&$.includes("401")||(L=E==null?void 0:E.message)!=null&&L.includes("403")?!1:f<2}}))}),P=p.map(a=>({data:a.data,isLoading:a.isLoading,error:a.error})),M=m.useMemo(()=>{const a=[];return P.forEach(f=>{if(f.data){const{storeId:E,storeName:$,sources:L}=f.data,ne=L.filter(g=>ue.grab.includes(g.source_id)),re=L.filter(g=>ue.be.includes(g.source_id)),ae=L.filter(g=>ue.shopee.includes(g.source_id)),z=L.filter(g=>ue["tai-cho"].includes(g.source_id)),ce=ne.reduce((g,D)=>g+(D.revenue_gross||0),0),oe=re.reduce((g,D)=>g+(D.revenue_gross||0),0),ie=ae.reduce((g,D)=>g+(D.revenue_gross||0),0),r=z.reduce((g,D)=>g+(D.revenue_gross||0),0),C=ne.reduce((g,D)=>g+(D.total_bill||0),0),R=re.reduce((g,D)=>g+(D.total_bill||0),0),G=ae.reduce((g,D)=>g+(D.total_bill||0),0),Q=z.reduce((g,D)=>g+(D.total_bill||0),0);a.push({storeId:E,storeName:$,allSources:L,grabSources:ne,beSources:re,shopeeSources:ae,taiChoSources:z,grabRevenue:ce,beRevenue:oe,shopeeRevenue:ie,taiChoRevenue:r,grabTotalBill:C,beTotalBill:R,shopeeTotalBill:G,taiChoTotalBill:Q})}}),a},[P]),se=p.some(a=>a.isLoading),Y=p.some(a=>a.error),U=p.filter(a=>a.error).map(a=>a.error),te=m.useMemo(()=>({grab:{leastRevenue:[...M].sort((a,f)=>a.grabRevenue-f.grabRevenue),mostRevenue:[...M].sort((a,f)=>f.grabRevenue-a.grabRevenue)},be:{leastRevenue:[...M].sort((a,f)=>a.beRevenue-f.beRevenue),mostRevenue:[...M].sort((a,f)=>f.beRevenue-a.beRevenue)},shopee:{leastRevenue:[...M].sort((a,f)=>a.shopeeRevenue-f.shopeeRevenue),mostRevenue:[...M].sort((a,f)=>f.shopeeRevenue-a.shopeeRevenue)},taiCho:{leastRevenue:[...M].sort((a,f)=>a.taiChoRevenue-f.taiChoRevenue),mostRevenue:[...M].sort((a,f)=>f.taiChoRevenue-a.taiChoRevenue)}}),[M]);return{storesData:M,platformRankings:te,isLoading:se,hasError:Y,errors:U,refetch:async()=>{const a=p.map(f=>f.refetch());await Promise.allSettled(a)},queryInfo:{brandId:w,companyId:v,filterType:I,storeCount:(H==null?void 0:H.length)||0}}}function Is(s){return new Intl.NumberFormat("vi-VN").format(s)}function Ms(s){return["10000232","GRAPDHI"].includes(s)?"bg-green-100 text-green-800":["BEFOOD","BE_TUTIMI","BE-DHI"].includes(s)?"bg-blue-100 text-blue-800":["10000169","NOWDHI"].includes(s)?"bg-orange-100 text-orange-800":["10000172"].includes(s)?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}function K({dateRange:s,selectedStores:t=["all-stores"],filterType:n="monthly",sourceId:c,sourceName:h,type:x="least-revenue",limit:o=5,className:i}){const{storesData:j,isLoading:y,hasError:S}=Cs({dateRange:s,selectedStores:t,filterType:n,autoFetch:!0}),b=[...j.map(v=>{const I=v.allSources.filter(N=>N.source_id===c).reduce((N,T)=>N+(T.revenue_gross||0),0);return{...v,sourceRevenue:I}})].sort((v,I)=>x==="least-revenue"?v.sourceRevenue-I.sourceRevenue:I.sourceRevenue-v.sourceRevenue).slice(0,o),w=h||c;return y?e.jsx("div",{className:i,children:e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((v,I)=>e.jsxs("div",{className:"flex animate-pulse items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"bg-muted h-6 w-6 rounded-full"}),e.jsx("div",{className:"bg-muted h-4 w-32 rounded"})]}),e.jsx("div",{className:"bg-muted h-4 w-16 rounded"})]},I))})}):S?e.jsx("div",{className:i,children:e.jsxs("div",{className:"text-center text-sm text-red-500",children:["Lỗi tải dữ liệu ",w]})}):b.length===0?e.jsx("div",{className:i,children:e.jsxs("div",{className:"text-muted-foreground text-center text-sm",children:["Không có dữ liệu ",w]})}):e.jsx("div",{className:i,children:e.jsx("div",{className:"space-y-3",children:b.map((v,I)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex min-w-0 flex-1 items-center space-x-3",children:[e.jsx(q,{variant:I===0?"destructive":I===1?"secondary":"outline",className:"flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full p-0 text-xs font-medium",children:I+1}),e.jsx("div",{className:"min-w-0 flex-1",children:e.jsx("div",{className:"cursor-help truncate text-sm font-medium",title:v.storeName,children:v.storeName})})]}),e.jsx("div",{className:"flex flex-shrink-0 items-center justify-end",children:e.jsx(q,{variant:"outline",className:`text-xs font-bold whitespace-nowrap ${v.sourceRevenue===0?"border-gray-200 bg-gray-50 text-gray-400":Ms(c)}`,children:v.sourceRevenue===0?"0 ₫":Is(v.sourceRevenue)})})]},v.storeId))})})}function Bs(s){return e.jsx(K,{...s,sourceId:"10000232",sourceName:"Grab (10000232)"})}function Fs(s){return e.jsx(K,{...s,sourceId:"GRAPDHI",sourceName:"Grab (GRAPDHI)"})}function ks(s){return e.jsx(K,{...s,sourceId:"BEFOOD",sourceName:"Be (BEFOOD)"})}function _s(s){return e.jsx(K,{...s,sourceId:"BE_TUTIMI",sourceName:"Be (BE_TUTIMI)"})}function Es(s){return e.jsx(K,{...s,sourceId:"BE-DHI",sourceName:"Be (BE-DHI)"})}function As(s){return e.jsx(K,{...s,sourceId:"10000169",sourceName:"Shopee (10000169)"})}function Ps(s){return e.jsx(K,{...s,sourceId:"NOWDHI",sourceName:"Shopee (NOWDHI)"})}function Vs(s){return e.jsx(K,{...s,sourceId:"10000172",sourceName:"Tại Chỗ (10000172)"})}function Os({dateRange:s,selectedStores:t=["all-stores"],filterType:n="monthly"}){const{currentBrandStores:c,selectedBrand:h}=ee(),{stores:x}=Be(),i=(h==null?void 0:h.id)==="5ed8968a-e4ed-4a04-870d-b53b7758fdc7",j=x.length>0?x:c,y=m.useMemo(()=>{if(t.includes("all-stores"))return"Tất cả cửa hàng";const S=t[0],l=j.find(u=>u.id===S);return(l==null?void 0:l.store_name)||`Store ${S}`},[t,j]);return e.jsxs("div",{className:"space-y-4 sm:space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Top Cửa Hàng Theo Source"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Cửa hàng:"}),e.jsx(q,{variant:"outline",className:"text-xs",children:y})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:[e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-green-700",children:"GRABFOOD"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(Bs,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]}),!i&&e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-green-700",children:"GRAB - DHI"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(Fs,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]}),i&&e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-blue-700",children:"BEFOOD"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(ks,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]}),!i&&e.jsxs(e.Fragment,{children:[e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-blue-700",children:"BE - TUTIMI"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(_s,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-blue-700",children:"BE - DHI"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(Es,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-orange-700",children:"ShopeeFood"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(As,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]}),!i&&e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-orange-700",children:"ShopeeFood - DHI"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(Ps,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]}),e.jsxs(B,{className:"h-full",children:[e.jsxs(F,{className:"pb-3",children:[e.jsx(k,{className:"text-sm font-medium text-red-700",children:"TẠI CHỖ"}),e.jsx(V,{className:"text-xs",children:"Doanh số thấp nhất"})]}),e.jsx(_,{className:"pt-0",children:e.jsx(Vs,{dateRange:s,selectedStores:t,filterType:n,type:"least-revenue",limit:5})})]})]})]})}const Ls=["FB100SK","FBGTVM","FBUPXL5"],Rs=(s,t)=>`sales-voucher-data-${s}-${t}`,Hs=(s,t)=>{try{localStorage.setItem(s,JSON.stringify(t))}catch{}},Gs=s=>{try{const t=localStorage.getItem(s);if(t){const n=JSON.parse(t),c=24*60*60*1e3;if(Date.now()-n.timestamp<c)return n}}catch{}return null};function Ks({dateRange:s,selectedStores:t=["all-stores"],voucherCodes:n=Ls,autoFetch:c=!0}){var z,ce,oe,ie;const{selectedBrand:h,currentBrandApiStores:x}=ee(),{company:o}=Ne(),i="269717a1-7bb6-4fa3-9150-dea2f709c081",j="5ed8968a-e4ed-4a04-870d-b53b7758fdc7",y=(o==null?void 0:o.id)||i,S=(h==null?void 0:h.id)||j,l=m.useMemo(()=>S,[S]),u=m.useMemo(()=>y,[y]),[b,w]=m.useState(null),[v,I]=m.useState(!0),{startTime:N,endTime:T}=m.useMemo(()=>{if(!(s!=null&&s.from)||!(s!=null&&s.to))return{startTime:null,endTime:null};const r=new Date(s.from);r.setHours(0,0,0,0);const C=new Date(s.to);return C.setHours(23,59,59,999),{startTime:r.getTime(),endTime:C.getTime()}},[s]),H=m.useMemo(()=>{const r=new Map;return x==null||x.forEach(C=>{r.set(C.id,C.store_name)}),r},[x]),d=m.useMemo(()=>!x||x.length===0?[]:t.includes("all-stores")?x.map(r=>r.id):t.filter(r=>x.some(C=>C.id===r)),[x,t]),p=m.useMemo(()=>!N||!T?null:Rs(N,T),[N,T]);m.useEffect(()=>{if(p){const r=Gs(p);r&&w(r)}I(!1)},[p]);const P=ve({queries:d.map(r=>({queryKey:["sales-voucher-data",u,l,r,N,T,n],queryFn:async()=>{if(!l||!u||!N||!T)throw new Error("Missing required parameters");const C=await ds.getVoucherSummary({companyUid:u,brandUid:l,listStoreUid:r,startDate:N,endDate:T,voucherCodes:n,sourceId:10000172}),R=H.get(r),G=x==null?void 0:x.find(A=>A.id===r),g=ms().find(A=>A.id===r),D=R||(G==null?void 0:G.store_name)||(g==null?void 0:g.store_name)||C.storeName||`Store ${r}`;return{storeId:r,storeName:D,voucherSummary:C,hasVouchers:C.totalAmountOrigin>0}},enabled:c&&!!l&&!!u&&!!N&&!!T&&n.length>0,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchInterval:15*60*1e3,refetchIntervalInBackground:!0,retry:2}))}),M=P.map(r=>r.data),se=P.map(r=>r.status),Y=m.useMemo(()=>M.filter((r,C)=>(r==null?void 0:r.hasVouchers)&&se[C]==="success").filter(r=>!!r),[M,se]),U=ve({queries:Y.map(r=>({queryKey:["store-revenue",u,l,r.storeId,N,T],queryFn:async()=>{if(!l||!u||!N||!T)throw new Error("Missing required parameters");const R=(await Fe.getRevenueSummary({companyUid:u,brandUid:l,startDate:N,endDate:T,storeUids:[r.storeId],byDays:0,limit:1e3})).data[0];return{storeId:r.storeId,totalRevenueGross:(R==null?void 0:R.revenue_gross)||0}},enabled:c&&!!l&&!!u&&!!N&&!!T,staleTime:5*60*1e3,gcTime:10*60*1e3,refetchInterval:15*60*1e3,refetchIntervalInBackground:!0,retry:2}))}),te=U.map(r=>r.data),a=U.map(r=>r.status),f=m.useMemo(()=>{const r=[];let C=0,R=0,G=0;Y.forEach((g,D)=>{const{voucherSummary:A}=g,ye=te[D],Ge=a[D]==="success"&&ye;if(A.totalAmountOrigin>0&&Ge){const fe=ye.totalRevenueGross,Ke=fe>0?A.totalAmountOrigin/fe*100:0;r.push({storeUid:g.storeId,storeName:g.storeName,totalTransactions:A.transactionCount,totalPrice:A.totalAmountOrigin,totalAmount:A.totalNetAmount,revenueGross:fe,discountAmount:A.totalDiscountAmount,voucherPercentage:Ke,voucherSales:A.voucherSales}),C+=A.totalAmountOrigin,R+=A.totalNetAmount,G+=A.transactionCount}});const Q={data:r,totalPrice:C,totalAmount:R,totalTransactions:G};if(p&&r.length>0){const g={...Q,timestamp:Date.now()};Hs(p,g)}return Q},[Y,te,a,p]),E=P.some(r=>r.isLoading)||U.some(r=>r.isLoading),$=((ce=(z=P.find(r=>r.error))==null?void 0:z.error)==null?void 0:ce.message)||((ie=(oe=U.find(r=>r.error))==null?void 0:oe.error)==null?void 0:ie.message)||null,L=()=>{P.forEach(r=>r.refetch()),U.forEach(r=>r.refetch())};return{...f.data.length>0?f:b||f,isLoading:v||E,error:$,refetch:L}}const Us=s=>s>=15?"#dc2626":s>=10?"#ea580c":s>=5?"#ca8a04":s>=2?"#16a34a":"#0ea5e9",qs=({active:s,payload:t,label:n})=>{if(s&&t&&t.length){const c=t[0].payload;return e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white p-3 shadow-lg",children:[e.jsx("p",{className:"mb-2 font-medium text-gray-900",children:n}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("p",{className:"flex items-center gap-2",children:[e.jsx(J,{className:"h-3 w-3 text-blue-500"}),e.jsxs("span",{children:["Tỷ lệ voucher: ",e.jsxs("strong",{children:[c.voucherPercentage,"%"]})]})]}),e.jsxs("p",{className:"text-gray-600",children:["Doanh thu cửa hàng:"," ",new Intl.NumberFormat("vi-VN").format(c.revenueGross)," VNĐ"]}),e.jsxs("p",{className:"text-gray-600",children:["Voucher gốc:"," ",new Intl.NumberFormat("vi-VN").format(c.totalPrice)," VNĐ"]}),e.jsxs("p",{className:"text-gray-600",children:["Số giao dịch:"," ",new Intl.NumberFormat("vi-VN").format(c.totalTransactions)]})]})]})}return null};function $s({dateRange:s,selectedStores:t,className:n}){const{data:c,totalPrice:h,isLoading:x,error:o}=Ks({dateRange:s,selectedStores:t,voucherCodes:["FB100SK","FBGTVM","FBUPXL5"],autoFetch:!0}),i=m.useMemo(()=>!c||!Array.isArray(c)?[]:c.filter(u=>u.totalPrice>0&&u.revenueGross&&u.revenueGross>0).map(u=>{const b=u.voucherPercentage||0,w=u.storeName||"Unknown Store";return{name:w.length>15?`${w.substring(0,15)}...`:w,fullName:w,voucherPercentage:Number(b.toFixed(2)),totalPrice:u.totalPrice,revenueGross:u.revenueGross,totalTransactions:u.totalTransactions,color:Us(b)}}).sort((u,b)=>b.voucherPercentage-u.voucherPercentage),[c]),j=m.useMemo(()=>{if(i.length===0)return 0;const u=i.reduce((b,w)=>b+w.voucherPercentage,0);return Number((u/i.length).toFixed(2))},[i]),y=m.useMemo(()=>i.length>0?i[0].voucherPercentage:0,[i]),S=m.useMemo(()=>i.length>0?i[i.length-1].voucherPercentage:0,[i]),l=m.useMemo(()=>i.reduce((u,b)=>u+b.totalPrice,0),[i]);return x||(c==null?void 0:c.length)===0?e.jsxs(B,{className:n,children:[e.jsxs(F,{children:[e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(J,{className:"h-5 w-5"}),"Tỷ lệ Voucher Thành Viên theo Cửa hàng"]}),e.jsx(V,{children:"Biểu đồ tỷ lệ giảm giá voucher theo cửa hàng"})]}),e.jsx(_,{children:e.jsx("div",{className:"flex h-80 items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-primary mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Đang tải dữ liệu..."})]})})})]}):o?e.jsxs(B,{className:n,children:[e.jsx(F,{children:e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(J,{className:"h-5 w-5"}),"Tỷ lệ Voucher Thành Viên"]})}),e.jsx(_,{children:e.jsx("div",{className:"flex h-80 items-center justify-center",children:e.jsxs("div",{className:"text-center text-red-500",children:[e.jsx("p",{className:"font-medium",children:"Lỗi tải dữ liệu"}),e.jsx("p",{className:"text-sm",children:o})]})})})]}):i.length===0?e.jsxs(B,{className:n,children:[e.jsx(F,{children:e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(J,{className:"h-5 w-5"}),"Tỷ lệ Voucher Thành Viên"]})}),e.jsx(_,{children:e.jsx("div",{className:"flex h-80 items-center justify-center",children:e.jsxs("div",{className:"text-muted-foreground text-center",children:[e.jsx("p",{className:"font-medium",children:"Không có dữ liệu voucher"}),e.jsx("p",{className:"text-sm",children:"Không tìm thấy giao dịch nào với voucher codes: FB100SK, FBGTVM, FBUPXL5"})]})})})]}):e.jsxs(B,{className:n,children:[e.jsxs(F,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs(k,{className:"flex items-center gap-2",children:[e.jsx(J,{className:"h-5 w-5"}),"Tỷ lệ Voucher Thành Viên"]}),e.jsx(V,{children:"Voucher Codes: FB100SK, FBGTVM, FBUPXL5 - Sắp xếp theo tỷ lệ cao nhất"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(q,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(He,{className:"h-3 w-3 text-red-500"}),"Cao nhất: ",y,"%"]}),e.jsxs(q,{variant:"outline",className:"flex items-center gap-1",children:[e.jsx(Re,{className:"h-3 w-3 text-green-500"}),"Thấp nhất: ",S,"%"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(us,{className:"h-4 w-4 text-blue-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Tổng voucher"}),e.jsxs("p",{className:"font-medium",children:[new Intl.NumberFormat("vi-VN").format(h)," VNĐ"]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Tổng giảm giá"}),e.jsxs("p",{className:"font-medium",children:[new Intl.NumberFormat("vi-VN").format(l)," VNĐ"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Tỷ lệ trung bình"}),e.jsxs("p",{className:"font-medium",children:[j,"%"]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Số cửa hàng"}),e.jsx("p",{className:"font-medium",children:i.length})]})]})]}),e.jsxs(_,{children:[e.jsx("div",{className:"h-96 min-h-[400px]",children:e.jsx(ke,{width:"100%",height:"100%",children:e.jsxs(Oe,{data:i,margin:{top:20,right:30,left:20,bottom:80},children:[e.jsx(Le,{strokeDasharray:"3 3",className:"opacity-30"}),e.jsx(_e,{dataKey:"name",angle:-45,textAnchor:"end",height:100,fontSize:11,interval:0,tick:{fontSize:11}}),e.jsx(Ee,{fontSize:12,tickFormatter:u=>`${u}%`}),e.jsx(Ae,{content:e.jsx(qs,{})}),e.jsx(Pe,{dataKey:"voucherPercentage",radius:[4,4,0,0],children:i.map((u,b)=>e.jsx(Ve,{fill:u.color},`cell-${b}`))})]})})}),e.jsxs("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-red-600"}),e.jsx("span",{children:"Rất cao (≥15%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-orange-600"}),e.jsx("span",{children:"Cao (10-15%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-yellow-600"}),e.jsx("span",{children:"Trung bình cao (5-10%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-green-600"}),e.jsx("span",{children:"Trung bình (2-5%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-3 w-3 rounded bg-blue-500"}),e.jsx("span",{children:"Thấp (<2%)"})]})]})]})]})}function zs(){const[s,t]=m.useState("monthly"),[n,c]=m.useState(Se()),[h,x]=m.useState(["all-stores"]),[o,i]=m.useState(["all-sources"]),j=S=>{t(S),S==="monthly"?c(Se()):S==="daily"&&c(Xe())},y=m.useMemo(()=>({dateRange:n,filterType:s,selectedStores:h,selectedSources:o}),[n,s,h,o]);return e.jsx(Qe.Provider,{value:y,children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(bs,{dateRange:n,onDateRangeChange:c,selectedStores:h,onStoreChange:x,selectedSources:o,onSourceChange:i,filterType:s,onFilterTypeChange:j}),e.jsxs("div",{className:"grid gap-6",children:[e.jsx("div",{className:"col-span-full",children:e.jsx(Ns,{dateRange:n,selectedStores:h,className:"w-full"})}),e.jsx("div",{className:"col-span-full",children:e.jsx($s,{dateRange:n,selectedStores:h,className:"w-full"})}),e.jsx("div",{className:"col-span-full",children:e.jsx(Os,{dateRange:n,selectedStores:h,filterType:s})})]})]})})}function Ws(){const{selectedBrand:s}=Ue(),t=s==null?void 0:s.name,n=t?`Báo Cáo Kiểm Soát ${t}`:"Báo Cáo Kiểm Soát";return e.jsxs(e.Fragment,{children:[e.jsxs(ze,{children:[e.jsx(Je,{links:Ys}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(es,{}),e.jsx(ss,{}),e.jsx(Ze,{})]})]}),e.jsxs(We,{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between space-y-2",children:[e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:n}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{variant:"outline",children:"Xuất Excel"}),e.jsx(O,{children:"Tải Báo Cáo"})]})]}),e.jsxs(qe,{orientation:"vertical",defaultValue:"overview",className:"space-y-4",children:[e.jsx("div",{className:"w-full overflow-x-auto pb-2",children:e.jsxs($e,{children:[e.jsx(le,{value:"overview",children:"Tổng Quan"}),e.jsx(le,{value:"stores",children:"Theo Cửa Hàng"}),e.jsx(le,{value:"products",children:"Theo Sản Phẩm"}),e.jsx(le,{value:"time",children:"Theo Thời Gian"})]})}),e.jsx(me,{value:"overview",className:"space-y-4",children:e.jsx(zs,{})}),e.jsx(me,{value:"stores",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo sản phẩm"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(me,{value:"products",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo sản phẩm"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(me,{value:"time",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo thời gian"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})})]})]})]})}const Ys=[{title:"Tổng Quan",href:"/bao-cao/doanh-thu",isActive:!0,disabled:!1},{title:"Doanh Thu Net",href:"/bao-cao/doanh-thu/net",isActive:!1,disabled:!0},{title:"Theo Cửa Hàng",href:"/bao-cao/doanh-thu/cua-hang",isActive:!1,disabled:!0},{title:"Theo Khu Vực",href:"/bao-cao/doanh-thu/khu-vuc",isActive:!1,disabled:!0}],Ut=Ws;export{Ut as component};
