import { useState, useRef } from 'react'

import { DownloadIcon, UploadIcon } from '@radix-ui/react-icons'

import { toast } from 'sonner'
import * as XLSX from 'xlsx'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

import { useItemsInCity } from '../../../context'
import { useDownloadImportTemplate } from '../../../hooks'
import { generateItemId } from '../../../utils'
import { ExcelPreviewImportDialog } from './excel-preview-import-dialog'
import { useItemTypesData, useUnitsData, useItemClassesData, useCitiesData } from '@/hooks/api'

export function ImportDialog() {
  const { open, setOpen } = useItemsInCity()
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const [uploadedData, setUploadedData] = useState<(string | number)[][]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { data: itemTypesData = [] } = useItemTypesData()
  const { data: itemClassesData = [] } = useItemClassesData()
  const { data: unitsData = [] } = useUnitsData()
  const { data: citiesData = [] } = useCitiesData()

  const { downloadImportTemplateAsync, isPending } = useDownloadImportTemplate()

  const handleDownloadTemplate = async () => {
    try {
      await downloadImportTemplateAsync({
          itemTypes: itemTypesData,
          itemClasses: itemClassesData,
          units: unitsData,
          cities: citiesData
        }
      )
    } catch (_error) {
      toast.error('Lỗi khi tải template')
    }
  }

  const handleFileUpload = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = e => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // Get first sheet
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // Convert to JSON first to process item codes
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          defval: '',
          raw: false
        }) as any[]

        if (jsonData.length === 0) {
          toast.error('File không có dữ liệu')
          return
        }

        // Track used item IDs to avoid duplicates
        const usedItemIds = new Set<string>()

        const processedData = jsonData.map((row: any) => {
          const existingItemId = (row['Mã món'] ?? '').toString().trim()
          if (existingItemId) {
            usedItemIds.add(existingItemId)
          }

          let itemId = existingItemId
          if (!itemId) {
            let generated = generateItemId()
            while (usedItemIds.has(generated)) {
              generated = generateItemId()
            }
            usedItemIds.add(generated)
            itemId = generated
          }

          return {
            ...row,
            'Mã món': itemId
          }
        })

        // Convert back to array of arrays format for the preview dialog
        if (processedData.length > 0) {
          const headers = Object.keys(processedData[0])
          const arrayData = [
            headers,
            ...processedData.map(row => headers.map(header => row[header] || ''))
          ]
          setUploadedData(arrayData)
        } else {
          setUploadedData([])
        }

        setOpen(null) // Close import dialog first
        setShowPreviewDialog(true)

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      } catch (_error) {
        toast.error('Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.')
      }
    }
    reader.readAsArrayBuffer(file)
  }

  const handleSaveData = () => {
    // TODO: Implement API call to save data
    toast.success('Dữ liệu đã được lưu thành công!')
    setShowPreviewDialog(false)
    setOpen(null)
  }

  return (
    <>
      <Dialog open={open === 'import'} onOpenChange={isOpen => setOpen(isOpen ? 'import' : null)}>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Thêm món</DialogTitle>
          </DialogHeader>

          <div className='space-y-6'>
            {/* Bước 1: Tải file mẫu */}
            <div className='space-y-4 rounded-lg bg-gray-50 p-4'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-medium'>Bước 1. Tải file mẫu</h3>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleDownloadTemplate}
                  disabled={isPending}
                  className='flex items-center gap-2'
                >
                  {isPending ? (
                    'Đang tải...'
                  ) : (
                    <>
                      Tải xuống
                      <DownloadIcon className='h-4 w-4' />
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Bước 2: Thêm món vào file */}
            <div className='space-y-4 rounded-lg bg-gray-50 p-4'>
              <h3 className='text-lg font-medium'>Bước 2. Thêm món vào file</h3>

              <div className='space-y-3 text-sm text-gray-600'>
                <p>
                  Không được để trống các cột <span className='font-mono text-blue-600'>Tên, Thành phố</span>.
                </p>

                <p>
                  Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị
                  đã có vào cột <span className='font-mono text-blue-600'>Nhóm, Loại món</span>.
                </p>

                <p>
                  Mã đơn vị món, mã thành phố có thể xem trong sheet{' '}
                  <span className='font-mono text-blue-600'>Guide</span> của file mẫu.
                </p>
              </div>
            </div>

            {/* Bước 3: Tải file thực đơn lên */}
            <div className='space-y-4 rounded-lg bg-gray-50 p-4'>
              <div className='flex items-center justify-between'>
                <div>
                  <h3 className='text-lg font-medium'>Bước 3. Tải file thực đơn lên</h3>
                  <p className='mt-1 text-sm text-gray-600'>Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên</p>
                </div>
                <Button variant='outline' size='sm' onClick={handleFileUpload} className='flex items-center gap-2'>
                  Tải file lên
                  <UploadIcon className='h-4 w-4' />
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />

      {/* Upload Preview Dialog */}
      <ExcelPreviewImportDialog
        open={showPreviewDialog}
        onOpenChange={setShowPreviewDialog}
        data={uploadedData}
        onSave={handleSaveData}
      />
    </>
  )
}
