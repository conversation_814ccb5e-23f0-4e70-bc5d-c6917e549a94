import{i as h,a3 as y,u as o,k,r as p,a4 as f,a5 as P}from"./index-B283E1a3.js";import{u as v}from"./useMutation-Bf5OzDko.js";import{b as i}from"./pos-api-C7RsFAun.js";const S={login:async s=>(await i.post("/accounts/v1/user/login",s)).data.data,getProfile:async()=>{const s=await i.get("/accounts/v1/user/profile");return s.data.data||s.data},logout:async()=>{await i.post("/accounts/v1/user/logout")},refreshToken:async()=>{const s=await i.post("/accounts/v1/user/refresh");return s.data.data||s.data},verifyToken:async()=>{const s=await i.get("/accounts/v1/user/verify");return s.data.data||s.data}},d={all:["auth"],profile:()=>[...d.all,"profile"],verify:()=>[...d.all,"verify"]},A=()=>{const s=h(),n=y(),{setLoginData:c}=o(e=>e.auth);return v({mutationFn:e=>S.login(e),onSuccess:e=>{c(e),P(e),n.invalidateQueries({queryKey:d.profile()}),f.success(`Welcome back, ${e.user.full_name||e.user.email}!`);const r=new URLSearchParams(s.history.location.search).get("redirect")||"/";s.navigate({to:r})},onError:e=>{var r,u,l,g;let a="Login failed. Please check your credentials.";(u=(r=e.response)==null?void 0:r.data)!=null&&u.message&&typeof e.response.data.message=="string"?a=e.response.data.message:(g=(l=e.response)==null?void 0:l.data)!=null&&g.error&&typeof e.response.data.error=="string"?a=e.response.data.error:e.message&&typeof e.message=="string"&&(a=e.message),f.error(a)}})},C=()=>{const s=h(),n=y(),{reset:c}=o(a=>a.auth),{clearAuth:e}=k();return v({mutationFn:()=>S.logout(),onSuccess:()=>{c(),e(),localStorage.clear(),n.clear(),f.success("Logged out successfully!"),s.navigate({to:"/sign-in"})},onError:a=>{c(),e(),localStorage.clear(),n.clear(),s.navigate({to:"/sign-in"})}})},R=()=>{const s=o(t=>t.auth.user),n=o(t=>t.auth.userRole),c=o(t=>t.auth.company),e=o(t=>t.auth.brands),a=o(t=>t.auth.cities),r=o(t=>t.auth.stores),u=o(t=>t.auth.jwtToken),l=p.useMemo(()=>!!(s&&u),[s,u]),g=p.useMemo(()=>e.filter(t=>t.active===1),[e]),m=p.useMemo(()=>r.filter(t=>t.active===1),[r]),w=p.useMemo(()=>m[0]||null,[m]);return{user:s,userRole:n,company:c,brands:e,cities:a,stores:r,isAuthenticated:l,currentStore:w,activeBrands:g,activeStores:m}};export{C as a,R as b,A as u};
