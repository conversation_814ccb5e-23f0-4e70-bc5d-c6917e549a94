import{z as t,r as n,j as e}from"./index-B283E1a3.js";import{M as y,T as v,a as w,L as p}from"./TileLayer-CLS7WM6P.js";const L=t.object({name:t.string().min(1,"Tên nhà hàng là bắt buộc"),address:t.string().min(1,"Địa chỉ nhà hàng là bắt buộc"),phone:t.string().min(1,"SĐT cửa hàng là bắt buộc"),partnerDriverPhone:t.string().optional(),active:t.boolean(),onlineSales:t.boolean(),deliverySales:t.boolean(),onlineReservation:t.boolean(),emailList:t.array(t.string().email("Email không hợp lệ")),banner:t.string().optional()});delete p.Icon.Default.prototype._getIconUrl;p.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png"});function S({address:r,className:o="",height:l="300px"}){const g=[10.8231,106.6297],[a,f]=n.useState(g),[u,d]=n.useState(!1),[i,s]=n.useState(!1),x=async m=>{if(!m.trim()){s(!1);return}d(!0);try{const c=await(await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(m)}&accept-language=vi&limit=1&countrycodes=vn`)).json();if(c.length>0){const b=parseFloat(c[0].lat),j=parseFloat(c[0].lon);f([b,j]),s(!0)}else s(!1)}catch(h){console.error("Error geocoding address:",h),s(!1)}finally{d(!1)}};return n.useEffect(()=>{r?x(r):s(!1)},[r]),r.trim()?u?e.jsx("div",{className:`flex items-center justify-center bg-gray-100 rounded-md border ${o}`,style:{height:l},children:e.jsx("p",{className:"text-gray-500 text-sm",children:"Đang tải bản đồ..."})}):e.jsxs("div",{className:`relative rounded-md overflow-hidden border ${o}`,style:{height:l},children:[e.jsxs(y,{center:a,zoom:i?16:12,style:{height:"100%",width:"100%"},zoomControl:!0,scrollWheelZoom:!1,doubleClickZoom:!1,dragging:!0,children:[e.jsx(v,{attribution:'© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),i&&e.jsx(w,{position:a})]},`${a[0]}-${a[1]}`),!i&&e.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center",children:e.jsx("div",{className:"bg-white px-3 py-2 rounded-md shadow-md",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Không tìm thấy vị trí chính xác"})})}),e.jsx("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:e.jsx("div",{className:"bg-white rounded shadow-md p-1",children:e.jsx("div",{className:"w-8 h-8 flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})})})})]}):e.jsx("div",{className:`flex items-center justify-center bg-gray-100 rounded-md border-2 border-dashed border-gray-300 ${o}`,style:{height:l},children:e.jsx("p",{className:"text-gray-500 text-sm",children:"Nhập địa chỉ để hiển thị bản đồ"})})}export{S as A,L as s};
