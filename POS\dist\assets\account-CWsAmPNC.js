import{j as t,B as c,L as m}from"./index-B283E1a3.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{D as d}from"./data-table-Ch4anmfN.js";import{B as l}from"./badge-DZXns0dL.js";import{S as p}from"./settings-C9fDD83q.js";import{u as h}from"./use-account-management-CoMA16hh.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";import"./popover-CCXriU_R.js";import"./select-BzVwefGp.js";import"./index-Df0XEEuz.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./table-pagination-CQwCsWP1.js";import"./pagination-Dms6Uzom.js";import"./table-vHuVXp9f.js";function x({users:a,isLoading:s,onEditUser:n,onToggleStatus:r}){const o=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:i=>t.jsx(l,{variant:i==="active"?"default":"secondary",children:i==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(i,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(c,{variant:"ghost",size:"icon",onClick:()=>n(e),className:"h-8 w-8",children:t.jsx(p,{className:"h-4 w-4"})}),t.jsx(c,{variant:"ghost",size:"icon",onClick:()=>r(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(d,{data:a,columns:o,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}function g(){const{users:a,isLoading:s,error:n,toggleUserStatus:r}=h(),o=e=>{console.log("Edit user:",e)},i=async e=>{await r(e)};return n?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:n})})}):t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("div",{className:"mb-6",children:t.jsx(m,{to:"/general-setups/create-user",children:t.jsx(c,{children:"Tạo tài khoản"})})}),t.jsx(x,{users:a,isLoading:s,onEditUser:o,onToggleStatus:i})]})}const L=g;export{L as component};
