import{j as e,B as y,r as m,h as I,a4 as C}from"./index-B283E1a3.js";import{g as w}from"./error-utils-CItCmAST.js";import"./pos-api-C7RsFAun.js";import"./vietqr-api-BbJFOv9v.js";import{a as _,b as E,u as K}from"./use-roles-YdbLqhpY.js";import"./user-Cxn8z8PZ.js";import"./crm-api-CE_jLH-z.js";import{H as $}from"./header-BZ_7I_4c.js";import{M as z}from"./main-BlYSJOOd.js";import{C as k}from"./index-BQauI_Wp.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{P as A}from"./profile-dropdown-DhwpuuhW.js";import{S as F,T as X}from"./search-eyocbSug.js";import{C as D}from"./checkbox-DtNgKdj2.js";import{I as q}from"./IconCopy-Bx-S4wCr.js";import{I as U}from"./IconTrash-C3WF0IhL.js";import{u as V,e as G,f as M}from"./index-AD4tjDOS.js";import{T as J,a as Q,b as N,c as W,d as Y,e as T}from"./table-vHuVXp9f.js";import{I as Z}from"./IconPlus-pU5ZBuw7.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./query-keys-3lmd-xp6.js";import"./separator-DLHnMAQ0.js";import"./dialog-BTZKnesd.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";import"./popover-CCXriU_R.js";import"./select-BzVwefGp.js";import"./index-Df0XEEuz.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./avatar-CfLE65or.js";import"./dropdown-menu-JDsssJHk.js";import"./index-BhFEt02S.js";import"./search-context-CZoJZmsi.js";import"./command-C1ySvjo8.js";import"./search-BKvg0ovQ.js";import"./createReactComponent-WabRa4kY.js";import"./scroll-area-BlxlVxpe.js";import"./IconChevronRight-jxL9ONfH.js";import"./IconSearch-S0sgK6Kj.js";const ee=[{id:"select",header:({table:t})=>e.jsx(D,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:s=>t.toggleAllPageRowsSelected(!!s),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(D,{checked:t.getIsSelected(),onCheckedChange:s=>t.toggleSelected(!!s),onClick:s=>s.stopPropagation(),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1},{accessorKey:"id",header:"#",cell:({row:t})=>{const s=t.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:s})},enableSorting:!1},{accessorKey:"role_name",header:"Tên chức vụ",cell:({row:t})=>{const s=t.original;return e.jsx("span",{className:"font-medium",children:s.role_name})}},{accessorKey:"description",header:"Mô tả",cell:({row:t})=>{const s=t.original;return e.jsx("span",{className:"text-muted-foreground",children:s.description||"-"})}},{accessorKey:"allow_access",header:"Phạm vi đăng nhập",cell:({row:t})=>{var o;const r=((o=t.original.allow_access)==null?void 0:o.join(", "))||"-";return e.jsx("span",{className:"text-sm",children:r})}},{id:"copy",header:"Sao chép",cell:({row:t,table:s})=>{const r=t.original,o=s.options.meta;return e.jsxs(y,{variant:"ghost",size:"sm",onClick:i=>{var a;i.stopPropagation(),(a=o==null?void 0:o.onCopyRole)==null||a.call(o,r)},className:"h-8 w-8 p-0 text-blue-600 hover:text-blue-700",children:[e.jsx(q,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép quyền ",r.role_name]})]})}},{id:"delete",header:"",cell:({row:t,table:s})=>{const r=t.original,o=s.options.meta;return e.jsxs(y,{variant:"ghost",size:"sm",onClick:i=>{var a;i.stopPropagation(),(a=o==null?void 0:o.onDeleteRole)==null||a.call(o,r)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(U,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa chức vụ ",r.role_name]})]})}}];function te({columns:t,data:s,onCopyRole:r,onDeleteRole:o,onBulkDelete:i,clearSelection:a,onRowClick:x}){var R;const[h,g]=m.useState({}),v=I();m.useEffect(()=>{a&&g({})},[a]);const j=(l,n)=>{const u=n.target;u.closest('input[type="checkbox"]')||u.closest("button")||u.closest('[role="button"]')||u.tagName==="BUTTON"||(x?x(l):v({to:"/employee/role/detail/$roleId",params:{roleId:l.id}}))},d=V({data:s,columns:t,getCoreRowModel:G(),state:{rowSelection:h},enableRowSelection:!0,onRowSelectionChange:g,meta:{onCopyRole:r,onDeleteRole:o}}),p=d.getFilteredSelectedRowModel().rows.map(l=>l.original),b=()=>{i&&p.length>0&&i(p)};return e.jsxs("div",{className:"space-y-4",children:[p.length>0&&e.jsxs("div",{className:"flex items-center justify-between p-4 bg-muted rounded-md",children:[e.jsxs("span",{className:"text-sm text-muted-foreground",children:["Đã chọn ",p.length," chức vụ"]}),e.jsx(y,{variant:"destructive",size:"sm",onClick:b,children:"Xóa đã chọn"})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(J,{children:[e.jsx(Q,{children:d.getHeaderGroups().map(l=>e.jsx(N,{children:l.headers.map(n=>e.jsx(W,{children:n.isPlaceholder?null:M(n.column.columnDef.header,n.getContext())},n.id))},l.id))}),e.jsx(Y,{children:(R=d.getRowModel().rows)!=null&&R.length?d.getRowModel().rows.map(l=>e.jsx(N,{"data-state":l.getIsSelected()&&"selected",onClick:n=>j(l.original,n),className:"cursor-pointer hover:bg-muted/50",children:l.getVisibleCells().map(n=>e.jsx(T,{children:M(n.column.columnDef.cell,n.getContext())},n.id))},l.id)):e.jsx(N,{children:e.jsx(T,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu chức vụ."})})})]})})]})}function se(){const t=I(),[s,r]=m.useState(!1),[o,i]=m.useState(!1),[a,x]=m.useState(null),[h,g]=m.useState([]),[v,j]=m.useState(!1),d=_(),S=E(),{data:p,isLoading:b,error:R}=K({}),l=b,n=R,u=c=>{t({to:"/employee/role/detail",search:{copyFromRoleId:c.id}})},P=c=>{x(c),r(!0)},B=c=>{g(c),i(!0)},H=async()=>{if(a)try{await d.mutateAsync(a.id),C.success(`Chức vụ "${a.role_name}" đã được xóa thành công!`),r(!1),x(null)}catch(c){const f=w(c);C.error(f)}},L=async()=>{if(h.length!==0)try{const c=h.map(f=>f.id);await S.mutateAsync(c),C.success(`Đã xóa ${h.length} chức vụ thành công`),i(!1),g([]),j(!0),setTimeout(()=>j(!1),100)}catch(c){const f=w(c);C.error(`Lỗi khi xóa chức vụ: ${f}`)}},O=()=>{t({to:"/employee/role/detail"})};return e.jsxs(e.Fragment,{children:[e.jsx($,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(F,{}),e.jsx(X,{}),e.jsx(A,{})]})}),e.jsx(z,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Danh sách chức vụ"}),e.jsxs(y,{size:"sm",onClick:O,children:[e.jsx(Z,{className:"mr-2 h-4 w-4"}),"Tạo chức vụ"]})]}),n?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:w(n)})}):l?e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu chức vụ..."})}):e.jsx(te,{columns:ee,data:p||[],onCopyRole:u,onDeleteRole:P,onBulkDelete:B,clearSelection:v}),e.jsx(k,{open:s,onOpenChange:r,content:"Bạn có muốn xoá chức vụ này?",onConfirm:H,isLoading:d.isPending}),e.jsx(k,{open:o,onOpenChange:i,content:`Bạn có chắc chắn muốn xóa ${h.length} chức vụ đã chọn? Hành động này không thể hoàn tác.`,onConfirm:L,isLoading:S.isPending})]})})]})}const Qe=se;export{Qe as component};
