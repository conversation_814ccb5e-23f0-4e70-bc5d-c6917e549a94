import{z as a,j as e,B as p}from"./index-B283E1a3.js";import{C as h}from"./content-section-BqZAnrkO.js";import{u,F as x,a as i,b as l,c as m,g as j,d as b,e as f}from"./form-dNL1hWKC.js";import{s as y}from"./zod-BOoGjb2n.js";import{C as F}from"./checkbox-DtNgKdj2.js";import"./separator-DLHnMAQ0.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./createLucideIcon-D6RMy2u2.js";const C=[{id:"recents",label:"Recents"},{id:"home",label:"Home"},{id:"applications",label:"Applications"},{id:"desktop",label:"Desktop"},{id:"downloads",label:"Downloads"},{id:"documents",label:"Documents"}],g=a.object({items:a.array(a.string()).refine(o=>o.some(s=>s),{message:"You have to select at least one item."})}),S={items:["recents","home"]};function D(){const o=u({resolver:y(g),defaultValues:S});return e.jsx(x,{...o,children:e.jsxs("form",{onSubmit:o.handleSubmit(s=>{}),className:"space-y-8",children:[e.jsx(i,{control:o.control,name:"items",render:()=>e.jsxs(l,{children:[e.jsxs("div",{className:"mb-4",children:[e.jsx(m,{className:"text-base",children:"Sidebar"}),e.jsx(j,{children:"Select the items you want to display in the sidebar."})]}),C.map(s=>e.jsx(i,{control:o.control,name:"items",render:({field:t})=>{var r;return e.jsxs(l,{className:"flex flex-row items-start space-y-0 space-x-3",children:[e.jsx(b,{children:e.jsx(F,{checked:(r=t.value)==null?void 0:r.includes(s.id),onCheckedChange:c=>{var n;return c?t.onChange([...t.value,s.id]):t.onChange((n=t.value)==null?void 0:n.filter(d=>d!==s.id))}})}),e.jsx(m,{className:"font-normal",children:s.label})]},s.id)}},s.id)),e.jsx(f,{})]})}),e.jsx(p,{type:"submit",children:"Update display"})]})})}function v(){return e.jsx(h,{title:"Display",desc:"Turn items on or off to control what's displayed in the app.",children:e.jsx(D,{})})}const I=v;export{I as component};
