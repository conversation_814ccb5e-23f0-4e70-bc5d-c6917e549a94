import{u as A}from"./useQuery-Cc4LgMzN.js";import{u as w,a3 as f,l as h,aB as I}from"./index-B283E1a3.js";import{u as E}from"./useMutation-Bf5OzDko.js";import{a as y}from"./pos-api-C7RsFAun.js";import{Q as l}from"./query-keys-3lmd-xp6.js";const N=async e=>{const t=new URLSearchParams({company_uid:e.company_uid,brand_uid:e.brand_uid,page:(e.page||1).toString()});return e.store_uid&&t.append("store_uid",e.store_uid),e.list_store_uid&&t.append("list_store_uid",e.list_store_uid),e.search&&t.append("search",e.search),e.type&&t.append("type",e.type),e.skip_limit&&t.append("skip_limit","true"),e.active!==void 0&&t.append("active",e.active.toString()),(await y.get(`/v1/pos-devices?${t.toString()}`)).data},C=(e,t)=>({id:e.id,name:e.device_name,type:U(e.type),version:e.version_app||"",storeId:e.store_uid,storeName:t||"Unknown Store",status:e.active===1?"active":"inactive",lastUpdate:new Date(e.updated_at),serialNumber:e.device_code,manufacturer:"None",model:e.type,ipAddress:e.address||void 0,macAddress:void 0,createdAt:new Date(e.updated_at),updatedAt:new Date(e.updated_at),isActive:e.active===1,device_type_local:e.device_type_local}),U=e=>{const t={POS:"POS","POS MINI":"POS_MINI",POS_MINI:"POS_MINI",PDA:"PDA",KDS:"KDS",KDS_ORDER_CONTROL:"KDS_ORDER_CONTROL",KDS_MAKER:"KDS_MAKER",SELF_ORDER:"SELF_ORDER"};return t[e]||t[e.toUpperCase()]||"POS"},O=async e=>{var _,p;const{company_uid:t,brand_uid:i,id:r,newDeviceName:o}=e,d=new URLSearchParams({company_uid:t,brand_uid:i,id:r}),a=await y.get(`/v1/pos-devices/id?${d.toString()}`);if(!((_=a.data)!=null&&_.data))throw new Error("Device not found");const c=a.data.data,s={device_name:o,type:c.type,company_uid:c.company_uid,brand_uid:c.brand_uid,store_uid:c.store_uid,extra_data:c.extra_data},u=await y.post("/v3/pos-cms/pos-device",s);if((p=u.data)!=null&&p.data){const n=u.data.data;return{id:n.id,name:n.device_name,type:n.type,status:n.active===1?"active":"inactive",storeName:"",lastUpdate:new Date(n.updated_at),isActive:n.active===1,version:n.version_app||"",storeId:n.store_uid,serialNumber:n.device_code||"",manufacturer:"Unknown",model:"Unknown",createdAt:new Date(n.created_at),updatedAt:new Date(n.updated_at),ipAddress:n.my_ip_local||void 0}}throw new Error("Failed to copy device")},P=async e=>{var a,c;let i=((a=w.getState().auth.company)==null?void 0:a.id)||"";if(!i)try{const s=localStorage.getItem("pos_user_data"),u=localStorage.getItem("pos_company_data");if(s){const _=JSON.parse(s);i=i||_.company_uid||""}if(u){const _=JSON.parse(u);i=i||_.id||""}}catch{}if(!i)throw new Error(`Company UID not found. Company: ${i}`);const r=s=>{switch(s){case"POS":return 1;case"POS_MINI":return 2;case"PDA":return 3;case"KDS":return 4;case"KDS_ORDER_CONTROL":return 5;case"KDS_MAKER":return 6;case"SELF_ORDER":return 7;default:return 1}},o={device_name:e.name,type:e.device_type,company_uid:i,brand_uid:e.brand_uid,store_uid:e.store_id,extra_data:{ip_server:null,value_vat:0,is_pos_mini:e.device_type==="POS_MINI"?1:0,device_type_local:r(e.device_type),dual_screen_enable:0,area_manager_enable:1,use_ip_server_manual:0,sale_change_vat_enable:0,ip_from_server_register:null}},d=await y.post("/v3/pos-cms/pos-device",o);if((c=d.data)!=null&&c.data){const s=d.data.data;return{id:s.id,name:s.device_name,type:s.type,status:s.active===1?"active":"inactive",storeName:"",lastUpdate:new Date(s.updated_at),isActive:s.active===1,version:s.version_app||"",storeId:s.store_uid,serialNumber:s.device_code||"",manufacturer:"Unknown",model:"Unknown",createdAt:new Date(s.created_at),updatedAt:new Date(s.updated_at),ipAddress:s.my_ip_local||void 0,device_code:s.device_code}}throw new Error("Failed to create device")},K=async(e,t,i)=>{var d;if(!e||!t||!i)throw new Error(`Missing required parameters: deviceId=${e}, companyUid=${t}, brandUid=${i}`);const r=new URLSearchParams({company_uid:t,brand_uid:i,id:e}),o=await y.get(`/v1/pos-devices/id?${r.toString()}`);if((d=o.data)!=null&&d.data){const a=o.data.data;return{id:a.id,name:a.device_name,type:a.type,status:a.active===1?"active":"inactive",storeName:"",lastUpdate:new Date(a.updated_at),isActive:a.active===1,version:a.version_app||"",storeId:a.store_uid,serialNumber:a.device_code||"",manufacturer:"Unknown",model:a.type,createdAt:new Date(a.created_at),updatedAt:new Date(a.updated_at),ipAddress:a.my_ip_local||a.address||void 0,macAddress:void 0,device_code:a.device_code,device_id:a.device_id,time_display:a.time_display,extra_data:a.extra_data,address:a.address,hardware_info:a.hardware_info,software_info:a.software_info,time_zone:a.time_zone,device_name:a.device_name,my_ip_local:a.my_ip_local,version_app:a.version_app}}throw new Error("Device not found")},q=async(e,t,i)=>{var d;if(!e||!t||!i)throw new Error(`Missing required parameters: deviceId=${e}, companyUid=${t}, brandUid=${i}`);const r=new URLSearchParams({company_uid:t,brand_uid:i,id:e}),o=await y.get(`/v1/pos-devices/id?${r.toString()}`);if(!((d=o.data)!=null&&d.data))throw new Error("Device not found");return o.data.data},M=async(e,t)=>{var s,u,_,p;const i=w.getState().auth;let r=((s=i.company)==null?void 0:s.id)||"",o=((_=(u=i.brands)==null?void 0:u[0])==null?void 0:_.id)||"";if(!r||!o)try{const n=localStorage.getItem("pos_user_data"),D=localStorage.getItem("pos_company_data"),m=localStorage.getItem("pos_brands_data");if(n){const v=JSON.parse(n);r=r||v.company_uid||""}if(D){const v=JSON.parse(D);r=r||v.id||""}if(m){const v=JSON.parse(m);Array.isArray(v)&&v.length>0&&(o=o||v[0].id||"")}}catch{}if(!r||!o)throw new Error(`Company UID or Brand UID not found. Company: ${r}, Brand: ${o}`);const d=await q(e,r,o),a={...t,id:e,company_uid:r,brand_uid:o,store_uid:d.store_uid};console.log("🚀 Final payload sent to API:",JSON.stringify(a,null,2));const c=await y.post("/v3/pos-cms/pos-device",a);if((p=c.data)!=null&&p.data){const n=c.data.data;return{id:n.id,name:n.device_name,type:n.type,status:n.active===1?"active":"inactive",storeName:"",lastUpdate:new Date(n.updated_at),isActive:n.active===1,version:n.version_app||"",storeId:n.store_uid,serialNumber:n.device_code||"",manufacturer:"Unknown",model:n.type,createdAt:new Date(n.created_at),updatedAt:new Date(n.updated_at),ipAddress:n.my_ip_local||n.address||void 0,macAddress:void 0,device_code:n.device_code}}throw new Error("Failed to update device")},L=async e=>{await y.delete(`/v3/pos-cms/pos-device/${e}`)},g={fetchDevices:N,copyDevice:O,createDevice:P,getDeviceDetail:K,updateDevice:M,deleteDevice:L},V=(e={})=>{const{params:t={skip_limit:!0},enabled:i=!0,storesData:r,searchTerm:o,storeUid:d,deviceType:a,page:c=1}=e,{company:s}=w(m=>m.auth),{selectedBrand:u}=h(),{selectedStore:_}=I(),p={company_uid:(s==null?void 0:s.id)||"",brand_uid:(u==null?void 0:u.id)||"",page:c,skip_limit:!0,active:1};e.listStoreUid?p.list_store_uid=e.listStoreUid:d&&d!=="all"?p.store_uid=d:_!=null&&_.id&&(p.list_store_uid=_.id),o&&(p.search=o),a&&a!=="all"&&(p.type=a);const n={...p,...t},D=!!(s!=null&&s.id&&(u!=null&&u.id));return A({queryKey:[l.DEVICES_LIST,n],queryFn:async()=>(await N(n)).data.map(v=>{const S=r==null?void 0:r.find(R=>R.id===v.store_uid),b=(S==null?void 0:S.store_name)||`Store ${v.store_uid.slice(0,8)}`;return C(v,b)}),enabled:i&&D,staleTime:5*60*1e3,refetchInterval:30*1e3})};function x(){const e=f();return E({mutationFn:async t=>await g.createDevice({name:t.deviceName,store_id:t.storeId,device_type:t.deviceType,brand_uid:t.brandUid}),onSuccess:()=>{e.invalidateQueries({queryKey:[l.DEVICES_LIST]}),e.invalidateQueries({queryKey:[l.DEVICES]})},onError:t=>{throw t}})}const J=()=>{const e=f(),{company:t}=w(r=>r.auth),{selectedBrand:i}=h();return E({mutationFn:async({deviceId:r,newDeviceName:o})=>{if(!(t!=null&&t.id)||!(i!=null&&i.id))throw new Error("Missing authentication data");const d={company_uid:t.id,brand_uid:i.id,id:r,newDeviceName:o};return O(d)},onSuccess:()=>{e.invalidateQueries({queryKey:[l.DEVICES_LIST]})},onError:r=>{throw r}})},z=(e,t=!0)=>{const{company:i}=w(a=>a.auth),{selectedBrand:r}=h(),{stores:o}=I(),d=!!(i!=null&&i.id&&(r!=null&&r.id));return A({queryKey:[l.DEVICES,"detail",e,i==null?void 0:i.id,r==null?void 0:r.id],queryFn:async()=>{if(!(i!=null&&i.id)||!(r!=null&&r.id))throw new Error("Missing authentication data");const a=await g.getDeviceDetail(e,i.id,r.id),c=o==null?void 0:o.find(s=>s.id===a.storeId);return c&&(a.storeName=c.store_name),a},enabled:!!e&&t&&d,staleTime:0,refetchOnMount:!0,refetchOnWindowFocus:!1,retry:3})},Y=()=>{const e=f();return E({mutationFn:async({id:t,...i})=>g.updateDevice(t,i),onSuccess:t=>{e.invalidateQueries({queryKey:[l.DEVICES_LIST]}),e.invalidateQueries({queryKey:[l.DEVICES,"detail",t.id]}),e.invalidateQueries({queryKey:[l.DEVICES]})},onError:t=>{throw t}})};export{J as a,V as b,Y as c,z as d,x as u};
