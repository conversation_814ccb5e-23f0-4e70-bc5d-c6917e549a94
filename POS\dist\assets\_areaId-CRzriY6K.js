import{aY as t,j as i}from"./index-B283E1a3.js";import"./pos-api-C7RsFAun.js";import"./header-BZ_7I_4c.js";import"./main-BlYSJOOd.js";import"./search-context-CZoJZmsi.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{A as m}from"./area-form-CEwNW1zx.js";import"./separator-DLHnMAQ0.js";import"./command-C1ySvjo8.js";import"./calendar-AxR9kFpj.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";import"./createReactComponent-WabRa4kY.js";import"./scroll-area-BlxlVxpe.js";import"./index-Df0XEEuz.js";import"./select-BzVwefGp.js";import"./index-DY0KH0l4.js";import"./check-CjIon4B5.js";import"./IconChevronRight-jxL9ONfH.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";import"./popover-CCXriU_R.js";import"./images-api-CKiFhZ_l.js";import"./use-areas-Cik1fKOj.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./useMutation-Bf5OzDko.js";import"./query-keys-3lmd-xp6.js";import"./input-Bx4sCRS0.js";const B=function(){const{areaId:o}=t.useParams(),{store_uid:r}=t.useSearch();return console.log("🔍 AreaDetailPage rendered"),console.log("📍 Route params:",{areaId:o}),console.log("🔍 Route search:",{store_uid:r}),console.log("📊 Full URL:",window.location.href),i.jsx(m,{areaId:o,storeUid:r})};export{B as component};
