import{j as s}from"./index-B283E1a3.js";import{S as e}from"./skeleton-BwMfFUqN.js";import"./date-range-picker-B7aoXHt3.js";import"./form-dNL1hWKC.js";import{b as l,e as a}from"./table-vHuVXp9f.js";function j(){return s.jsx(s.Fragment,{children:Array.from({length:5}).map((m,r)=>s.jsxs(l,{children:[s.jsx(a,{className:"text-center",children:s.jsx(e,{className:"mx-auto h-4 w-6"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-24"})}),s.jsx(a,{children:s.jsx(e,{className:"h-4 w-32"})}),s.jsx(a,{children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(e,{className:"h-4 w-20"}),s.jsx(e,{className:"h-4 w-4"})]})}),s.jsx(a,{children:s.jsx(e,{className:"h-8 w-8"})})]},r))})}export{j as T};
