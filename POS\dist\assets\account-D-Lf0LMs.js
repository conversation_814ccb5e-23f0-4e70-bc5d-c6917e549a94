import{z as n,j as e,B as l,c as t}from"./index-B283E1a3.js";import{C as f}from"./content-section-BqZAnrkO.js";import{u as v,F as C,a as c,b as i,c as m,d as u,g as d,e as h}from"./form-dNL1hWKC.js";import{i as y,C as N,f as S}from"./react-icons.esm-CwfFxlzT.js";import{s as F}from"./zod-BOoGjb2n.js";import{C as w}from"./calendar-AxR9kFpj.js";import{C as I,a as P,c as k,d as A,b as D,e as M}from"./command-C1ySvjo8.js";import{I as q}from"./input-Bx4sCRS0.js";import{P as x,a as p,b as j}from"./popover-CCXriU_R.js";import{f as z}from"./isSameMonth-C8JQo-AN.js";import"./separator-DLHnMAQ0.js";import"./createLucideIcon-D6RMy2u2.js";import"./index-CqlrRQAb.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";const b=[{label:"English",value:"en"},{label:"French",value:"fr"},{label:"German",value:"de"},{label:"Spanish",value:"es"},{label:"Portuguese",value:"pt"},{label:"Russian",value:"ru"},{label:"Japanese",value:"ja"},{label:"Korean",value:"ko"},{label:"Chinese",value:"zh"}],E=n.object({name:n.string().min(2,{message:"Name must be at least 2 characters."}).max(30,{message:"Name must not be longer than 30 characters."}),dob:n.date({required_error:"A date of birth is required."}),language:n.string({required_error:"Please select a language."})}),L={name:""};function T(){const r=v({resolver:F(E),defaultValues:L});function g(a){}return e.jsx(C,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(g),className:"space-y-8",children:[e.jsx(c,{control:r.control,name:"name",render:({field:a})=>e.jsxs(i,{children:[e.jsx(m,{children:"Name"}),e.jsx(u,{children:e.jsx(q,{placeholder:"Your name",...a})}),e.jsx(d,{children:"This is the name that will be displayed on your profile and in emails."}),e.jsx(h,{})]})}),e.jsx(c,{control:r.control,name:"dob",render:({field:a})=>e.jsxs(i,{className:"flex flex-col",children:[e.jsx(m,{children:"Date of birth"}),e.jsxs(x,{children:[e.jsx(p,{asChild:!0,children:e.jsx(u,{children:e.jsxs(l,{variant:"outline",className:t("w-[240px] pl-3 text-left font-normal",!a.value&&"text-muted-foreground"),children:[a.value?z(a.value,"MMM d, yyyy"):e.jsx("span",{children:"Pick a date"}),e.jsx(y,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),e.jsx(j,{className:"w-auto p-0",align:"start",children:e.jsx(w,{mode:"single",selected:a.value,onSelect:a.onChange,disabled:o=>o>new Date||o<new Date("1900-01-01")})})]}),e.jsx(d,{children:"Your date of birth is used to calculate your age."}),e.jsx(h,{})]})}),e.jsx(c,{control:r.control,name:"language",render:({field:a})=>{var o;return e.jsxs(i,{className:"flex flex-col",children:[e.jsx(m,{children:"Language"}),e.jsxs(x,{children:[e.jsx(p,{asChild:!0,children:e.jsx(u,{children:e.jsxs(l,{variant:"outline",role:"combobox",className:t("w-[200px] justify-between",!a.value&&"text-muted-foreground"),children:[a.value?(o=b.find(s=>s.value===a.value))==null?void 0:o.label:"Select language",e.jsx(N,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})})}),e.jsx(j,{className:"w-[200px] p-0",children:e.jsxs(I,{children:[e.jsx(P,{placeholder:"Search language..."}),e.jsx(k,{children:"No language found."}),e.jsx(A,{children:e.jsx(D,{children:b.map(s=>e.jsxs(M,{value:s.label,onSelect:()=>{r.setValue("language",s.value)},children:[e.jsx(S,{className:t("mr-2 h-4 w-4",s.value===a.value?"opacity-100":"opacity-0")}),s.label]},s.value))})})]})})]}),e.jsx(d,{children:"This is the language that will be used in the dashboard."}),e.jsx(h,{})]})}}),e.jsx(l,{type:"submit",children:"Update account"})]})})}function _(){return e.jsx(f,{title:"Account",desc:`Update your account settings. Set your preferred language and\r
          timezone.`,children:e.jsx(T,{})})}const ee=_;export{ee as component};
