import{j as e,c as B,B as y,a as fe,r as d,b as be,l as Se}from"./index-B283E1a3.js";import{T as Te,a as _e,c as Q,b as q}from"./tabs-CaBi0l4C.js";import{H as Ce}from"./header-BZ_7I_4c.js";import{M as De}from"./main-BlYSJOOd.js";import{e as we,g as ie,a as Me,u as Ve,d as le,R as ke,c as Pe,T as Fe}from"./use-all-stores-Cl6GI3PI.js";import{P as Ee}from"./profile-dropdown-DhwpuuhW.js";import{S as Le,T as Ae}from"./search-eyocbSug.js";import{L as K}from"./form-dNL1hWKC.js";import{S as Be,a as He,b as Oe,c as ze,d as Z}from"./select-BzVwefGp.js";import{C as me}from"./calendar-AxR9kFpj.js";import{P as he,a as de,b as ue}from"./popover-CCXriU_R.js";import{C as xe}from"./calendar-ALtyELt3.js";import{f as Y,v as R,l as Ke}from"./isSameMonth-C8JQo-AN.js";import{v as A}from"./date-range-picker-B7aoXHt3.js";import{g as $e,u as Qe,f as qe}from"./use-excel-export-PPSTcSVV.js";import{u as Ye}from"./useQueries-Dp1bFxoJ.js";import{s as Ge}from"./sales-api-JZqmL4cN.js";import{a as Ie,f as Je}from"./stores-api-BC3ZNTx9.js";import{B as H}from"./badge-DZXns0dL.js";import{L as Xe}from"./loader-circle-C-Xia4IQ.js";import{D as Ue}from"./download-CGP1HWbk.js";import{T as We}from"./table-pagination-CQwCsWP1.js";import{S}from"./skeleton-BwMfFUqN.js";import{T as te,d as se,b as G,e as x,a as Ze,c as D}from"./table-vHuVXp9f.js";import"./index-BhFEt02S.js";import"./index-Df0XEEuz.js";import"./separator-DLHnMAQ0.js";import"./dropdown-menu-JDsssJHk.js";import"./index-CqlrRQAb.js";import"./check-CjIon4B5.js";import"./createLucideIcon-D6RMy2u2.js";import"./createReactComponent-WabRa4kY.js";import"./useQuery-Cc4LgMzN.js";import"./utils-km2FGkQ4.js";import"./avatar-CfLE65or.js";import"./search-context-CZoJZmsi.js";import"./command-C1ySvjo8.js";import"./dialog-BTZKnesd.js";import"./search-BKvg0ovQ.js";import"./pos-api-C7RsFAun.js";import"./scroll-area-BlxlVxpe.js";import"./IconChevronRight-jxL9ONfH.js";import"./IconSearch-S0sgK6Kj.js";import"./index-DY0KH0l4.js";import"./chevron-right-CVT48KKP.js";import"./react-icons.esm-CwfFxlzT.js";import"./excel-export-DLM_lrY4.js";import"./exceljs.min-C48dW61m.js";import"./xlsx-DkH2s96g.js";import"./pagination-Dms6Uzom.js";function Re({dateRange:t,onDateRangeChange:s,filterType:a="daily",className:r}){const o=n=>{s(n(t))},m=(n,i)=>{if(n)if(a==="monthly")if(i==="from"){const l=R(n);o(h=>({from:l,to:h.to}))}else{const l=Ke(n);o(h=>({from:h.from,to:l}))}else o(l=>({...l,[i]:n}))};return e.jsxs("div",{className:B("space-y-4",r),children:[e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"text-sm font-medium",children:a==="monthly"?"Từ tháng":"Từ ngày"}),e.jsxs(he,{children:[e.jsx(de,{asChild:!0,children:e.jsxs(y,{variant:"outline",className:B("w-fit justify-start text-left font-normal",!t.from&&"text-muted-foreground"),children:[e.jsx(xe,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:t.from?a==="monthly"?Y(t.from,"MM/yyyy",{locale:A}):Y(t.from,"dd/MM/yyyy",{locale:A}):a==="monthly"?"Chọn tháng bắt đầu":"Chọn ngày bắt đầu"})]})}),e.jsx(ue,{className:"w-auto p-0",align:"start",children:e.jsx(me,{mode:"single",selected:t.from,onSelect:n=>m(n,"from"),initialFocus:!0,locale:A})})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"text-sm font-medium",children:a==="monthly"?"Đến tháng":"Đến ngày"}),e.jsxs(he,{children:[e.jsx(de,{asChild:!0,children:e.jsxs(y,{variant:"outline",className:B("w-fit justify-start text-left font-normal",!t.to&&"text-muted-foreground"),children:[e.jsx(xe,{className:"mr-2 h-4 w-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:t.to?a==="monthly"?Y(t.to,"MM/yyyy",{locale:A}):Y(t.to,"dd/MM/yyyy",{locale:A}):a==="monthly"?"Chọn tháng kết thúc":"Chọn ngày kết thúc"})]})}),e.jsx(ue,{className:"w-auto p-0",align:"start",children:e.jsx(me,{mode:"single",selected:t.to,onSelect:n=>m(n,"to"),initialFocus:!0,locale:A,disabled:n=>a==="monthly"?R(n)<R(t.from):n<t.from})})]})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"text-sm font-medium",children:"Chọn nhanh"}),e.jsx("div",{className:"grid grid-cols-2 gap-2 sm:flex sm:flex-wrap",children:a==="daily"?e.jsxs(e.Fragment,{children:[e.jsx(y,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(we()),children:"Hôm nay"}),e.jsx(y,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(ie(7)),children:"7 ngày"}),e.jsx(y,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(ie(28)),children:"28 ngày"}),e.jsx(y,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const n=new Date,i=new Date(n.getFullYear(),n.getMonth(),1);s({from:i,to:n})},children:"Tháng này"})]}):e.jsxs(e.Fragment,{children:[e.jsx(y,{variant:"outline",size:"sm",className:"flex-1 sm:flex-none",onClick:()=>s(Me()),children:"3 tháng gần đây"}),e.jsx(y,{variant:"outline",size:"sm",className:"col-span-2 sm:col-span-1 sm:flex-none",onClick:()=>{const n=new Date,i=n.getFullYear(),l=new Date(i,n.getMonth()-6,1);s({from:l,to:n})},children:"6 tháng gần đây"})]})})]})]})}function et({dateRange:t,onDateRangeChange:s,selectedStores:a,onStoreChange:r,filterType:o,onFilterTypeChange:m,className:n}){const{currentBrandStores:i}=fe(),{stores:l}=Ve(),h=l.length>0?l:i,u=d.useMemo(()=>h.filter(g=>g.active===1),[h]);return e.jsx("div",{className:n,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{className:"text-sm font-medium",children:"Loại bộ lọc"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(y,{variant:o==="monthly"?"default":"outline",size:"sm",onClick:()=>m("monthly"),children:"Theo tháng"}),e.jsx(y,{variant:o==="daily"?"default":"outline",size:"sm",onClick:()=>m("daily"),children:"Theo ngày"})]})]}),e.jsx(Re,{dateRange:t,onDateRangeChange:s,filterType:o}),e.jsx("div",{className:"flex flex-wrap gap-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx(K,{htmlFor:"store-select",className:"text-sm font-medium",children:"Cửa hàng"}),e.jsxs(Be,{value:a[0]||"all-stores",onValueChange:g=>r([g]),children:[e.jsx(He,{id:"store-select",className:"w-[180px]",children:e.jsx(Oe,{placeholder:"Chọn cửa hàng"})}),e.jsxs(ze,{children:[e.jsx(Z,{value:"all-stores",children:"Tất cả cửa hàng"}),u.map(g=>e.jsx(Z,{value:g.id,children:g.store_name},g.id)),u.length===0&&e.jsx(Z,{value:"no-stores",disabled:!0,children:"Không có cửa hàng"})]})]})]})})]})})}const tt=10000172,st=(t,s,a,r,o)=>{const m=a.includes("all-stores")?"all-stores":a.sort().join("-"),n=o?`-${o}`:"";return`sale-not-sync-vat-data-${t}-${s}-${m}-${r}${n}`},at=(t,s)=>{try{localStorage.setItem(t,JSON.stringify(s))}catch{}},nt=t=>{try{const s=localStorage.getItem(t);if(s){const a=JSON.parse(s),r=24*60*60*1e3;if(Date.now()-a.timestamp<r)return a}}catch{}return null},rt=(t,s)=>{const a=/TẠI CHỖ/i;return t.filter(r=>!r.table_name||!a.test(r.table_name)||s&&r.payment_method_id!==s?!1:r.amount_origin===r.total_amount?!0:r.amount_origin<=0?!1:(r.amount_origin-r.total_amount)/r.amount_origin*100<=50)};function ot({dateRange:t,selectedStores:s=["all-stores"],sourceId:a=tt,autoFetch:r=!0,paymentMethodId:o}){var oe,ce;const{selectedBrand:m,currentBrandApiStores:n,setApiStores:i}=fe(),{company:l}=be(),h="269717a1-7bb6-4fa3-9150-dea2f709c081",u="5ed8968a-e4ed-4a04-870d-b53b7758fdc7",g=(l==null?void 0:l.id)||h,_=(m==null?void 0:m.id)||u,j=d.useMemo(()=>_,[_]),p=d.useMemo(()=>g,[g]),[$,k]=d.useState(null),[C,w]=d.useState(!0);d.useEffect(()=>{k(null),w(!0)},[j]),d.useEffect(()=>{(async()=>{if(p&&j&&n.length===0)try{const f=await Je(p,j);i(f)}catch{}})()},[p,j,n.length,i]);const{startTime:N,endTime:M}=d.useMemo(()=>{if(!(t!=null&&t.from)||!(t!=null&&t.to))return{startTime:null,endTime:null};const c=new Date(t.from);c.setHours(0,0,0,0);const f=new Date(t.to);return f.setHours(23,59,59,999),{startTime:c.getTime(),endTime:f.getTime()}},[t]),pe=d.useMemo(()=>{const c=new Map;return n==null||n.forEach(f=>{c.set(f.id,f.store_name)}),c},[n]),je=d.useMemo(()=>!n||n.length===0?[]:s.includes("all-stores")?n.slice(0,20).map(c=>c.id):s.filter(c=>n.some(f=>f.id===c)).slice(0,20),[n,s]),P=d.useMemo(()=>!N||!M?null:st(N,M,s,a,o),[N,M,s,a,o]);d.useEffect(()=>{if(P){const c=nt(P);c&&k(c)}w(!1)},[P]);const O=Ye({queries:je.map(c=>({queryKey:["sale-not-sync-vat-data",p,j,c,N,M,a],queryFn:async()=>{if(!j||!p||!N||!M)throw new Error("Missing required parameters");const f=pe.get(c),F=n==null?void 0:n.find(v=>v.id===c),E=Ie().find(v=>v.id===c),b=f||(F==null?void 0:F.store_name)||(E==null?void 0:E.store_name)||`Store ${c}`,L=[];let T=1;const V=10,z=50;for(;T<=V;)try{const v=await Ge.getSaleNotSyncVat({companyUid:p,brandUid:j,storeUid:c,startDate:N,endDate:M,sourceId:a,page:T,resultsPerPage:z});if(!v.data||v.data.length===0)break;const X=rt(v.data,o);if(L.push(...X),L.length>=1e3)break;T++}catch{break}return{storeId:c,storeName:b,salesData:L,hasData:L.length>0}},enabled:r&&!!j&&!!p&&!!N&&!!M,staleTime:10*60*1e3,gcTime:30*60*1e3,refetchInterval:30*60*1e3,refetchIntervalInBackground:!1,retry:1}))}),ne=O.map(c=>c.data),re=O.map(c=>c.status),I=d.useMemo(()=>{const c=[];let f=0,F=0,J=0;ne.forEach((b,L)=>{if(b!=null&&b.hasData&&re[L]==="success"){const{salesData:T}=b,V=T.reduce((U,W)=>U+W.amount_origin,0),z=T.reduce((U,W)=>U+W.total_amount,0),v=V-z,X=V>0?v/V*100:0;c.push({storeUid:b.storeId,storeName:b.storeName,totalTransactions:T.length,totalPrice:V,totalAmount:z,discountAmount:v,discountPercentage:X,salesData:T}),f+=V,F+=z,J+=T.length}});const E={data:c,totalPrice:f,totalAmount:F,totalTransactions:J};if(P&&c.length>0){const b={...E,timestamp:Date.now(),selectedStores:s,sourceId:a,paymentMethodId:o};at(P,b)}return E},[ne,re,P,s,a,o]),Ne=O.some(c=>c.isLoading),ve=((ce=(oe=O.find(c=>c.error))==null?void 0:oe.error)==null?void 0:ce.message)||null,ye=()=>{O.forEach(c=>c.refetch())};return{...I.data.length>0?I:$||I,isLoading:C||Ne&&!$,error:ve,refetch:ye}}function ct({onClick:t,disabled:s=!1,isLoading:a=!1,children:r="Xuất Excel",className:o,size:m="sm",variant:n="outline",icon:i,loadingText:l="Đang xuất..."}){const h=async()=>{try{await t()}catch(u){console.error("Export button error:",u)}};return e.jsx(y,{onClick:h,disabled:s||a,size:m,variant:n,className:B("gap-2",o),children:a?e.jsxs(e.Fragment,{children:[e.jsx(Xe,{className:"h-4 w-4 animate-spin"}),l]}):e.jsxs(e.Fragment,{children:[i||e.jsx(Ue,{className:"h-4 w-4"}),r]})})}function it({data:t,filename:s,sheetName:a,columnMapping:r,className:o,size:m="sm",variant:n="outline",children:i="Xuất Excel",onExportStart:l,onExportComplete:h,onExportError:u,disabled:g=!1}){const{exportData:_,isExporting:j}=Qe({data:t,filename:s,sheetName:a,columnMapping:r,onExportStart:l,onExportComplete:h,onExportError:u});return e.jsx(ct,{onClick:_,disabled:g||t.length===0,isLoading:j,className:o,size:m,variant:n,children:i})}function lt({type:t,data:s,filename:a,dateRange:r,className:o,customColumnMapping:m,disabled:n=!1}){const i={saleNotSyncVat:{tran_no:"Mã giao dịch",tran_date:"Thời gian",storeName:"Cửa hàng",employee_name:"Nhân viên",table_name:"Loại bàn",payment_method_name:"Phương thức thanh toán",voucher_code:"Mã voucher",amount_origin:"Số tiền gốc (VNĐ)",total_amount:"Thành tiền (VNĐ)"},revenue:{date:"Ngày",tran_date:"Thời gian giao dịch",total_bill:"Tổng hóa đơn",revenue_gross:"Doanh thu gốc (VNĐ)",discount_amount:"Số tiền giảm giá (VNĐ)",commission_amount:"Số tiền hoa hồng (VNĐ)",revenue_net:"Doanh thu ròng (VNĐ)",deduct_tax_amount:"Số tiền thuế khấu trừ (VNĐ)"},topStores:{storeName:"Tên cửa hàng",totalRevenue:"Tổng doanh thu (VNĐ)",totalSales:"Tổng số lượng bán",totalBills:"Tổng hóa đơn"}},h=(()=>{switch(t){case"sale-not-sync-vat":return{columnMapping:i.saleNotSyncVat,sheetName:"Giao dịch chưa đồng bộ VAT",baseName:"giao-dich-chua-dong-bo-vat"};case"revenue":return{columnMapping:i.revenue,sheetName:"Doanh thu",baseName:"doanh-thu"};case"top-stores":return{columnMapping:i.topStores,sheetName:"Top cửa hàng",baseName:"top-cua-hang"};case"custom":return{columnMapping:m||{},sheetName:"Dữ liệu",baseName:"du-lieu"};default:return{columnMapping:{},sheetName:"Dữ liệu",baseName:"export"}}})(),u=a||$e(h.baseName,r);return e.jsx(it,{data:s,filename:u,sheetName:h.sheetName,columnMapping:h.columnMapping,className:o,disabled:n})}function ee(t){return new Intl.NumberFormat("vi-VN").format(t)}function ge(t){return new Date(t).toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}function mt(t){return`${t.toFixed(1)}%`}function ht(t){const s=t.toLowerCase();return s.includes("tiền mặt")||s.includes("cod")?"default":s.includes("chuyển khoản")||s.includes("bank")?"secondary":(s.includes("thẻ")||s.includes("card"),"outline")}function dt(t){const s=t.toLowerCase();return s.includes("tại chỗ")?"destructive":s.includes("mang về")?"secondary":s.includes("giao hàng")?"outline":"default"}function ae({showStoreInfo:t=!0,showEmployeeInfo:s=!0,showPaymentMethod:a=!0}){return e.jsx(Ze,{children:e.jsxs(G,{children:[e.jsx(D,{children:"Mã giao dịch"}),e.jsx(D,{children:"Thời gian"}),t&&e.jsx(D,{children:"Cửa hàng"}),s&&e.jsx(D,{children:"Nhân viên"}),e.jsx(D,{children:"Loại bàn"}),a&&e.jsx(D,{children:"Thanh toán"}),e.jsx(D,{children:"Voucher"}),e.jsx(D,{className:"text-right",children:"Số tiền"})]})})}function ut({sale:t,showStoreInfo:s=!0,showEmployeeInfo:a=!0,showPaymentMethod:r=!0}){const o=t.amount_origin>0?(t.amount_origin-t.total_amount)/t.amount_origin*100:0;return e.jsxs(G,{children:[e.jsx(x,{children:e.jsx("div",{className:"space-y-1",children:e.jsx("div",{className:"font-mono text-sm font-medium",children:t.tran_no})})}),e.jsx(x,{children:e.jsx("span",{className:"text-sm",children:ge(t.tran_date)})}),s&&e.jsx(x,{children:e.jsx("span",{className:"text-sm",children:t.storeName})}),a&&e.jsx(x,{children:e.jsx("span",{className:"text-sm",children:t.employee_name})}),e.jsx(x,{children:e.jsx(H,{variant:dt(t.table_name),className:"px-2 py-0.5 text-xs",children:t.table_name})}),r&&e.jsx(x,{children:e.jsx(H,{variant:ht(t.payment_method_name),className:"px-1.5 py-0.5 text-xs",children:t.payment_method_name})}),e.jsx(x,{children:t.voucher_code?e.jsx(H,{variant:"outline",className:"px-2 py-0.5 font-mono text-xs",children:t.voucher_code}):e.jsx("span",{className:"text-muted-foreground text-xs",children:"-"})}),e.jsx(x,{className:"text-right",children:e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"text-sm font-semibold",children:[ee(t.total_amount)," VNĐ"]}),t.amount_origin!==t.total_amount&&e.jsxs("div",{className:"text-muted-foreground text-xs line-through",children:[ee(t.amount_origin)," VNĐ"]}),o>0&&e.jsx("div",{className:"text-xs",children:e.jsxs("span",{className:"text-red-600",children:["-",mt(o)]})})]})})]})}function xt({showStoreInfo:t=!0,showEmployeeInfo:s=!0,showPaymentMethod:a=!0}){return e.jsx("div",{className:"rounded-md border",children:e.jsxs(te,{children:[e.jsx(ae,{showStoreInfo:t,showEmployeeInfo:s,showPaymentMethod:a}),e.jsx(se,{children:Array.from({length:5}).map((r,o)=>e.jsxs(G,{children:[e.jsx(x,{children:e.jsxs("div",{className:"space-y-1",children:[e.jsx(S,{className:"h-4 w-24"}),e.jsx(S,{className:"h-3 w-16"})]})}),e.jsx(x,{children:e.jsx(S,{className:"h-4 w-20"})}),t&&e.jsx(x,{children:e.jsx(S,{className:"h-4 w-24"})}),s&&e.jsx(x,{children:e.jsx(S,{className:"h-4 w-20"})}),e.jsx(x,{children:e.jsx(S,{className:"h-6 w-16"})}),a&&e.jsx(x,{children:e.jsx(S,{className:"h-6 w-20"})}),e.jsx(x,{children:e.jsx(S,{className:"h-6 w-16"})}),e.jsx(x,{className:"text-right",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx(S,{className:"h-4 w-20"}),e.jsx(S,{className:"h-3 w-16"})]})})]},o))})]})})}function ft({showStoreInfo:t=!0,showEmployeeInfo:s=!0,showPaymentMethod:a=!0}){const r=4+(t?1:0)+(s?1:0)+(a?1:0);return e.jsx("div",{className:"rounded-md border",children:e.jsxs(te,{children:[e.jsx(ae,{showStoreInfo:t,showEmployeeInfo:s,showPaymentMethod:a}),e.jsx(se,{children:e.jsx(G,{children:e.jsx(x,{colSpan:r,className:"py-8 text-center",children:e.jsx("div",{className:"text-muted-foreground",children:"Không có dữ liệu giao dịch"})})})})]})})}function gt({data:t,showStoreInfo:s,showEmployeeInfo:a,showPaymentMethod:r}){return e.jsx("div",{className:"rounded-md border",children:e.jsxs(te,{children:[e.jsx(ae,{showStoreInfo:s,showEmployeeInfo:a,showPaymentMethod:r}),e.jsx(se,{children:t.map(o=>e.jsx(ut,{sale:o,showStoreInfo:s,showEmployeeInfo:a,showPaymentMethod:r},o.id))})]})})}function pt({dateRange:t,selectedStores:s=["all-stores"],sourceId:a=10000172,pageSize:r=20,className:o,showStoreInfo:m=!0,showEmployeeInfo:n=!0,showPaymentMethod:i=!0,showPagination:l=!0,paymentMethodId:h="COD"}){const{data:u,totalAmount:g,isLoading:_,error:j}=ot({dateRange:t,selectedStores:s,sourceId:a,autoFetch:!0,paymentMethodId:h}),p=d.useMemo(()=>{const C=[];return u.forEach(w=>{w.salesData.forEach(N=>{C.push({...N,storeName:w.storeName})})}),C.sort((w,N)=>N.tran_date-w.tran_date)},[u]),$=d.useMemo(()=>qe(p,{tran_date:C=>ge(C)}),[p]),k={showStoreInfo:m,showEmployeeInfo:n,showPaymentMethod:i};return j?e.jsxs("div",{className:B("text-center text-red-500",o),children:["Lỗi khi tải dữ liệu: ",j]}):e.jsxs("div",{className:B("space-y-4",o),children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Giao Dịch Chưa Đồng Bộ VAT"}),e.jsx(H,{variant:"outline",className:"text-xs",children:"Tại chỗ"}),e.jsx(H,{variant:"secondary",className:"text-xs",children:"COD"}),e.jsx(H,{variant:"secondary",className:"text-xs",children:"Lọc: Không giảm giá hoặc ≤ 50%"})]}),e.jsx(lt,{type:"sale-not-sync-vat",data:$,dateRange:t,className:"gap-2",disabled:_||p.length===0})]}),!_&&e.jsx("div",{className:"text-muted-foreground space-y-1 text-sm",children:e.jsxs("div",{children:["Tổng gốc: ",p.length," giao dịch • Thành tiền:"," ",ee(g)," VNĐ"]})})]}),_?e.jsx(xt,{...k}):p.length===0?e.jsx(ft,{...k}):l&&e.jsx(e.Fragment,{children:e.jsx(We,{data:p,pageSize:r,showPageSizeSelector:!0,showItemCount:!0,pageSizeOptions:[10,20,50,100],children:C=>e.jsx("div",{className:"space-y-4",children:e.jsx(gt,{data:C,...k})})})})]})}function jt(){const[t,s]=d.useState("daily"),[a,r]=d.useState(le()),[o,m]=d.useState(["all-stores"]),[n,i]=d.useState(["all-sources"]),l=u=>{s(u),u==="monthly"?r(Pe()):u==="daily"&&r(le())},h=d.useMemo(()=>({dateRange:a,filterType:t,selectedStores:o,selectedSources:n}),[a,t,o,n]);return e.jsx(ke.Provider,{value:h,children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(et,{dateRange:a,onDateRangeChange:r,selectedStores:o,onStoreChange:m,selectedSources:n,onSourceChange:i,filterType:t,onFilterTypeChange:l}),e.jsx("div",{className:"grid gap-6",children:e.jsx("div",{className:"col-span-full",children:e.jsx(pt,{dateRange:a,selectedStores:o,sourceId:10000172,pageSize:10,showStoreInfo:!0,showEmployeeInfo:!1,showPaymentMethod:!0,showPagination:!0})})})]})})}function Nt(){const{selectedBrand:t}=Se(),s=t==null?void 0:t.name,a=s?`Báo Cáo Kiểm Soát ${s}`:"Báo Cáo Kiểm Soát";return e.jsxs(e.Fragment,{children:[e.jsxs(Ce,{children:[e.jsx(Fe,{links:vt}),e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Le,{}),e.jsx(Ae,{}),e.jsx(Ee,{})]})]}),e.jsxs(De,{children:[e.jsx("div",{className:"mb-2 flex items-center justify-between space-y-2",children:e.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:a})}),e.jsxs(Te,{orientation:"vertical",defaultValue:"overview",className:"space-y-4",children:[e.jsx("div",{className:"w-full overflow-x-auto pb-2",children:e.jsxs(_e,{children:[e.jsx(Q,{value:"overview",children:"Tổng Quan"}),e.jsx(Q,{value:"stores",children:"Theo Cửa Hàng"}),e.jsx(Q,{value:"products",children:"Theo Sản Phẩm"}),e.jsx(Q,{value:"time",children:"Theo Thời Gian"})]})}),e.jsx(q,{value:"overview",className:"space-y-4",children:e.jsx(jt,{})}),e.jsx(q,{value:"stores",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo sản phẩm"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(q,{value:"products",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo sản phẩm"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})}),e.jsx(q,{value:"time",className:"space-y-4",children:e.jsxs("div",{className:"py-8 text-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Báo cáo theo thời gian"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Tính năng đang được phát triển"})]})})]})]})]})}const vt=[{title:"Tổng Quan",href:"/bao-cao/doanh-thu",isActive:!0,disabled:!1},{title:"Doanh Thu Net",href:"/bao-cao/doanh-thu/net",isActive:!1,disabled:!0},{title:"Theo Cửa Hàng",href:"/bao-cao/doanh-thu/cua-hang",isActive:!1,disabled:!0},{title:"Theo Khu Vực",href:"/bao-cao/doanh-thu/khu-vuc",isActive:!1,disabled:!0}],vs=Nt;export{vs as component};
