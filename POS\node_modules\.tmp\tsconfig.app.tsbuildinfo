{"root": ["../../src/main.tsx", "../../src/routetree.gen.ts", "../../src/vite-env.d.ts", "../../src/assets/clerk-full-logo.tsx", "../../src/assets/clerk-logo.tsx", "../../src/components/brand-status-indicator.tsx", "../../src/components/command-menu.tsx", "../../src/components/confirm-dialog.tsx", "../../src/components/filter-dropdown.tsx", "../../src/components/index.ts", "../../src/components/learn-more.tsx", "../../src/components/long-text.tsx", "../../src/components/multi-select-dropdown.tsx", "../../src/components/multi-select.tsx", "../../src/components/navigation-progress.tsx", "../../src/components/pagination.tsx", "../../src/components/password-input.tsx", "../../src/components/pos-data-example.tsx", "../../src/components/profile-dropdown.tsx", "../../src/components/search.tsx", "../../src/components/select-dropdown.tsx", "../../src/components/skip-to-main.tsx", "../../src/components/table-pagination.tsx", "../../src/components/theme-switch.tsx", "../../src/components/data-table/data-table-column-header.tsx", "../../src/components/data-table/data-table-faceted-filter.tsx", "../../src/components/data-table/data-table-pagination.tsx", "../../src/components/data-table/data-table-view-options.tsx", "../../src/components/data-table/index.ts", "../../src/components/error/index.ts", "../../src/components/error/loading-error.tsx", "../../src/components/error/loading-spinner.tsx", "../../src/components/error/missing-id-error.tsx", "../../src/components/layout/app-sidebar.tsx", "../../src/components/layout/authenticated-layout.tsx", "../../src/components/layout/brand-compat.tsx", "../../src/components/layout/brand-switcher.tsx", "../../src/components/layout/header.tsx", "../../src/components/layout/index.tsx", "../../src/components/layout/main.tsx", "../../src/components/layout/nav-group.tsx", "../../src/components/layout/nav-user.tsx", "../../src/components/layout/team-switcher.tsx", "../../src/components/layout/top-nav.tsx", "../../src/components/layout/types.ts", "../../src/components/layout/data/business-nav.ts", "../../src/components/layout/data/crm-nav.ts", "../../src/components/layout/data/general-nav.ts", "../../src/components/layout/data/index.ts", "../../src/components/layout/data/menu-nav.ts", "../../src/components/layout/data/pages-nav.ts", "../../src/components/layout/data/reports-nav.ts", "../../src/components/layout/data/settings-nav.ts", "../../src/components/layout/data/sidebar-data-modular.ts", "../../src/components/layout/data/user-teams.ts", "../../src/components/minimal-tiptap/index.ts", "../../src/components/minimal-tiptap/minimal-tiptap.tsx", "../../src/components/minimal-tiptap/types.ts", "../../src/components/minimal-tiptap/utils.ts", "../../src/components/minimal-tiptap/components/measured-container.tsx", "../../src/components/minimal-tiptap/components/shortcut-key.tsx", "../../src/components/minimal-tiptap/components/spinner.tsx", "../../src/components/minimal-tiptap/components/toolbar-button.tsx", "../../src/components/minimal-tiptap/components/toolbar-section.tsx", "../../src/components/minimal-tiptap/components/bubble-menu/link-bubble-menu.tsx", "../../src/components/minimal-tiptap/components/image/image-edit-block.tsx", "../../src/components/minimal-tiptap/components/image/image-edit-dialog.tsx", "../../src/components/minimal-tiptap/components/link/link-edit-block.tsx", "../../src/components/minimal-tiptap/components/link/link-edit-popover.tsx", "../../src/components/minimal-tiptap/components/link/link-popover-block.tsx", "../../src/components/minimal-tiptap/components/section/five.tsx", "../../src/components/minimal-tiptap/components/section/four.tsx", "../../src/components/minimal-tiptap/components/section/one.tsx", "../../src/components/minimal-tiptap/components/section/three.tsx", "../../src/components/minimal-tiptap/components/section/two.tsx", "../../src/components/minimal-tiptap/extensions/index.ts", "../../src/components/minimal-tiptap/extensions/code-block-lowlight/code-block-lowlight.ts", "../../src/components/minimal-tiptap/extensions/code-block-lowlight/index.ts", "../../src/components/minimal-tiptap/extensions/color/color.ts", "../../src/components/minimal-tiptap/extensions/color/index.ts", "../../src/components/minimal-tiptap/extensions/file-handler/index.ts", "../../src/components/minimal-tiptap/extensions/horizontal-rule/horizontal-rule.ts", "../../src/components/minimal-tiptap/extensions/horizontal-rule/index.ts", "../../src/components/minimal-tiptap/extensions/image/image.ts", "../../src/components/minimal-tiptap/extensions/image/index.ts", "../../src/components/minimal-tiptap/extensions/image/components/image-actions.tsx", "../../src/components/minimal-tiptap/extensions/image/components/image-overlay.tsx", "../../src/components/minimal-tiptap/extensions/image/components/image-view-block.tsx", "../../src/components/minimal-tiptap/extensions/image/components/resize-handle.tsx", "../../src/components/minimal-tiptap/extensions/image/hooks/use-drag-resize.ts", "../../src/components/minimal-tiptap/extensions/image/hooks/use-image-actions.ts", "../../src/components/minimal-tiptap/extensions/reset-marks-on-enter/index.ts", "../../src/components/minimal-tiptap/extensions/reset-marks-on-enter/reset-marks-on-enter.ts", "../../src/components/minimal-tiptap/extensions/unset-all-marks/index.ts", "../../src/components/minimal-tiptap/extensions/unset-all-marks/unset-all-marks.ts", "../../src/components/minimal-tiptap/hooks/use-container-size.ts", "../../src/components/minimal-tiptap/hooks/use-minimal-tiptap.ts", "../../src/components/minimal-tiptap/hooks/use-theme.ts", "../../src/components/minimal-tiptap/hooks/use-throttle.ts", "../../src/components/pos/combobox.tsx", "../../src/components/pos/discount-toggle-button.tsx", "../../src/components/pos/index.ts", "../../src/components/pos/multi-select-combobox.tsx", "../../src/components/pos/skeleton-table.tsx", "../../src/components/pos/source-combobox.tsx", "../../src/components/pos/status-badge.tsx", "../../src/components/pos/modal/index.tsx", "../../src/components/pos/modal/modal.tsx", "../../src/components/pos/modal/confirmmodal/index.tsx", "../../src/components/ui/alert-dialog.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/calendar.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/carousel.tsx", "../../src/components/ui/chart.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/collapsible.tsx", "../../src/components/ui/command.tsx", "../../src/components/ui/data-table.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/edit-sheet.tsx", "../../src/components/ui/export-button.tsx", "../../src/components/ui/form.tsx", "../../src/components/ui/index.ts", "../../src/components/ui/input-otp.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/popover.tsx", "../../src/components/ui/radio-group.tsx", "../../src/components/ui/scroll-area.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/sidebar.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/sonner.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/table.tsx", "../../src/components/ui/tabs.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/toggle-group.tsx", "../../src/components/ui/toggle.tsx", "../../src/components/ui/tooltip.tsx", "../../src/components/ui/date/date-input.tsx", "../../src/components/ui/date/date-picker.tsx", "../../src/components/ui/date/date-range-picker.tsx", "../../src/components/ui/date/index.ts", "../../src/config/fonts.ts", "../../src/constants/index.ts", "../../src/constants/local-storage.ts", "../../src/constants/query-keys.ts", "../../src/constants/cms/index.ts", "../../src/constants/cms/permission-details.ts", "../../src/constants/cms/permissions.ts", "../../src/constants/cms/types.ts", "../../src/constants/crm/file-types.ts", "../../src/constants/crm/index.ts", "../../src/constants/crm/query-keys.ts", "../../src/constants/crm/time.ts", "../../src/constants/manager/index.ts", "../../src/constants/manager/permission-details.ts", "../../src/constants/manager/permissions.ts", "../../src/constants/manager/types.ts", "../../src/constants/pos-pda/index.ts", "../../src/constants/pos-pda/permission-details.ts", "../../src/constants/pos-pda/permissions.ts", "../../src/constants/pos-pda/types.ts", "../../src/context/font-context.tsx", "../../src/context/search-context.tsx", "../../src/context/theme-context.tsx", "../../src/features/areas/areas-list.tsx", "../../src/features/areas/index.ts", "../../src/features/areas/components/add-tables-modal.tsx", "../../src/features/areas/components/area-form.tsx", "../../src/features/areas/components/areas-columns.tsx", "../../src/features/areas/components/areas-data-table.tsx", "../../src/features/areas/components/areas-table-header.tsx", "../../src/features/areas/components/areas-table-row.tsx", "../../src/features/areas/components/areas-table-skeleton.tsx", "../../src/features/areas/components/import-areas-modal.tsx", "../../src/features/areas/components/index.ts", "../../src/features/areas/hooks/index.ts", "../../src/features/areas/hooks/use-areas-excel-parser.ts", "../../src/features/areas/hooks/use-areas-import.ts", "../../src/features/auth/auth-guard.tsx", "../../src/features/auth/auth-layout.tsx", "../../src/features/auth/forgot-password/index.tsx", "../../src/features/auth/forgot-password/components/forgot-password-form.tsx", "../../src/features/auth/otp/index.tsx", "../../src/features/auth/otp/components/otp-form.tsx", "../../src/features/auth/sign-in/index.tsx", "../../src/features/auth/sign-in/sign-in-2.tsx", "../../src/features/auth/sign-in/components/user-auth-form.tsx", "../../src/features/auth/sign-up/index.tsx", "../../src/features/auth/sign-up/components/sign-up-form.tsx", "../../src/features/categories/categories-in-brand/index.tsx", "../../src/features/categories/categories-in-brand/components/category-columns.tsx", "../../src/features/categories/categories-in-brand/components/category-data-table.tsx", "../../src/features/categories/categories-in-brand/components/index.ts", "../../src/features/combos/components/combo-sort-modal.tsx", "../../src/features/crm/index.ts", "../../src/features/crm/billing-detail/index.tsx", "../../src/features/crm/billing-detail/components/billing-detail-table.tsx", "../../src/features/crm/billing-detail/components/index.tsx", "../../src/features/crm/config-register-page/context.tsx", "../../src/features/crm/config-register-page/index.tsx", "../../src/features/crm/config-register-page/components/config-register-form.tsx", "../../src/features/crm/config-register-page/components/index.ts", "../../src/features/crm/config-register-page/components/registration-page-link.tsx", "../../src/features/crm/connect-crm/index.tsx", "../../src/features/crm/customer-report/customer-report-page.tsx", "../../src/features/crm/customer-report/index.ts", "../../src/features/crm/customer-report/components/customer-report-page.tsx", "../../src/features/crm/customer-report/components/date-range-picker.tsx", "../../src/features/crm/customer-report/components/index.ts", "../../src/features/crm/general-setups/account/index.tsx", "../../src/features/crm/general-setups/account/components/account-table.tsx", "../../src/features/crm/general-setups/account/components/create-account-page.tsx", "../../src/features/crm/general-setups/account/components/index.ts", "../../src/features/crm/general-setups/account/components/permission-section.tsx", "../../src/features/crm/general-setups/account/data/account-schema.ts", "../../src/features/crm/general-setups/account/data/account-types.ts", "../../src/features/crm/general-setups/account/data/index.ts", "../../src/features/crm/general-setups/account/hooks/index.ts", "../../src/features/crm/general-setups/account/hooks/use-account-form.ts", "../../src/features/crm/general-setups/account/hooks/use-account-management.ts", "../../src/features/crm/general-setups/account/hooks/use-permission-management.ts", "../../src/features/crm/general-setups/account/utils/navigation.ts", "../../src/features/crm/loyalty/index.ts", "../../src/features/crm/loyalty/extra-point/index.tsx", "../../src/features/crm/loyalty/extra-point/components/extra-point-modal-form.tsx", "../../src/features/crm/loyalty/extra-point/components/extra-point-table.tsx", "../../src/features/crm/loyalty/extra-point/components/index.ts", "../../src/features/crm/loyalty/extra-point/components/marketing-timeframe-section.tsx", "../../src/features/crm/loyalty/extra-point/context/extra-point-context.tsx", "../../src/features/crm/loyalty/extra-point/context/index.ts", "../../src/features/crm/loyalty/membership-type/index.tsx", "../../src/features/crm/loyalty/membership-type/util.tsx", "../../src/features/crm/loyalty/membership-type/components/index.ts", "../../src/features/crm/loyalty/membership-type/components/membership-type-table.tsx", "../../src/features/crm/loyalty/membership-type/components/membership-type-modal-form/index.tsx", "../../src/features/crm/loyalty/membership-type/components/membership-type-modal-form/membership-type-section.tsx", "../../src/features/crm/loyalty/membership-type/components/membership-type-modal-form/point-accumulation-section.tsx", "../../src/features/crm/loyalty/membership-type/components/membership-type-modal-form/rank-achievement-conditions.tsx", "../../src/features/crm/loyalty/membership-type/components/membership-type-modal-form/rank-review-cycle-section.tsx", "../../src/features/crm/loyalty/membership-type/components/membership-type-modal-form/schema.ts", "../../src/features/crm/loyalty/membership-type/context/index.ts", "../../src/features/crm/loyalty/membership-type/context/membership-type-context.tsx", "../../src/features/crm/member-revenue/index.ts", "../../src/features/crm/member-revenue/components/index.ts", "../../src/features/crm/member-revenue/components/member-revenue-page.tsx", "../../src/features/crm/membership-page/index.ts", "../../src/features/crm/membership-page/components/membership-page.tsx", "../../src/features/crm/pricing-table/index.tsx", "../../src/features/crm/pricing-table/components/action-bar.tsx", "../../src/features/crm/pricing-table/components/index.ts", "../../src/features/crm/pricing-table/components/pricing-table-columns.tsx", "../../src/features/crm/pricing-table/components/pricing-table.tsx", "../../src/features/crm/rating-feedback/index.ts", "../../src/features/crm/rating-feedback/rating-feedback-page.tsx", "../../src/features/crm/rating-feedback/components/index.ts", "../../src/features/crm/rating-feedback/components/rating-chart.tsx", "../../src/features/crm/rating-feedback/components/star-rating.tsx", "../../src/features/crm/restaurant-menu/general-setups/index.tsx", "../../src/features/crm/restaurant-menu/general-setups/menu-items.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo/action-bar.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo/columns.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo/filters.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo/index.ts", "../../src/features/crm/restaurant-menu/general-setups/combo-special/action-bar.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo-special/columns.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo-special/filters.tsx", "../../src/features/crm/restaurant-menu/general-setups/combo-special/index.ts", "../../src/features/crm/restaurant-menu/general-setups/components/action-bar.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/address-map-display.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/email-list-input.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/image-upload.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/index.ts", "../../src/features/crm/restaurant-menu/general-setups/components/menu-items-columns.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/menu-items-data-table.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/search-bar.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/store-info-sidebar.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/store-settings-dropdown.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/stores-columns.tsx", "../../src/features/crm/restaurant-menu/general-setups/components/stores-data-table.tsx", "../../src/features/crm/restaurant-menu/general-setups/data/schema.ts", "../../src/features/crm/restaurant-menu/general-setups/hooks/index.ts", "../../src/features/crm/restaurant-menu/general-setups/hooks/use-menu-items.ts", "../../src/features/crm/restaurant-menu/general-setups/hooks/use-store-menu.ts", "../../src/features/crm/restaurant-menu/general-setups/item-type/action-bar.tsx", "../../src/features/crm/restaurant-menu/general-setups/item-type/columns.tsx", "../../src/features/crm/restaurant-menu/general-setups/item-type/filters.tsx", "../../src/features/crm/restaurant-menu/general-setups/item-type/index.ts", "../../src/features/crm/restaurant-menu/general-setups/items/action-bar.tsx", "../../src/features/crm/restaurant-menu/general-setups/items/columns.tsx", "../../src/features/crm/restaurant-menu/general-setups/items/filters.tsx", "../../src/features/crm/restaurant-menu/general-setups/items/index.ts", "../../src/features/crm/restaurant-menu/general-setups/types/index.ts", "../../src/features/crm/restaurant-menu/menu-content-editor/index.tsx", "../../src/features/crm/restaurant-menu/menu-content-editor/components/menu-filters.tsx", "../../src/features/crm/restaurant-menu/menu-content-editor/components/dialog/combo-edit-dialog.tsx", "../../src/features/crm/restaurant-menu/menu-content-editor/components/dialog/index.ts", "../../src/features/crm/restaurant-menu/menu-content-editor/components/dialog/menu-item-edit-dialog.tsx", "../../src/features/crm/restaurant-menu/menu-content-editor/hooks/use-menu-content-editor.ts", "../../src/features/crm/settings/index.tsx", "../../src/features/crm/settings/components/email-tags-input.tsx", "../../src/features/crm/settings/components/sections/brand-config-section.tsx", "../../src/features/crm/settings/components/sections/delivery-config-section.tsx", "../../src/features/crm/settings/components/sections/image-upload-section.tsx", "../../src/features/crm/settings/components/sections/index.ts", "../../src/features/crm/settings/components/sections/membership-config-section.tsx", "../../src/features/crm/settings/components/sections/online-order-alert-section.tsx", "../../src/features/crm/settings/components/sections/transaction-alert-section.tsx", "../../src/features/crm/settings/data/crm-settings-schema.ts", "../../src/features/crm/settings/data/crm-settings-types.ts", "../../src/features/crm/settings/data/index.ts", "../../src/features/crm/settings/hooks/index.ts", "../../src/features/crm/settings/hooks/use-crm-settings-form.ts", "../../src/features/crm/settings/hooks/use-settings-api.ts", "../../src/features/crm/system-log/index.tsx", "../../src/features/crm/system-log/components/index.ts", "../../src/features/crm/system-log/components/system-log-filters.tsx", "../../src/features/crm/system-log/components/system-log-table.tsx", "../../src/features/crm/system-log/constants/action-mappings.ts", "../../src/features/crm/system-log/data/index.ts", "../../src/features/crm/system-log/data/system-log-types.ts", "../../src/features/crm/system-log/hooks/index.ts", "../../src/features/crm/system-log/hooks/use-system-log.ts", "../../src/features/crm/using-month/index.tsx", "../../src/features/crm/using-month/components/action-bar.tsx", "../../src/features/crm/using-month/components/index.tsx", "../../src/features/crm/using-month/components/table-data.tsx", "../../src/features/crm/using-month/components/vietnamese-date-picker.tsx", "../../src/features/crm/using-month/types/index.ts", "../../src/features/crm/voucher-report/index.ts", "../../src/features/crm/voucher-report/voucher-report-page.tsx", "../../src/features/crm/voucher-report/components/date-range-picker.tsx", "../../src/features/crm/voucher-report/components/index.ts", "../../src/features/dashboard/index.tsx", "../../src/features/dashboard/components/action-bar.tsx", "../../src/features/dashboard/components/index.ts", "../../src/features/dashboard/components/overview.tsx", "../../src/features/dashboard/components/pie-chart.tsx", "../../src/features/dashboard/components/promotions-overview.tsx", "../../src/features/dashboard/components/recent-sales.tsx", "../../src/features/dashboard/components/sort-dropdown.tsx", "../../src/features/dashboard/components/sources-overview.tsx", "../../src/features/dashboard/components/sources-test.tsx", "../../src/features/dashboard/components/statistics-cards.tsx", "../../src/features/dashboard/components/store-selection-dropdown.tsx", "../../src/features/dashboard/components/stores-overview.tsx", "../../src/features/dashboard/components/top-sources-list.tsx", "../../src/features/dashboard/components/report-charts/grab-orders-chart.tsx", "../../src/features/dashboard/components/report-charts/index.tsx", "../../src/features/dashboard/components/report-charts/profit-loss-report-chart.tsx", "../../src/features/dashboard/components/report-charts/revenue-chart.tsx", "../../src/features/dashboard/components/report-charts/shopee-orders-chart.tsx", "../../src/features/dashboard/components/report-charts/top-payment-methods-chart.tsx", "../../src/features/dashboard/components/report-charts/top-products-chart.tsx", "../../src/features/dashboard/components/report-charts/top-promotions-chart.tsx", "../../src/features/dashboard/components/report-charts/top-sources-chart.tsx", "../../src/features/dashboard/components/report-charts/top-stores-chart.tsx", "../../src/features/dashboard/context/dashboard-context.tsx", "../../src/features/dashboard/context/index.ts", "../../src/features/dashboard/utils/sources-filter.ts", "../../src/features/devices/detail/index.tsx", "../../src/features/devices/detail/components/date-range-picker.tsx", "../../src/features/devices/detail/components/index.ts", "../../src/features/devices/detail/components/forms/device-basic-info.tsx", "../../src/features/devices/detail/components/forms/device-configuration.tsx", "../../src/features/devices/detail/components/forms/device-form.tsx", "../../src/features/devices/detail/components/forms/index.ts", "../../src/features/devices/detail/components/modals/cancel-device-modal.tsx", "../../src/features/devices/detail/components/modals/group-selection-modal.tsx", "../../src/features/devices/detail/components/modals/index.ts", "../../src/features/devices/detail/components/modals/order-log-modal.tsx", "../../src/features/devices/detail/components/modals/printer-modal.tsx", "../../src/features/devices/detail/constants/device-form-constants.ts", "../../src/features/devices/detail/constants/index.ts", "../../src/features/devices/detail/hooks/index.ts", "../../src/features/devices/detail/hooks/use-device-form.ts", "../../src/features/devices/detail/hooks/use-device-item-types.ts", "../../src/features/devices/detail/hooks/use-device-packages.ts", "../../src/features/devices/detail/types/form-types.ts", "../../src/features/devices/detail/types/index.ts", "../../src/features/devices/detail/utils/device-form-helpers.ts", "../../src/features/devices/detail/utils/index.ts", "../../src/features/devices/list/index.tsx", "../../src/features/devices/list/components/device-columns.tsx", "../../src/features/devices/list/components/device-copy-modal.tsx", "../../src/features/devices/list/components/device-data-table.tsx", "../../src/features/devices/list/components/device-table-toolbar.tsx", "../../src/features/devices/list/components/index.tsx", "../../src/features/devices/new/index.tsx", "../../src/features/devices/types/index.tsx", "../../src/features/devices/types/components/device-detail-modal.tsx", "../../src/features/devices/types/components/index.ts", "../../src/features/employee/detail/index.tsx", "../../src/features/employee/detail/components/hierarchical-list.tsx", "../../src/features/employee/detail/components/index.tsx", "../../src/features/employee/detail/components/selected-store-display.tsx", "../../src/features/employee/detail/components/selected-tables-display.tsx", "../../src/features/employee/detail/components/form/brand-access-section.tsx", "../../src/features/employee/detail/components/form/create-employee-form.tsx", "../../src/features/employee/detail/components/form/index.ts", "../../src/features/employee/detail/components/form/password-section.tsx", "../../src/features/employee/detail/components/form/table-selection-section.tsx", "../../src/features/employee/detail/components/form/schema/create-employee-form-schema.ts", "../../src/features/employee/detail/components/selectors/brand-city-store-selector.tsx", "../../src/features/employee/detail/components/selectors/index.ts", "../../src/features/employee/detail/components/selectors/store-selector.tsx", "../../src/features/employee/detail/components/selectors/table-selector.tsx", "../../src/features/employee/detail/components/summary/index.ts", "../../src/features/employee/detail/components/summary/selected-items-display.tsx", "../../src/features/employee/detail/components/summary/selected-items-summary.tsx", "../../src/features/employee/detail/components/summary/user-permissions-summary.tsx", "../../src/features/employee/detail/hooks/index.ts", "../../src/features/employee/detail/hooks/use-brand-city-store-selector.ts", "../../src/features/employee/detail/hooks/use-create-employee-form.ts", "../../src/features/employee/detail/hooks/use-hierarchical-data.ts", "../../src/features/employee/detail/hooks/use-selected-items-summary.ts", "../../src/features/employee/detail/hooks/use-store-selector.ts", "../../src/features/employee/detail/hooks/use-table-selector.ts", "../../src/features/employee/detail/types/index.ts", "../../src/features/employee/detail/utils/index.ts", "../../src/features/employee/detail/utils/role-utils.ts", "../../src/features/employee/list/index.tsx", "../../src/features/employee/list/components/employees-columns.tsx", "../../src/features/employee/list/components/employees-data-table.tsx", "../../src/features/employee/list/components/employees-header.tsx", "../../src/features/employee/list/components/index.tsx", "../../src/features/employee/list/hooks/index.ts", "../../src/features/employee/list/hooks/use-employee-actions.ts", "../../src/features/employee/list/hooks/use-employee-export.ts", "../../src/features/employee/list/hooks/use-employee-filters.ts", "../../src/features/employee/role/index.tsx", "../../src/features/employee/role/components/index.tsx", "../../src/features/employee/role/components/role-columns.tsx", "../../src/features/employee/role/components/role-data-table.tsx", "../../src/features/employee/role/detail/index.tsx", "../../src/features/employee/role/detail/utils.ts", "../../src/features/employee/role/detail/components/cms-tab-content.tsx", "../../src/features/employee/role/detail/components/index.ts", "../../src/features/employee/role/detail/components/manager-tab-content.tsx", "../../src/features/employee/role/detail/components/pos-pda-tab-content.tsx", "../../src/features/employee/role/detail/hooks/index.ts", "../../src/features/employee/role/detail/hooks/use-permissions.ts", "../../src/features/employee/role/detail/hooks/use-role-form.ts", "../../src/features/employee/role/detail/hooks/use-role-save.ts", "../../src/features/employee/role/detail/hooks/use-tab-management.ts", "../../src/features/errors/forbidden.tsx", "../../src/features/errors/general-error.tsx", "../../src/features/errors/maintenance-error.tsx", "../../src/features/errors/not-found-error.tsx", "../../src/features/errors/unauthorized-error.tsx", "../../src/features/general-setups/account/index.tsx", "../../src/features/general-setups/account/components/account-table.tsx", "../../src/features/general-setups/account/components/create-account-page.tsx", "../../src/features/general-setups/account/components/index.ts", "../../src/features/general-setups/account/data/account-schema.ts", "../../src/features/general-setups/account/data/account-types.ts", "../../src/features/general-setups/account/data/index.ts", "../../src/features/general-setups/account/hooks/index.ts", "../../src/features/general-setups/account/hooks/use-account-form.ts", "../../src/features/general-setups/account/hooks/use-account-management.ts", "../../src/features/menu/categories/categories-in-brand/index.tsx", "../../src/features/menu/categories/categories-in-brand/components/action-bar.tsx", "../../src/features/menu/categories/categories-in-brand/components/category-columns.tsx", "../../src/features/menu/categories/categories-in-brand/components/category-data-table.tsx", "../../src/features/menu/categories/categories-in-brand/components/index.ts", "../../src/features/menu/categories/categories-in-brand/components/modals/import-categories-modal.tsx", "../../src/features/menu/categories/categories-in-brand/detail/components/index.ts", "../../src/features/menu/categories/categories-in-brand/detail/components/category-form/index.tsx", "../../src/features/menu/categories/categories-in-brand/detail/components/category-form/item-assignment-section.tsx", "../../src/features/menu/categories/categories-in-brand/detail/components/category-form/item-selection-modal.tsx", "../../src/features/menu/categories/categories-in-brand/detail/components/category-form/printer-position-section.tsx", "../../src/features/menu/categories/categories-in-brand/detail/components/category-form/printer-selection-modal.tsx", "../../src/features/menu/categories/categories-in-brand/detail/components/category-form/util.ts", "../../src/features/menu/categories/categories-in-brand/hooks/use-categories-import.ts", "../../src/features/menu/categories/categories-in-store/index.tsx", "../../src/features/menu/categories/categories-in-store/components/action-bar.tsx", "../../src/features/menu/categories/categories-in-store/components/category-columns.tsx", "../../src/features/menu/categories/categories-in-store/components/category-data-table.tsx", "../../src/features/menu/categories/categories-in-store/components/index.ts", "../../src/features/menu/categories/categories-in-store/components/modals/import-categories-modal.tsx", "../../src/features/menu/categories/categories-in-store/detail/components/index.ts", "../../src/features/menu/categories/categories-in-store/detail/components/category-form/index.tsx", "../../src/features/menu/categories/categories-in-store/detail/components/category-form/item-assignment-section.tsx", "../../src/features/menu/categories/categories-in-store/detail/components/category-form/item-selection-modal.tsx", "../../src/features/menu/categories/categories-in-store/detail/components/category-form/printer-position-section.tsx", "../../src/features/menu/categories/categories-in-store/detail/components/category-form/printer-selection-modal.tsx", "../../src/features/menu/categories/categories-in-store/detail/components/category-form/util.ts", "../../src/features/menu/categories/categories-in-store/hooks/index.ts", "../../src/features/menu/categories/categories-in-store/hooks/use-categories-import.ts", "../../src/features/menu/categories/categories-in-store/hooks/use-item-types-in-store-data.ts", "../../src/features/menu/customization/customization-in-city/index.tsx", "../../src/features/menu/customization/customization-in-city/components/action-bar.tsx", "../../src/features/menu/customization/customization-in-city/components/customization-columns.tsx", "../../src/features/menu/customization/customization-in-city/components/customization-data-table.tsx", "../../src/features/menu/customization/customization-in-city/components/customization-pagination.tsx", "../../src/features/menu/customization/customization-in-city/components/index.ts", "../../src/features/menu/customization/customization-in-city/components/modals/copy-customization-modal.tsx", "../../src/features/menu/customization/customization-in-city/components/modals/delete-customization-modal.tsx", "../../src/features/menu/customization/customization-in-city/components/modals/export-customization-modal.tsx", "../../src/features/menu/customization/customization-in-city/components/modals/import-customization-modal.tsx", "../../src/features/menu/customization/customization-in-city/components/modals/index.ts", "../../src/features/menu/customization/customization-in-city/detail/form-context.tsx", "../../src/features/menu/customization/customization-in-city/detail/index.tsx", "../../src/features/menu/customization/customization-in-city/detail/components/add-menu-item-modal.tsx", "../../src/features/menu/customization/customization-in-city/detail/components/basic-form.tsx", "../../src/features/menu/customization/customization-in-city/detail/components/create-group-modal.tsx", "../../src/features/menu/customization/customization-in-city/detail/components/customization-header.tsx", "../../src/features/menu/customization/customization-in-city/detail/components/dish-selection-modal.tsx", "../../src/features/menu/customization/customization-in-city/detail/components/index.ts", "../../src/features/menu/customization/customization-in-city/detail/components/item-selection-modal.tsx", "../../src/features/menu/customization/customization-in-city/detail/hooks/index.ts", "../../src/features/menu/customization/customization-in-city/detail/hooks/use-customization-form.ts", "../../src/features/menu/customization/customization-in-city/detail/hooks/use-dish-selection.ts", "../../src/features/menu/customization/customization-in-city/detail/hooks/use-group-management.ts", "../../src/features/menu/customization/customization-in-city/detail/hooks/use-menu-item-selection.ts", "../../src/features/menu/customization/customization-in-city/detail/hooks/use-modal-state.ts", "../../src/features/menu/customization/customization-in-city/hooks/index.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-customization-copy.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-customization-delete.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-customization-export.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-customization-import.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-customization-modals.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-customization-search.ts", "../../src/features/menu/customization/customization-in-city/hooks/use-excel-parser.ts", "../../src/features/menu/customization/customization-in-store/index.tsx", "../../src/features/menu/customization/customization-in-store/components/customization-columns.tsx", "../../src/features/menu/customization/customization-in-store/components/customization-data-table.tsx", "../../src/features/menu/customization/customization-in-store/components/index.ts", "../../src/features/menu/customization/customization-in-store/components/modals/copy-customization-modal.tsx", "../../src/features/menu/customization/customization-in-store/components/modals/delete-customization-modal.tsx", "../../src/features/menu/customization/customization-in-store/components/modals/export-customization-modal.tsx", "../../src/features/menu/customization/customization-in-store/components/modals/import-customization-modal.tsx", "../../src/features/menu/customization/customization-in-store/components/modals/index.ts", "../../src/features/menu/customization/customization-in-store/detail/index.tsx", "../../src/features/menu/customization/customization-in-store/detail/components/add-menu-item-modal.tsx", "../../src/features/menu/customization/customization-in-store/detail/components/create-group-modal.tsx", "../../src/features/menu/customization/customization-in-store/detail/components/customization-header.tsx", "../../src/features/menu/customization/customization-in-store/detail/components/dish-selection-modal.tsx", "../../src/features/menu/customization/customization-in-store/detail/components/index.ts", "../../src/features/menu/customization/customization-in-store/detail/components/item-selection-modal.tsx", "../../src/features/menu/customization/customization-in-store/detail/edit/index.tsx", "../../src/features/menu/customization/customization-in-store/detail/hooks/index.ts", "../../src/features/menu/customization/customization-in-store/detail/hooks/use-customization-form.ts", "../../src/features/menu/customization/customization-in-store/detail/hooks/use-dish-selection.ts", "../../src/features/menu/customization/customization-in-store/detail/hooks/use-group-management.ts", "../../src/features/menu/customization/customization-in-store/detail/hooks/use-menu-item-selection.ts", "../../src/features/menu/customization/customization-in-store/detail/hooks/use-modal-state.ts", "../../src/features/menu/customization/customization-in-store/hooks/index.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-customization-copy.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-customization-delete.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-customization-export.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-customization-import.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-customization-modals.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-customization-search.ts", "../../src/features/menu/customization/customization-in-store/hooks/use-excel-parser.ts", "../../src/features/menu/item-class/index.tsx", "../../src/features/menu/item-class/components/action-bar.tsx", "../../src/features/menu/item-class/components/index.tsx", "../../src/features/menu/item-class/components/item-class-columns.tsx", "../../src/features/menu/item-class/components/item-class-data-table.tsx", "../../src/features/menu/item-class/components/item-class-form.tsx", "../../src/features/menu/item-class/components/item-selection-modal.tsx", "../../src/features/menu/item-removed/item-removed-in-city/index.tsx", "../../src/features/menu/item-removed/item-removed-in-city/components/action-bar.tsx", "../../src/features/menu/item-removed/item-removed-in-city/components/index.ts", "../../src/features/menu/item-removed/item-removed-in-city/components/removed-item-columns.tsx", "../../src/features/menu/item-removed/item-removed-in-city/components/removed-item-data-table.tsx", "../../src/features/menu/item-removed/item-removed-in-city/utils/excel-export.ts", "../../src/features/menu/item-removed/item-removed-in-city/utils/index.ts", "../../src/features/menu/item-removed/item-removed-in-store/index.tsx", "../../src/features/menu/item-removed/item-removed-in-store/components/action-bar.tsx", "../../src/features/menu/item-removed/item-removed-in-store/components/index.ts", "../../src/features/menu/item-removed/item-removed-in-store/components/removed-item-columns.tsx", "../../src/features/menu/item-removed/item-removed-in-store/components/removed-item-data-table.tsx", "../../src/features/menu/items/items-in-city/index.ts", "../../src/features/menu/items/items-in-city/context/index.tsx", "../../src/features/menu/items/items-in-city/data/index.ts", "../../src/features/menu/items/items-in-city/data/schema.ts", "../../src/features/menu/items/items-in-city/detail/index.ts", "../../src/features/menu/items/items-in-city/detail/components/image-color-dialog.tsx", "../../src/features/menu/items/items-in-city/detail/components/index.ts", "../../src/features/menu/items/items-in-city/detail/components/item-basic-info.tsx", "../../src/features/menu/items/items-in-city/detail/components/item-configuration.tsx", "../../src/features/menu/items/items-in-city/detail/components/item-detail-form.tsx", "../../src/features/menu/items/items-in-city/detail/components/item-detail-header.tsx", "../../src/features/menu/items/items-in-city/detail/components/item-form-sections.tsx", "../../src/features/menu/items/items-in-city/detail/components/price-source-dialog.tsx", "../../src/features/menu/items/items-in-city/detail/components/time-frame-config-dialog.tsx", "../../src/features/menu/items/items-in-city/hooks/index.ts", "../../src/features/menu/items/items-in-city/hooks/items-in-city-api.ts", "../../src/features/menu/items/items-in-city/hooks/items-in-city-types.ts", "../../src/features/menu/items/items-in-city/hooks/use-item-configuration-data.ts", "../../src/features/menu/items/items-in-city/hooks/use-item-configuration-state.ts", "../../src/features/menu/items/items-in-city/hooks/use-items-in-city-data.ts", "../../src/features/menu/items/items-in-city/hooks/use-items-in-city-list-state.ts", "../../src/features/menu/items/items-in-city/hooks/use-items-in-city-mutations.ts", "../../src/features/menu/items/items-in-city/hooks/use-upload-image.ts", "../../src/features/menu/items/items-in-city/list/index.ts", "../../src/features/menu/items/items-in-city/list/components/custom-column-header.tsx", "../../src/features/menu/items/items-in-city/list/components/index.ts", "../../src/features/menu/items/items-in-city/list/components/items-in-city-buttons.tsx", "../../src/features/menu/items/items-in-city/list/components/items-in-city-columns.tsx", "../../src/features/menu/items/items-in-city/list/components/items-in-city-data-table.tsx", "../../src/features/menu/items/items-in-city/list/components/items-in-city-pagination.tsx", "../../src/features/menu/items/items-in-city/list/components/items-in-city-table-skeleton.tsx", "../../src/features/menu/items/items-in-city/list/components/items-in-city-table-toolbar.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/buffet-config-modal.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/customization-dialog.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/excel-preview-export-dialog.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/excel-preview-import-dialog-with-api.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/excel-preview-import-dialog.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/export-dialog.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/import-dialog.tsx", "../../src/features/menu/items/items-in-city/list/components/modals/index.ts", "../../src/features/menu/items/items-in-city/list/components/modals/items-in-city-dialogs.tsx", "../../src/features/menu/items/items-in-city/utils/excel-utils.ts", "../../src/features/menu/items/items-in-city/utils/index.ts", "../../src/features/menu/items/items-in-store/index.ts", "../../src/features/menu/items/items-in-store/context/index.tsx", "../../src/features/menu/items/items-in-store/data/index.ts", "../../src/features/menu/items/items-in-store/data/schema.ts", "../../src/features/menu/items/items-in-store/detail/index.ts", "../../src/features/menu/items/items-in-store/detail/components/image-color-dialog.tsx", "../../src/features/menu/items/items-in-store/detail/components/index.ts", "../../src/features/menu/items/items-in-store/detail/components/item-basic-info.tsx", "../../src/features/menu/items/items-in-store/detail/components/item-configuration.tsx", "../../src/features/menu/items/items-in-store/detail/components/item-detail-form.tsx", "../../src/features/menu/items/items-in-store/detail/components/item-detail-header.tsx", "../../src/features/menu/items/items-in-store/detail/components/item-form-sections.tsx", "../../src/features/menu/items/items-in-store/detail/components/price-source-dialog.tsx", "../../src/features/menu/items/items-in-store/detail/components/time-frame-config-dialog.tsx", "../../src/features/menu/items/items-in-store/hooks/index.ts", "../../src/features/menu/items/items-in-store/hooks/items-in-store-api.ts", "../../src/features/menu/items/items-in-store/hooks/items-in-store-types.ts", "../../src/features/menu/items/items-in-store/hooks/use-item-configuration-data.ts", "../../src/features/menu/items/items-in-store/hooks/use-item-configuration-state.ts", "../../src/features/menu/items/items-in-store/hooks/use-items-in-store-data.ts", "../../src/features/menu/items/items-in-store/hooks/use-items-in-store-list-state.ts", "../../src/features/menu/items/items-in-store/hooks/use-items-in-store-mutations.ts", "../../src/features/menu/items/items-in-store/hooks/use-price-by-source-data.ts", "../../src/features/menu/items/items-in-store/hooks/use-sort-item-types.ts", "../../src/features/menu/items/items-in-store/list/index.ts", "../../src/features/menu/items/items-in-store/list/components/custom-column-header.tsx", "../../src/features/menu/items/items-in-store/list/components/index.ts", "../../src/features/menu/items/items-in-store/list/components/item-types-sortable-list.tsx", "../../src/features/menu/items/items-in-store/list/components/items-in-store-buttons.tsx", "../../src/features/menu/items/items-in-store/list/components/items-in-store-columns.tsx", "../../src/features/menu/items/items-in-store/list/components/items-in-store-data-table.tsx", "../../src/features/menu/items/items-in-store/list/components/items-in-store-pagination.tsx", "../../src/features/menu/items/items-in-store/list/components/items-in-store-table-skeleton.tsx", "../../src/features/menu/items/items-in-store/list/components/items-in-store-table-toolbar.tsx", "../../src/features/menu/items/items-in-store/list/components/menu-items-sortable-panel.tsx", "../../src/features/menu/items/items-in-store/list/components/sortable-menu-item.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/buffet-config-modal.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/copy-menu-confirm-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/copy-menu-modal.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/customization-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/excel-preview-export-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/excel-preview-import-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/export-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/import-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/index.ts", "../../src/features/menu/items/items-in-store/list/components/modals/items-in-store-dialogs.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/menu-item-selection-modal.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/price-by-source-config-modal.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/price-by-source-preview-dialog.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/sort-menu-modal.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/time-frame-config-modal.tsx", "../../src/features/menu/items/items-in-store/list/components/modals/upload-preview-dialog.tsx", "../../src/features/menu/items/items-in-store/utils/excel-utils.ts", "../../src/features/menu/items/items-in-store/utils/index.ts", "../../src/features/menu/items/items-in-store/utils/parse-price-by-source-excel.ts", "../../src/features/menu/items/items-in-store/utils/price-by-source-excel-utils.ts", "../../src/features/menu/items/items-in-store/utils/utils.ts", "../../src/features/menu/quantity-day/index.tsx", "../../src/features/menu/quantity-day/components/import-from-file-dialog.tsx", "../../src/features/menu/quantity-day/components/index.ts", "../../src/features/menu/quantity-day/components/quantity-day-buttons.tsx", "../../src/features/menu/quantity-day/components/quantity-day-columns.tsx", "../../src/features/menu/quantity-day/components/quantity-day-data-table.tsx", "../../src/features/menu/quantity-day/components/quantity-day-dialogs.tsx", "../../src/features/menu/quantity-day/components/quantity-day-mutate.tsx", "../../src/features/menu/quantity-day/components/quantity-day-row-actions.tsx", "../../src/features/menu/quantity-day/components/quantity-day-table-skeleton.tsx", "../../src/features/menu/quantity-day/components/quantity-day-table-toolbar.tsx", "../../src/features/menu/quantity-day/components/quantity-day-table-wrapper.tsx", "../../src/features/menu/quantity-day/context/index.tsx", "../../src/features/menu/quantity-day/data/index.ts", "../../src/features/menu/quantity-day/data/schema.ts", "../../src/features/menu/quantity-day/utils/excel-template.ts", "../../src/features/menu/schedule/index.tsx", "../../src/features/menu/schedule/components/index.ts", "../../src/features/menu/schedule/components/menu-item-action-selector.tsx", "../../src/features/menu/schedule/components/menu-item-advanced-fields.tsx", "../../src/features/menu/schedule/components/menu-item-basic-fields.tsx", "../../src/features/menu/schedule/components/menu-item-category-fields.tsx", "../../src/features/menu/schedule/components/menu-item-schedule-dialog.tsx", "../../src/features/menu/schedule/components/menu-item-selector.tsx", "../../src/features/menu/schedule/components/menu-items-table.tsx", "../../src/features/menu/schedule/components/menu-schedule-actions-dropdown.tsx", "../../src/features/menu/schedule/components/menu-schedule-buttons.tsx", "../../src/features/menu/schedule/components/menu-schedule-columns.tsx", "../../src/features/menu/schedule/components/menu-schedule-data-table.tsx", "../../src/features/menu/schedule/components/menu-schedule-dialogs.tsx", "../../src/features/menu/schedule/components/menu-schedule-form-fields.tsx", "../../src/features/menu/schedule/components/menu-schedule-mutate.tsx", "../../src/features/menu/schedule/components/menu-schedule-row-actions.tsx", "../../src/features/menu/schedule/components/menu-schedule-table-skeleton.tsx", "../../src/features/menu/schedule/components/menu-schedule-table-toolbar.tsx", "../../src/features/menu/schedule/context/index.tsx", "../../src/features/menu/schedule/data/index.ts", "../../src/features/menu/schedule/data/schema.ts", "../../src/features/menu/schedule/hooks/index.ts", "../../src/features/menu/schedule/hooks/use-create-menu-schedule.ts", "../../src/features/menu/schedule/hooks/use-delete-item-schedule.ts", "../../src/features/menu/schedule/hooks/use-delete-menu-schedule.ts", "../../src/features/menu/schedule/hooks/use-fetch-menu-schedule-data.ts", "../../src/features/menu/schedule/hooks/use-menu-item-form.ts", "../../src/features/menu/schedule/hooks/use-menu-schedule-data.ts", "../../src/features/menu/schedule/hooks/use-menu-schedule.ts", "../../src/features/menu/schedule/hooks/use-update-item-schedule.ts", "../../src/features/menu/schedule/schemas/index.ts", "../../src/features/menu/schedule/schemas/menu-item-form-schema.ts", "../../src/features/menu/schedule/types/index.ts", "../../src/features/menu/schedule/types/menu-schedule-api.ts", "../../src/features/menu/schedule/utils/index.ts", "../../src/features/menu/schedule/utils/item-code-generator.ts", "../../src/features/menu/schedule/utils/item-data-mapper.ts", "../../src/features/menu/schedule/utils/payload-converter.ts", "../../src/features/menu/schedule/utils/time-conversion.ts", "../../src/features/order-sources/index.tsx", "../../src/features/order-sources/components/action-bar.tsx", "../../src/features/order-sources/components/create-order-source-form.tsx", "../../src/features/order-sources/components/index.ts", "../../src/features/order-sources/components/order-source-detail-form.tsx", "../../src/features/order-sources/components/order-source-stores-modal.tsx", "../../src/features/order-sources/components/order-sources-sort-modal.tsx", "../../src/features/order-sources/components/store-selection-modal.tsx", "../../src/features/order-sources/components/data-tables/data-table.tsx", "../../src/features/order-sources/components/data-tables/index.ts", "../../src/features/order-sources/components/data-tables/table-empty.tsx", "../../src/features/order-sources/components/data-tables/table-header.tsx", "../../src/features/order-sources/components/data-tables/table-row.tsx", "../../src/features/order-sources/components/data-tables/table-skeleton.tsx", "../../src/features/order-sources/detail/index.tsx", "../../src/features/payment-methods/index.ts", "../../src/features/payment-methods/payment-methods-list.tsx", "../../src/features/payment-methods/components/create-payment-method-form.tsx", "../../src/features/payment-methods/components/index.ts", "../../src/features/payment-methods/components/payment-method-detail-form.tsx", "../../src/features/payment-methods/components/payment-method-stores-modal.tsx", "../../src/features/payment-methods/components/payment-methods-table-header.tsx", "../../src/features/payment-methods/components/payment-methods-table-row.tsx", "../../src/features/payment-methods/components/payment-methods-table-skeleton.tsx", "../../src/features/payment-methods/components/store-selection-modal.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/index.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/date-range-picker.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/filters-panel.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/index.ts", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/invoice-overview.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/sale-not-sync-vat-list.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/sale-not-sync-vat-pagination.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/sale-not-sync-vat-table.tsx", "../../src/features/reports/accounting/invoices/sale-sync-vat/components/sale-not-sync-vat-utils.ts", "../../src/features/reports/accounting/sale-detail-audit/index.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/date-range-picker.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/filters-panel.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/index.ts", "../../src/features/reports/accounting/sale-detail-audit/components/invoice-columns.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/invoice-data-table.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/invoice-list-overview.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/invoice-list.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/invoice-table-skeleton.tsx", "../../src/features/reports/accounting/sale-detail-audit/components/invoice-utils.ts", "../../src/features/reports/revenue/categories/promotion/index.tsx", "../../src/features/reports/revenue/categories/promotion/components/action-bar.tsx", "../../src/features/reports/revenue/categories/promotion/components/index.ts", "../../src/features/reports/revenue/categories/promotion/components/invoice-promotion-modal.tsx", "../../src/features/reports/revenue/categories/promotion/components/order-log-modal.tsx", "../../src/features/reports/revenue/categories/promotion/components/promotion-chart.tsx", "../../src/features/reports/revenue/categories/promotion/components/promotion-picker-dialog.tsx", "../../src/features/reports/revenue/categories/promotion/components/promotion-table.tsx", "../../src/features/reports/revenue/categories/promotion/components/revenue-chart.tsx", "../../src/features/reports/revenue/categories/promotion/components/store-selection-dropdown.tsx", "../../src/features/reports/revenue/categories/promotion/context/index.tsx", "../../src/features/reports/revenue/categories/promotion/hooks/index.ts", "../../src/features/reports/revenue/categories/promotion/hooks/use-all-invoice-pages.ts", "../../src/features/reports/revenue/categories/promotion/hooks/use-invoice-source-detail.ts", "../../src/features/reports/revenue/categories/promotion/hooks/use-sale-change-log.ts", "../../src/features/reports/revenue/categories/promotion/utils/excel-export.ts", "../../src/features/reports/revenue/categories/promotion/utils/index.ts", "../../src/features/reports/revenue/categories/source/index.tsx", "../../src/features/reports/revenue/categories/source/components/action-bar.tsx", "../../src/features/reports/revenue/categories/source/components/index.ts", "../../src/features/reports/revenue/categories/source/components/invoice-source-modal.tsx", "../../src/features/reports/revenue/categories/source/components/order-log-modal.tsx", "../../src/features/reports/revenue/categories/source/components/source-chart.tsx", "../../src/features/reports/revenue/categories/source/components/source-table.tsx", "../../src/features/reports/revenue/categories/source/components/store-selection-dropdown.tsx", "../../src/features/reports/revenue/categories/source/context/index.tsx", "../../src/features/reports/revenue/categories/source/hooks/index.ts", "../../src/features/reports/revenue/categories/source/hooks/use-all-invoice-pages.ts", "../../src/features/reports/revenue/categories/source/hooks/use-invoice-source-detail.ts", "../../src/features/reports/revenue/categories/source/hooks/use-sale-change-log.ts", "../../src/features/reports/revenue/categories/source/utils/excel-export.ts", "../../src/features/reports/revenue/payment-method/index.tsx", "../../src/features/reports/revenue/payment-method/components/action-bar.tsx", "../../src/features/reports/revenue/payment-method/components/comparison-chart.tsx", "../../src/features/reports/revenue/payment-method/components/comparison-store-dropdown.tsx", "../../src/features/reports/revenue/payment-method/components/index.ts", "../../src/features/reports/revenue/payment-method/components/payment-method-chart.tsx", "../../src/features/reports/revenue/payment-method/components/payment-method-data-table.tsx", "../../src/features/reports/revenue/payment-method/components/store-selection-dropdown.tsx", "../../src/features/reports/revenue/payment-method/context/index.ts", "../../src/features/reports/revenue/payment-method/context/payment-method-context.tsx", "../../src/features/reports/revenue/payment-method/utils/date-format.ts", "../../src/features/reports/revenue/payment-method/utils/sales-report-export.ts", "../../src/features/reports/revenue/revenue/general/index.tsx", "../../src/features/reports/revenue/revenue/general/components/control-overview.tsx", "../../src/features/reports/revenue/revenue/general/components/date-range-picker.tsx", "../../src/features/reports/revenue/revenue/general/components/discount-rate-chart.tsx", "../../src/features/reports/revenue/revenue/general/components/filters-panel.tsx", "../../src/features/reports/revenue/revenue/general/components/index.ts", "../../src/features/reports/revenue/revenue/general/components/individual-source-stores.tsx", "../../src/features/reports/revenue/revenue/general/components/platform-sources.tsx", "../../src/features/reports/revenue/revenue/general/components/sources-by-store.tsx", "../../src/features/reports/revenue/revenue/general/components/sources-list.tsx", "../../src/features/reports/revenue/revenue/general/components/sources-section.tsx", "../../src/features/reports/revenue/revenue/general/components/voucher-discount-chart.tsx", "../../src/features/reports/revenue/sale-summary/index.tsx", "../../src/features/reports/revenue/sale-summary/components/date-range-picker.tsx", "../../src/features/reports/revenue/sale-summary/components/filters-panel.tsx", "../../src/features/reports/revenue/sale-summary/components/responsive-wrapper.tsx", "../../src/features/reports/revenue/sale-summary/components/revenue-chart.tsx", "../../src/features/reports/revenue/sale-summary/components/revenue-kpi-cards.tsx", "../../src/features/reports/revenue/sale-summary/components/revenue-overview.tsx", "../../src/features/reports/revenue/sale-summary/components/revenue-total-amount.tsx", "../../src/features/reports/revenue/sale-summary/components/top-deleted-stores.tsx", "../../src/features/reports/revenue/sale-summary/components/top-items.tsx", "../../src/features/reports/revenue/sale-summary/components/top-stores-chart.tsx", "../../src/features/reports/revenue/sale-summary/components/top-stores-section.tsx", "../../src/features/reports/revenue/sale-summary/components/top-stores.tsx", "../../src/features/sale/combo/index.tsx", "../../src/features/sale/combo/components/action-bar.tsx", "../../src/features/sale/combo/components/combo-columns.tsx", "../../src/features/sale/combo/components/combo-data-table.tsx", "../../src/features/sale/combo/components/combo-export-preview-dialog.tsx", "../../src/features/sale/combo/components/combo-import-preview-dialog.tsx", "../../src/features/sale/combo/components/copy-combo-modal.tsx", "../../src/features/sale/combo/components/create-combo-dialog.tsx", "../../src/features/sale/combo/components/export-combo-dialog.tsx", "../../src/features/sale/combo/components/group-create-dialog.tsx", "../../src/features/sale/combo/components/import-combo-dialog.tsx", "../../src/features/sale/combo/components/index.ts", "../../src/features/sale/combo/components/item-selection-dialog.tsx", "../../src/features/sale/combo/components/price-source-config-dialog.tsx", "../../src/features/sale/combo/components/store-selection-dialog.tsx", "../../src/features/sale/combo/components/time-frame-config-dialog.tsx", "../../src/features/sale/combo/components/combo-form/index.tsx", "../../src/features/sale/combo/components/combo-form/components/add-modal.tsx", "../../src/features/sale/combo/components/combo-form/components/combo-information-section.tsx", "../../src/features/sale/combo/components/combo-form/components/customization-section.tsx", "../../src/features/sale/combo/components/combo-form/components/date-application-section.tsx", "../../src/features/sale/combo/components/combo-form/components/groups-section.tsx", "../../src/features/sale/combo/components/combo-form/components/header-form.tsx", "../../src/features/sale/combo/components/combo-form/components/index.ts", "../../src/features/sale/combo/components/combo-form/components/items-section.tsx", "../../src/features/sale/combo/components/combo-form/components/marketing-timeframe-section.tsx", "../../src/features/sale/combo/components/combo-form/components/packages-section.tsx", "../../src/features/sale/combo/components/combo-form/hooks/index.ts", "../../src/features/sale/combo/components/combo-form/hooks/use-combo-form.ts", "../../src/features/sale/combo/components/combo-form/hooks/use-combo-sales-channels.ts", "../../src/features/sale/combo/components/combo-form/stores/combo-form-context.tsx", "../../src/features/sale/combo/components/combo-form/stores/index.ts", "../../src/features/sale/combo/detail/$id.tsx", "../../src/features/sale/combo/detail/index.tsx", "../../src/features/sale/combo/hooks/use-group-items-drag-drop.ts", "../../src/features/sale/discount/membership/index.tsx", "../../src/features/sale/discount/membership/components/action-bar.tsx", "../../src/features/sale/discount/membership/components/copy-discount-modal.tsx", "../../src/features/sale/discount/membership/components/discount-data-table.tsx", "../../src/features/sale/discount/membership/components/index.ts", "../../src/features/sale/discount/membership/components/membership-discount-form/index.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/add-modal.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/customization-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/date-application-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/discount-information-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/groups-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/header-form.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/index.ts", "../../src/features/sale/discount/membership/components/membership-discount-form/components/items-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/marketing-timeframe-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/packages-section.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/components/promotion-selector.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/hooks/index.ts", "../../src/features/sale/discount/membership/components/membership-discount-form/hooks/use-discount-form.ts", "../../src/features/sale/discount/membership/components/membership-discount-form/stores/discount-form-context.tsx", "../../src/features/sale/discount/membership/components/membership-discount-form/stores/index.ts", "../../src/features/sale/discount/membership/detail/index.tsx", "../../src/features/sale/discount/membership/hooks/index.ts", "../../src/features/sale/discount/membership/hooks/use-discount-filters.ts", "../../src/features/sale/discount/membership/hooks/use-membership-discounts-data.ts", "../../src/features/sale/discount/membership/utils/discount-transformers.ts", "../../src/features/sale/discount/regular/index.tsx", "../../src/features/sale/discount/regular/main-index.ts", "../../src/features/sale/discount/regular/components/action-bar.tsx", "../../src/features/sale/discount/regular/components/copy-discount-modal.tsx", "../../src/features/sale/discount/regular/components/discount-action-bar.tsx", "../../src/features/sale/discount/regular/components/discount-data-table.tsx", "../../src/features/sale/discount/regular/components/index.ts", "../../src/features/sale/discount/regular/components/regular-discount-pagination.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/index.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/add-modal.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/customization-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/date-application-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/discount-information-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/groups-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/header-form.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/index.ts", "../../src/features/sale/discount/regular/components/regular-discount-form/components/items-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/marketing-timeframe-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/packages-section.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/components/promotion-selector.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/hooks/index.ts", "../../src/features/sale/discount/regular/components/regular-discount-form/hooks/use-discount-form.ts", "../../src/features/sale/discount/regular/components/regular-discount-form/stores/discount-form-context.tsx", "../../src/features/sale/discount/regular/components/regular-discount-form/stores/index.ts", "../../src/features/sale/discount/regular/detail/$id.tsx", "../../src/features/sale/discount/regular/detail/index.tsx", "../../src/features/sale/discount/regular/detail/main-exports.tsx", "../../src/features/sale/discount-payment/index.tsx", "../../src/features/sale/discount-payment/components/actionbar.tsx", "../../src/features/sale/discount-payment/components/copydiscountpaymentmodal.tsx", "../../src/features/sale/discount-payment/components/datatable.tsx", "../../src/features/sale/discount-payment/components/index.ts", "../../src/features/sale/discount-payment/components/payment-discount-form/index.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/add-modal.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/customization-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/date-application-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/discount-information-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/groups-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/header-form.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/index.ts", "../../src/features/sale/discount-payment/components/payment-discount-form/components/items-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/marketing-timeframe-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/packages-section.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/components/promotion-selector.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/hooks/index.ts", "../../src/features/sale/discount-payment/components/payment-discount-form/hooks/use-discount-form.ts", "../../src/features/sale/discount-payment/components/payment-discount-form/stores/discount-form-context.tsx", "../../src/features/sale/discount-payment/components/payment-discount-form/stores/index.ts", "../../src/features/sale/discount-payment/detail/$id.tsx", "../../src/features/sale/discount-payment/detail/index.tsx", "../../src/features/sale/promotion/index.tsx", "../../src/features/sale/promotion/components/index.ts", "../../src/features/sale/promotion/components/promotion-buttons.tsx", "../../src/features/sale/promotion/components/promotion-columns.tsx", "../../src/features/sale/promotion/components/promotion-data-table.tsx", "../../src/features/sale/promotion/components/promotion-dialogs.tsx", "../../src/features/sale/promotion/components/promotion-mutate.tsx", "../../src/features/sale/promotion/components/promotion-row-actions.tsx", "../../src/features/sale/promotion/components/promotion-table-skeleton.tsx", "../../src/features/sale/promotion/components/promotion-table-toolbar.tsx", "../../src/features/sale/promotion/context/index.tsx", "../../src/features/sale/promotion/data/index.ts", "../../src/features/sale/promotion/data/schema.ts", "../../src/features/sale/service-charge/index.tsx", "../../src/features/sale/service-charge/components/actionbar.tsx", "../../src/features/sale/service-charge/components/copyservicechargemodal.tsx", "../../src/features/sale/service-charge/components/datatable.tsx", "../../src/features/sale/service-charge/components/expandedconditionsmodal.tsx", "../../src/features/sale/service-charge/components/servicechargepagination.tsx", "../../src/features/sale/service-charge/components/index.ts", "../../src/features/sale/service-charge/components/service-charge-form/index.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/add-modal.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/customization-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/date-application-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/groups-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/header-form.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/index.ts", "../../src/features/sale/service-charge/components/service-charge-form/components/items-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/marketing-timeframe-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/packages-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/service-charge-information-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/components/synchronization-section.tsx", "../../src/features/sale/service-charge/components/service-charge-form/hooks/index.ts", "../../src/features/sale/service-charge/components/service-charge-form/hooks/use-service-charge-form.ts", "../../src/features/sale/service-charge/components/service-charge-form/stores/index.ts", "../../src/features/sale/service-charge/components/service-charge-form/stores/service-charge-form-context.tsx", "../../src/features/sale-channel/channel/index.tsx", "../../src/features/sale-channel/channel/components/copy-channel-modal.tsx", "../../src/features/sale-channel/channel/components/index.tsx", "../../src/features/sale-channel/channel/components/data-tables/channel-actions-cell.tsx", "../../src/features/sale-channel/channel/components/data-tables/channels-columns.tsx", "../../src/features/sale-channel/channel/components/data-tables/channels-data-table.tsx", "../../src/features/sale-channel/channel/components/data-tables/delete-channel-button.tsx", "../../src/features/sale-channel/channel/components/data-tables/index.ts", "../../src/features/sale-channel/channel/components/header/channel-actions-dropdown.tsx", "../../src/features/sale-channel/channel/components/header/channel-header.tsx", "../../src/features/sale-channel/channel/components/header/index.ts", "../../src/features/sale-channel/channel/components/header/source-select.tsx", "../../src/features/sale-channel/channel/components/header/store-select.tsx", "../../src/features/sale-channel/channel/components/modals/import-preview-table.tsx", "../../src/features/sale-channel/channel/components/modals/import-sale-channel-modal.tsx", "../../src/features/sale-channel/channel/components/modals/index.ts", "../../src/features/sale-channel/channel/components/modals/validation-error-modal.tsx", "../../src/features/sale-channel/channel/detail/index.tsx", "../../src/features/sale-channel/channel/detail/components/index.ts", "../../src/features/sale-channel/channel/detail/components/forms/channel-form.tsx", "../../src/features/sale-channel/channel/detail/components/forms/index.ts", "../../src/features/sale-channel/channel/detail/components/sections/basic-info-section.tsx", "../../src/features/sale-channel/channel/detail/components/sections/channel-config-section.tsx", "../../src/features/sale-channel/channel/detail/components/sections/index.ts", "../../src/features/sale-channel/channel/detail/components/sections/marketing-config-section.tsx", "../../src/features/sale-channel/channel/detail/data/channel-types.ts", "../../src/features/sale-channel/channel/detail/data/index.ts", "../../src/features/sale-channel/channel/detail/hooks/index.ts", "../../src/features/sale-channel/channel/detail/hooks/use-channel-data.ts", "../../src/features/sale-channel/channel/detail/hooks/use-channel-form.ts", "../../src/features/sale-channel/channel/detail/hooks/use-channel-save.ts", "../../src/features/sale-channel/channel/detail/hooks/use-channel-validation.ts", "../../src/features/sale-channel/channel/detail/hooks/use-source-selection.ts", "../../src/features/sale-channel/channel/detail/hooks/use-store-selection.ts", "../../src/features/sale-channel/channel/detail/utils/days-of-week.ts", "../../src/features/sale-channel/channel/detail/utils/error-handler.ts", "../../src/features/sale-channel/channel/detail/utils/index.ts", "../../src/features/sale-channel/channel/hooks/index.ts", "../../src/features/sale-channel/channel/hooks/use-import-sale-channel.ts", "../../src/features/sale-channel/channel/utils/excel-utils.ts", "../../src/features/sale-channel/channel/utils/import-utils.ts", "../../src/features/sale-channel/channel/utils/index.ts", "../../src/features/sale-channel/discount/index.tsx", "../../src/features/sale-channel/discount/components/copy-discount-modal.tsx", "../../src/features/sale-channel/discount/components/discount-action-bar.tsx", "../../src/features/sale-channel/discount/components/discount-data-table.tsx", "../../src/features/sale-channel/discount/components/index.ts", "../../src/features/sale-channel/discount/components/discount-form/index.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/add-modal.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/customization-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/date-application-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/discount-information-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/groups-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/header-form.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/index.ts", "../../src/features/sale-channel/discount/components/discount-form/components/items-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/marketing-timeframe-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/components/packages-section.tsx", "../../src/features/sale-channel/discount/components/discount-form/hooks/index.ts", "../../src/features/sale-channel/discount/components/discount-form/hooks/use-discount-form.ts", "../../src/features/sale-channel/discount/components/discount-form/hooks/use-discount-sales-channels.ts", "../../src/features/sale-channel/discount/components/discount-form/stores/discount-form-context.tsx", "../../src/features/sale-channel/discount/components/discount-form/stores/index.ts", "../../src/features/setting/bill-template/index.tsx", "../../src/features/setting/bill-template/components/bill-config-panel.tsx", "../../src/features/setting/bill-template/components/bill-template-action-bar.tsx", "../../src/features/setting/bill-template/components/editable-field.tsx", "../../src/features/setting/bill-template/components/image-selector-dialog.tsx", "../../src/features/setting/bill-template/components/index.ts", "../../src/features/setting/bill-template/components/sync-bill-template-modal.tsx", "../../src/features/setting/bill-template/components/templates/bill-template-1.tsx", "../../src/features/setting/bill-template/components/templates/bill-template-2.tsx", "../../src/features/setting/bill-template/components/templates/bill-template-3.tsx", "../../src/features/setting/bill-template/components/templates/bill-template-4.tsx", "../../src/features/setting/bill-template/components/templates/index.ts", "../../src/features/setting/bill-template/hooks/index.ts", "../../src/features/setting/bill-template/hooks/use-bill-template-form.ts", "../../src/features/setting/bill-template/hooks/use-get-bill-template.ts", "../../src/features/setting/bill-template/hooks/use-save-bill-template-with-image.ts", "../../src/features/setting/bill-template/hooks/use-save-bill-template.ts", "../../src/features/setting/bill-template/hooks/use-use-bill-template.ts", "../../src/features/setting/bill-template/types/index.ts", "../../src/features/setting/bill-template/utils/create-minimal-payload.ts", "../../src/features/setting/bill-template/utils/create-use-template-payload.ts", "../../src/features/setting/bill-template/utils/index.ts", "../../src/features/setting/bill-template/utils/map-api-to-config.ts", "../../src/features/setting/printer-position/printer-position-in-brand/index.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/category-selection-section.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/index.ts", "../../src/features/setting/printer-position/printer-position-in-brand/components/item-selection-section.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/order-source-selection-dialog.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-bulk-delete.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-buttons.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-columns.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-data-table.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-dialogs.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-mutate.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-row-actions.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-table-skeleton.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-table-toolbar.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/components/printer-position-in-brand-table-wrapper.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/context/index.tsx", "../../src/features/setting/printer-position/printer-position-in-brand/data/index.ts", "../../src/features/setting/printer-position/printer-position-in-brand/data/schema.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/index.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/use-create-printer-position.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/use-delete-printer-position.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/use-printer-position-detail.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/use-printer-positions-data.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/use-printer-positions-for-table.ts", "../../src/features/setting/printer-position/printer-position-in-brand/hooks/use-update-printer-position.ts", "../../src/features/setting/printer-position/printer-position-in-store/index.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/area-selection-dialog.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/category-selection-section.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/index.ts", "../../src/features/setting/printer-position/printer-position-in-store/components/item-selection-section.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/order-source-selection-dialog.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-buttons.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-columns.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-data-table.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-dialogs.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-mutate.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-pagination.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-table-skeleton.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-table-toolbar.tsx", "../../src/features/setting/printer-position/printer-position-in-store/components/printer-position-in-store-table-wrapper.tsx", "../../src/features/setting/printer-position/printer-position-in-store/context/index.tsx", "../../src/features/setting/printer-position/printer-position-in-store/data/index.ts", "../../src/features/setting/printer-position/printer-position-in-store/data/schema.ts", "../../src/features/setting/printer-position/printer-position-in-store/hooks/index.ts", "../../src/features/setting/printer-position/printer-position-in-store/hooks/use-create-printer-position-in-store.ts", "../../src/features/setting/printer-position/printer-position-in-store/hooks/use-printer-position-in-store-detail.ts", "../../src/features/setting/printer-position/printer-position-in-store/hooks/use-printer-positions-in-store-data.ts", "../../src/features/setting/printer-position/printer-position-in-store/hooks/use-printer-positions-in-store-for-table.ts", "../../src/features/setting/printer-position/printer-position-in-store/hooks/use-update-printer-position-in-store.ts", "../../src/features/setting/store/index.tsx", "../../src/features/setting/store/components/index.ts", "../../src/features/setting/store/components/detail/index.ts", "../../src/features/setting/store/components/detail/form/store-form.tsx", "../../src/features/setting/store/components/detail/sections/device-section.tsx", "../../src/features/setting/store/components/detail/sections/index.ts", "../../src/features/setting/store/components/detail/sections/revenue-lock-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/advanced-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/buffet-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/business-hours-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/index.ts", "../../src/features/setting/store/components/detail/sections/advanced-config/invoice-number-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/invoice-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/label-order-management-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/mall-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/menu-edit-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/menu-items-dialog.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/operation-model-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/order-report-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/pin-code-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/pos-advanced-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/pos-tracking-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/print-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/self-order-config-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/shift-management-section.tsx", "../../src/features/setting/store/components/detail/sections/advanced-config/voucher-member-section.tsx", "../../src/features/setting/store/components/detail/sections/ahamove-config/ahamove-section.tsx", "../../src/features/setting/store/components/detail/sections/ahamove-config/index.ts", "../../src/features/setting/store/components/detail/sections/basic-info/basic-info-section.tsx", "../../src/features/setting/store/components/detail/sections/basic-info/contact-info-section.tsx", "../../src/features/setting/store/components/detail/sections/basic-info/index.ts", "../../src/features/setting/store/components/detail/sections/basic-info/license-details-dialog.tsx", "../../src/features/setting/store/components/detail/sections/basic-info/location-section.tsx", "../../src/features/setting/store/components/detail/sections/basic-info/vat-discount-config/index.ts", "../../src/features/setting/store/components/detail/sections/basic-info/vat-discount-config/vat-discount-config-modal.tsx", "../../src/features/setting/store/components/detail/sections/basic-info/vat-discount-config/vat-discount-form-modal.tsx", "../../src/features/setting/store/components/detail/sections/basic-info/vat-discount-config/vat-discount-schema.ts", "../../src/features/setting/store/components/detail/sections/basic-info/vat-discount-config/vat-discount-types.ts", "../../src/features/setting/store/components/detail/sections/brand-building/brand-building-section.tsx", "../../src/features/setting/store/components/detail/sections/brand-building/index.ts", "../../src/features/setting/store/components/detail/sections/momo-qr-config/index.ts", "../../src/features/setting/store/components/detail/sections/momo-qr-config/momo-qr-section.tsx", "../../src/features/setting/store/components/detail/sections/order-source/index.ts", "../../src/features/setting/store/components/detail/sections/order-source/order-source-section.tsx", "../../src/features/setting/store/components/detail/sections/order-source/order-source-selection-modal.tsx", "../../src/features/setting/store/components/detail/sections/other-info/index.ts", "../../src/features/setting/store/components/detail/sections/other-info/other-info-section.tsx", "../../src/features/setting/store/components/detail/sections/print-config/index.ts", "../../src/features/setting/store/components/detail/sections/print-config/print-config-section.tsx", "../../src/features/setting/store/components/detail/sections/vietqr-config/index.ts", "../../src/features/setting/store/components/detail/sections/vietqr-config/vietqr-section.tsx", "../../src/features/setting/store/components/detail/ui/action-buttons.tsx", "../../src/features/setting/store/components/detail/ui/address-autocomplete.tsx", "../../src/features/setting/store/components/detail/ui/expired-license-alert.tsx", "../../src/features/setting/store/components/detail/ui/index.ts", "../../src/features/setting/store/components/detail/ui/openstreet-map-picker.tsx", "../../src/features/setting/store/components/detail/ui/page-header.tsx", "../../src/features/setting/store/components/list/index.ts", "../../src/features/setting/store/components/list/stores-pagination.tsx", "../../src/features/setting/store/components/list/data-table/index.ts", "../../src/features/setting/store/components/list/data-table/store-type-cell.tsx", "../../src/features/setting/store/components/list/data-table/stores-columns.tsx", "../../src/features/setting/store/components/list/data-table/stores-data-table.tsx", "../../src/features/setting/store/components/list/data-table/cells/expiry-date-cell.tsx", "../../src/features/setting/store/components/list/data-table/cells/index.ts", "../../src/features/setting/store/components/list/data-table/cells/pos-id-cell.tsx", "../../src/features/setting/store/components/list/data-table/cells/status-cell.tsx", "../../src/features/setting/store/components/list/header/city-select.tsx", "../../src/features/setting/store/components/list/header/index.ts", "../../src/features/setting/store/components/list/header/store-actions-dropdown.tsx", "../../src/features/setting/store/components/list/header/store-header.tsx", "../../src/features/setting/store/components/list/modals/stores-sort-modal.tsx", "../../src/features/setting/store/components/list/modals/sync-secondary-screen-modal.tsx", "../../src/features/setting/store/data/index.ts", "../../src/features/setting/store/data/constants/index.ts", "../../src/features/setting/store/data/schemas/store-schema.ts", "../../src/features/setting/store/data/types/store-types.ts", "../../src/features/setting/store/hooks/index.ts", "../../src/features/setting/store/hooks/detail/index.ts", "../../src/features/setting/store/hooks/detail/use-menu-items.ts", "../../src/features/setting/store/hooks/detail/use-separated-sources.ts", "../../src/features/setting/store/hooks/detail/use-store-data.ts", "../../src/features/setting/store/hooks/detail/use-store-form.ts", "../../src/features/setting/store/hooks/detail/use-store-save.ts", "../../src/features/setting/store/hooks/detail/use-store-status.ts", "../../src/features/setting/store/hooks/license/index.ts", "../../src/features/setting/store/hooks/license/license-api.ts", "../../src/features/setting/store/hooks/license/use-license-check.ts", "../../src/features/setting/store/hooks/list/index.ts", "../../src/features/setting/store/hooks/list/use-cities-transform.ts", "../../src/features/setting/store/hooks/list/use-store-filtering.ts", "../../src/features/setting/store/hooks/list/use-store-list-state.ts", "../../src/features/setting/store/hooks/list/use-store-list-with-pagination.ts", "../../src/features/setting/store/hooks/list/use-store-list.ts", "../../src/features/setting/store/hooks/list/use-stores-with-pagination.ts", "../../src/features/setting/store/pages/store-detail.tsx", "../../src/features/setting/store/pages/store-list.tsx", "../../src/features/setting/store/utils/create-store-mapper.ts", "../../src/features/setting/store/utils/field-mappers.ts", "../../src/features/setting/store/utils/index.ts", "../../src/features/setting/store/utils/update-store-mapper.ts", "../../src/features/setting/table-layout/index.tsx", "../../src/features/setting/table-layout/table-layout-page.tsx", "../../src/features/setting/table-layout/components/index.ts", "../../src/features/setting/table-layout/components/area-tabs/area-tabs.tsx", "../../src/features/setting/table-layout/components/area-tabs/index.ts", "../../src/features/setting/table-layout/components/canvas/index.ts", "../../src/features/setting/table-layout/components/canvas/table-item.tsx", "../../src/features/setting/table-layout/components/canvas/table-layout-canvas.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/configure-tables-modal.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/index.ts", "../../src/features/setting/table-layout/components/configure-table-modal/components/color-picker.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/components/configure-table-controls.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/components/configure-table-grid.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/components/configure-table-header.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/components/configure-table-item.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/components/configure-table-sidebar.tsx", "../../src/features/setting/table-layout/components/configure-table-modal/components/index.ts", "../../src/features/setting/table-layout/components/configure-table-modal/hooks/index.ts", "../../src/features/setting/table-layout/components/configure-table-modal/hooks/use-configure-table-actions.ts", "../../src/features/setting/table-layout/components/configure-table-modal/hooks/use-configure-table-modal.ts", "../../src/features/setting/table-layout/components/configure-table-modal/hooks/use-configure-table-state.ts", "../../src/features/setting/table-layout/components/edit-table-modal/edit-table-info-modal.tsx", "../../src/features/setting/table-layout/components/edit-table-modal/edit-table-preview-modal.tsx", "../../src/features/setting/table-layout/components/edit-table-modal/edit-table-success-modal.tsx", "../../src/features/setting/table-layout/components/edit-table-modal/index.ts", "../../src/features/setting/table-layout/components/header/area-selector.tsx", "../../src/features/setting/table-layout/components/header/header-actions.tsx", "../../src/features/setting/table-layout/components/header/index.ts", "../../src/features/setting/table-layout/components/header/store-selector.tsx", "../../src/features/setting/table-layout/components/header/table-layout-header.tsx", "../../src/features/setting/table-layout/components/import-tables-modal/import-preview-modal.tsx", "../../src/features/setting/table-layout/components/import-tables-modal/import-tables-modal.tsx", "../../src/features/setting/table-layout/components/import-tables-modal/index.ts", "../../src/features/setting/table-layout/components/table-sort-modal/index.ts", "../../src/features/setting/table-layout/components/table-sort-modal/table-sort-modal.tsx", "../../src/features/setting/table-layout/components/table-sort-modal/components/areas-list.tsx", "../../src/features/setting/table-layout/components/table-sort-modal/components/draggable-area-item.tsx", "../../src/features/setting/table-layout/components/table-sort-modal/components/draggable-table-item.tsx", "../../src/features/setting/table-layout/components/table-sort-modal/components/index.ts", "../../src/features/setting/table-layout/components/table-sort-modal/components/table-item.tsx", "../../src/features/setting/table-layout/components/table-sort-modal/components/tables-grid.tsx", "../../src/features/setting/table-layout/components/table-sort-modal/hooks/index.ts", "../../src/features/setting/table-layout/components/table-sort-modal/hooks/use-table-sort-modal.ts", "../../src/features/setting/table-layout/data/index.ts", "../../src/features/setting/table-layout/data/table-layout-types.ts", "../../src/features/setting/table-layout/hooks/index.ts", "../../src/features/setting/table-layout/hooks/use-area-sort.ts", "../../src/features/setting/table-layout/hooks/use-edit-table-handlers.ts", "../../src/features/setting/table-layout/hooks/use-edit-table-preview.ts", "../../src/features/setting/table-layout/hooks/use-import-tables.ts", "../../src/features/setting/table-layout/hooks/use-save-table-positions.ts", "../../src/features/setting/table-layout/hooks/use-sort-handlers.ts", "../../src/features/setting/table-layout/hooks/use-stores-with-areas.ts", "../../src/features/setting/table-layout/hooks/use-table-actions.ts", "../../src/features/setting/table-layout/hooks/use-table-layout-data.ts", "../../src/features/setting/table-layout/hooks/use-table-layout-effects.ts", "../../src/features/setting/table-layout/hooks/use-table-layout-handlers.ts", "../../src/features/setting/table-layout/hooks/use-table-layout-page.ts", "../../src/features/setting/table-layout/hooks/use-table-layout-state.ts", "../../src/features/setting/table-layout/hooks/use-table-layout.ts", "../../src/features/setting/tables/index.ts", "../../src/features/setting/tables/tables-list.tsx", "../../src/features/setting/tables/components/add-tables-modal.tsx", "../../src/features/setting/tables/components/arrange-tables-modal.tsx", "../../src/features/setting/tables/components/configure-tables-modal.tsx", "../../src/features/setting/tables/components/copy-items-to-tables-modal.tsx", "../../src/features/setting/tables/components/create-table-form.tsx", "../../src/features/setting/tables/components/edit-tables-modal.tsx", "../../src/features/setting/tables/components/import-tables-modal.tsx", "../../src/features/setting/tables/components/index.ts", "../../src/features/setting/tables/components/tables-columns.tsx", "../../src/features/setting/tables/components/tables-data-table.tsx", "../../src/features/setting/tables/components/tables-table-header.tsx", "../../src/features/setting/tables/components/tables-table-row.tsx", "../../src/features/setting/tables/components/tables-table-skeleton.tsx", "../../src/features/setting/tables/hooks/index.ts", "../../src/features/setting/tables/hooks/use-auto-create-areas.ts", "../../src/features/setting/tables/hooks/use-tables-edit-excel-parser.ts", "../../src/features/setting/tables/hooks/use-tables-excel-parser.ts", "../../src/features/setting/tables/hooks/use-tables-import-excel-parser.ts", "../../src/features/setting/tables/hooks/use-tables-import.ts", "../../src/features/settings/index.tsx", "../../src/features/settings/account/account-form.tsx", "../../src/features/settings/account/index.tsx", "../../src/features/settings/appearance/appearance-form.tsx", "../../src/features/settings/appearance/index.tsx", "../../src/features/settings/components/content-section.tsx", "../../src/features/settings/components/sidebar-nav.tsx", "../../src/features/settings/display/display-form.tsx", "../../src/features/settings/display/index.tsx", "../../src/features/settings/notifications/index.tsx", "../../src/features/settings/notifications/notifications-form.tsx", "../../src/features/settings/profile/index.tsx", "../../src/features/settings/profile/profile-form.tsx", "../../src/features/users/index.tsx", "../../src/features/users/components/data-table-column-header.tsx", "../../src/features/users/components/data-table-faceted-filter.tsx", "../../src/features/users/components/data-table-pagination.tsx", "../../src/features/users/components/data-table-row-actions.tsx", "../../src/features/users/components/data-table-toolbar.tsx", "../../src/features/users/components/data-table-view-options.tsx", "../../src/features/users/components/users-action-dialog.tsx", "../../src/features/users/components/users-columns.tsx", "../../src/features/users/components/users-delete-dialog.tsx", "../../src/features/users/components/users-dialogs.tsx", "../../src/features/users/components/users-invite-dialog.tsx", "../../src/features/users/components/users-primary-buttons.tsx", "../../src/features/users/components/users-table.tsx", "../../src/features/users/context/users-context.tsx", "../../src/features/users/data/data.ts", "../../src/features/users/data/schema.ts", "../../src/features/users/data/users.ts", "../../src/hooks/index.ts", "../../src/hooks/use-accounting-sales-all.ts", "../../src/hooks/use-all-stores.ts", "../../src/hooks/use-auth.ts", "../../src/hooks/use-current-brand.ts", "../../src/hooks/use-deleted-orders.ts", "../../src/hooks/use-deleted-stores.ts", "../../src/hooks/use-excel-export.ts", "../../src/hooks/use-items.ts", "../../src/hooks/use-pos-data.ts", "../../src/hooks/use-reports-context.ts", "../../src/hooks/use-revenue-data.ts", "../../src/hooks/use-sale-not-sync-vat-data.ts", "../../src/hooks/use-sales-voucher-data.ts", "../../src/hooks/use-source-data.ts", "../../src/hooks/use-sources-by-stores.ts", "../../src/hooks/use-sources.ts", "../../src/hooks/use-stores-data.ts", "../../src/hooks/use-stores-discount-data.ts", "../../src/hooks/use-top-deleted-stores.ts", "../../src/hooks/use-top-items.ts", "../../src/hooks/use-top-stores.ts", "../../src/hooks/use-voucher-data.ts", "../../src/hooks/api/index.ts", "../../src/hooks/api/use-areas.ts", "../../src/hooks/api/use-channels.ts", "../../src/hooks/api/use-cities.ts", "../../src/hooks/api/use-combos.ts", "../../src/hooks/api/use-crm-item-types.ts", "../../src/hooks/api/use-customization-by-id.ts", "../../src/hooks/api/use-customizations.ts", "../../src/hooks/api/use-devices.ts", "../../src/hooks/api/use-discount-payment.ts", "../../src/hooks/api/use-discount-programs.ts", "../../src/hooks/api/use-discount.ts", "../../src/hooks/api/use-images.ts", "../../src/hooks/api/use-item-categories.ts", "../../src/hooks/api/use-item-classes.ts", "../../src/hooks/api/use-item-sale-summary.ts", "../../src/hooks/api/use-item-types.ts", "../../src/hooks/api/use-items.ts", "../../src/hooks/api/use-ktv-month-report.ts", "../../src/hooks/api/use-membership-discounts.ts", "../../src/hooks/api/use-menu-combos.ts", "../../src/hooks/api/use-menu-items.ts", "../../src/hooks/api/use-order-logs.ts", "../../src/hooks/api/use-order-sources.ts", "../../src/hooks/api/use-packages.ts", "../../src/hooks/api/use-payment-discounts.ts", "../../src/hooks/api/use-payment-method-revenue.ts", "../../src/hooks/api/use-payment-methods.ts", "../../src/hooks/api/use-printer-positions.ts", "../../src/hooks/api/use-printers.ts", "../../src/hooks/api/use-promotion-revenue.ts", "../../src/hooks/api/use-promotion-sale-summary.ts", "../../src/hooks/api/use-promotions.ts", "../../src/hooks/api/use-quantity-days.ts", "../../src/hooks/api/use-regular-discounts.ts", "../../src/hooks/api/use-removed-items.ts", "../../src/hooks/api/use-reports-sources.ts", "../../src/hooks/api/use-reports-stores.ts", "../../src/hooks/api/use-reports-weekdays.ts", "../../src/hooks/api/use-roles.ts", "../../src/hooks/api/use-sale-by-date.ts", "../../src/hooks/api/use-sale-summary-overview.ts", "../../src/hooks/api/use-sales-channels.ts", "../../src/hooks/api/use-service-charge.ts", "../../src/hooks/api/use-sources.ts", "../../src/hooks/api/use-stores.ts", "../../src/hooks/api/use-tables.ts", "../../src/hooks/api/use-units.ts", "../../src/hooks/api/use-update-customization.ts", "../../src/hooks/api/use-users.ts", "../../src/hooks/api/use-vietqr.ts", "../../src/hooks/api/payment-method/index.ts", "../../src/hooks/api/payment-method/use-payment-method-details.ts", "../../src/hooks/crm/index.ts", "../../src/hooks/crm/use-billing-detail.ts", "../../src/hooks/crm/use-billing-services.ts", "../../src/hooks/crm/use-connect-crm.ts", "../../src/hooks/crm/use-create-extra-point.ts", "../../src/hooks/crm/use-crm-image-upload.ts", "../../src/hooks/crm/use-customer-report.ts", "../../src/hooks/crm/use-extra-point.ts", "../../src/hooks/crm/use-membership-type.ts", "../../src/hooks/crm/use-pos-parent-settings.ts", "../../src/hooks/crm/use-register-page-config.ts", "../../src/hooks/crm/use-save-register-page-config.ts", "../../src/hooks/crm/use-update-extra-point.ts", "../../src/hooks/crm/use-using-daily.ts", "../../src/hooks/crm/use-using-month.ts", "../../src/hooks/local-storage/index.ts", "../../src/hooks/local-storage/use-brands-data.ts", "../../src/hooks/local-storage/use-pos-cities-data.ts", "../../src/hooks/local-storage/use-pos-company-data.ts", "../../src/hooks/local-storage/use-pos-stores-data.ts", "../../src/hooks/ui/use-dialog-state.tsx", "../../src/hooks/ui/use-mobile.tsx", "../../src/lib/auth-api.ts", "../../src/lib/auth.ts", "../../src/lib/bill-template-api.ts", "../../src/lib/channels-api.ts", "../../src/lib/combo-api.ts", "../../src/lib/combos-api.ts", "../../src/lib/customizations-api.ts", "../../src/lib/customizations-excel.ts", "../../src/lib/date-utils.ts", "../../src/lib/deleted-orders-api.ts", "../../src/lib/devices-api.ts", "../../src/lib/discount-payment-api.ts", "../../src/lib/discounts-api.ts", "../../src/lib/excel-export.ts", "../../src/lib/id-generators.ts", "../../src/lib/index.ts", "../../src/lib/item-api.ts", "../../src/lib/item-categories-api.ts", "../../src/lib/item-classes-api.ts", "../../src/lib/item-removed-api.ts", "../../src/lib/item-types-api.ts", "../../src/lib/items-api.ts", "../../src/lib/membership-discounts-api.ts", "../../src/lib/menu-items-api.ts", "../../src/lib/menu-schedule-api.ts", "../../src/lib/order-logs-api.ts", "../../src/lib/order-sources-api.ts", "../../src/lib/payment-methods-api.ts", "../../src/lib/printer-position-api.ts", "../../src/lib/printer-positions-api.ts", "../../src/lib/printers-api.ts", "../../src/lib/promotion-api.ts", "../../src/lib/promotion-revenue-api.ts", "../../src/lib/promotions-api.ts", "../../src/lib/quantity-day-api.ts", "../../src/lib/regular-discounts-api.ts", "../../src/lib/revenue-api.ts", "../../src/lib/roles-api.ts", "../../src/lib/sale-sources-api.ts", "../../src/lib/sales-api-core.ts", "../../src/lib/sales-api-vat.ts", "../../src/lib/sales-api-voucher.ts", "../../src/lib/sales-api.ts", "../../src/lib/sales-cache.ts", "../../src/lib/sales-channels-api.ts", "../../src/lib/sales-types.ts", "../../src/lib/service-charge-api.ts", "../../src/lib/sources-api.ts", "../../src/lib/stores-api.ts", "../../src/lib/string-utils.ts", "../../src/lib/table-api.ts", "../../src/lib/table-layout-api.ts", "../../src/lib/tables-api.ts", "../../src/lib/units-api.ts", "../../src/lib/users-api.ts", "../../src/lib/utils.ts", "../../src/lib/vietqr-api.ts", "../../src/lib/api/index.ts", "../../src/lib/api/crm/auth-by-token-api.ts", "../../src/lib/api/crm/billing-detail.ts", "../../src/lib/api/crm/billing-services-api.ts", "../../src/lib/api/crm/connect-crm-api.ts", "../../src/lib/api/crm/crm-api.ts", "../../src/lib/api/crm/customer-report-api.ts", "../../src/lib/api/crm/extra-point-api.ts", "../../src/lib/api/crm/images-api.ts", "../../src/lib/api/crm/index.ts", "../../src/lib/api/crm/marketing-api.ts", "../../src/lib/api/crm/membership-api.ts", "../../src/lib/api/crm/membership-type-api.ts", "../../src/lib/api/crm/settings-api.ts", "../../src/lib/api/crm/system-log-api.ts", "../../src/lib/api/crm/using-daily.ts", "../../src/lib/api/crm/using-month.ts", "../../src/lib/api/pos/areas-api.ts", "../../src/lib/api/pos/images-api.ts", "../../src/lib/api/pos/index.ts", "../../src/lib/api/pos/item-sale-summary-api.ts", "../../src/lib/api/pos/ktv-month-report-api.ts", "../../src/lib/api/pos/packages-api.ts", "../../src/lib/api/pos/payment-method-revenue-api.ts", "../../src/lib/api/pos/pos-api.ts", "../../src/lib/api/pos/promotion-revenue-api.ts", "../../src/lib/api/pos/promotion-sale-summary-api.ts", "../../src/lib/api/pos/reports-sources-api.ts", "../../src/lib/api/pos/reports-stores-api.ts", "../../src/lib/api/pos/reports-weekdays-api.ts", "../../src/lib/api/pos/sale-by-date-api.ts", "../../src/lib/api/pos/sale-summary-overview-api.ts", "../../src/routes/__root.tsx", "../../src/routes/(auth)/forgot-password.tsx", "../../src/routes/(auth)/otp.tsx", "../../src/routes/(auth)/sign-in-2.tsx", "../../src/routes/(auth)/sign-in.tsx", "../../src/routes/(auth)/sign-up.tsx", "../../src/routes/(errors)/401.tsx", "../../src/routes/(errors)/403.tsx", "../../src/routes/(errors)/404.tsx", "../../src/routes/(errors)/500.tsx", "../../src/routes/(errors)/503.tsx", "../../src/routes/_authenticated/index.tsx", "../../src/routes/_authenticated/membership.tsx", "../../src/routes/_authenticated/route.tsx", "../../src/routes/_authenticated/crm/billing-detail.tsx", "../../src/routes/_authenticated/crm/config-register-page.tsx", "../../src/routes/_authenticated/crm/connect-crm.tsx", "../../src/routes/_authenticated/crm/customer-group.tsx", "../../src/routes/_authenticated/crm/customer-list.tsx", "../../src/routes/_authenticated/crm/customer-profile.tsx", "../../src/routes/_authenticated/crm/pricing-table.tsx", "../../src/routes/_authenticated/crm/settings.tsx", "../../src/routes/_authenticated/crm/system-log.tsx", "../../src/routes/_authenticated/crm/using-month.tsx", "../../src/routes/_authenticated/crm/food-item-review/items.tsx", "../../src/routes/_authenticated/crm/general-setups/account.tsx", "../../src/routes/_authenticated/crm/general-setups/combo-special.tsx", "../../src/routes/_authenticated/crm/general-setups/combo.tsx", "../../src/routes/_authenticated/crm/general-setups/create-user.tsx", "../../src/routes/_authenticated/crm/general-setups/item-type.tsx", "../../src/routes/_authenticated/crm/general-setups/items.tsx", "../../src/routes/_authenticated/crm/general-setups/store-menu.tsx", "../../src/routes/_authenticated/crm/loyalty/extra-point.tsx", "../../src/routes/_authenticated/crm/loyalty/membership-type.tsx", "../../src/routes/_authenticated/crm/report/customer.tsx", "../../src/routes/_authenticated/crm/report/rating-feedback.tsx", "../../src/routes/_authenticated/crm/report/revenue.tsx", "../../src/routes/_authenticated/crm/report/sale-manager.tsx", "../../src/routes/_authenticated/crm/report/voucher.tsx", "../../src/routes/_authenticated/devices/detail.$id.tsx", "../../src/routes/_authenticated/devices/list.tsx", "../../src/routes/_authenticated/devices/new.tsx", "../../src/routes/_authenticated/devices/types.tsx", "../../src/routes/_authenticated/employee/detail.$userid.tsx", "../../src/routes/_authenticated/employee/detail.tsx", "../../src/routes/_authenticated/employee/list.tsx", "../../src/routes/_authenticated/employee/role.tsx", "../../src/routes/_authenticated/employee/role/detail.tsx", "../../src/routes/_authenticated/employee/role/index.tsx", "../../src/routes/_authenticated/employee/role/detail/$roleid.tsx", "../../src/routes/_authenticated/general-setups/account.tsx", "../../src/routes/_authenticated/general-setups/create-user.tsx", "../../src/routes/_authenticated/help-center/index.tsx", "../../src/routes/_authenticated/menu/item-class.tsx", "../../src/routes/_authenticated/menu/quantity-day.tsx", "../../src/routes/_authenticated/menu/schedule.tsx", "../../src/routes/_authenticated/menu/categories/categories-in-brand.tsx", "../../src/routes/_authenticated/menu/categories/categories-in-store.tsx", "../../src/routes/_authenticated/menu/categories/categories-in-brand/detail.tsx", "../../src/routes/_authenticated/menu/categories/categories-in-brand/detail/$id.tsx", "../../src/routes/_authenticated/menu/category/detail.tsx", "../../src/routes/_authenticated/menu/category/detail/$id.tsx", "../../src/routes/_authenticated/menu/category/detail/index.tsx", "../../src/routes/_authenticated/menu/category-in-store/detail.tsx", "../../src/routes/_authenticated/menu/category-in-store/detail/$id.tsx", "../../src/routes/_authenticated/menu/category-in-store/detail/index.tsx", "../../src/routes/_authenticated/menu/customization/new.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-city/detail.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-city/index.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-city/detail/$customizationid.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-city/detail/index.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-store/create.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-store/detail.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-store/index.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-store/detail/$customizationid.tsx", "../../src/routes/_authenticated/menu/customization/customization-in-store/detail/index.tsx", "../../src/routes/_authenticated/menu/item-class/detail.tsx", "../../src/routes/_authenticated/menu/item-class/index.tsx", "../../src/routes/_authenticated/menu/item-class/detail/$id.tsx", "../../src/routes/_authenticated/menu/item-class/detail/index.tsx", "../../src/routes/_authenticated/menu/item-removed/item-removed-in-city.tsx", "../../src/routes/_authenticated/menu/item-removed/item-removed-in-store.tsx", "../../src/routes/_authenticated/menu/items/items-in-city/detail.tsx", "../../src/routes/_authenticated/menu/items/items-in-city/index.tsx", "../../src/routes/_authenticated/menu/items/items-in-city/detail/$id.tsx", "../../src/routes/_authenticated/menu/items/items-in-city/detail/index.tsx", "../../src/routes/_authenticated/menu/items/items-in-store/detail.tsx", "../../src/routes/_authenticated/menu/items/items-in-store/index.tsx", "../../src/routes/_authenticated/menu/items/items-in-store/detail/$id.tsx", "../../src/routes/_authenticated/menu/items/items-in-store/detail/index.tsx", "../../src/routes/_authenticated/report/accounting/sale-detail-audit.tsx", "../../src/routes/_authenticated/report/accounting/invoices/sale-sync-vat.tsx", "../../src/routes/_authenticated/report/revenue/sale-summary.tsx", "../../src/routes/_authenticated/report/revenue/categories/payment-method.tsx", "../../src/routes/_authenticated/report/revenue/categories/promotion.tsx", "../../src/routes/_authenticated/report/revenue/categories/source.tsx", "../../src/routes/_authenticated/report/revenue/revenue/general.tsx", "../../src/routes/_authenticated/sale/combo.tsx", "../../src/routes/_authenticated/sale/discount-payment.tsx", "../../src/routes/_authenticated/sale/service-charge.tsx", "../../src/routes/_authenticated/sale/combo/detail.tsx", "../../src/routes/_authenticated/sale/combo/detail/$id.tsx", "../../src/routes/_authenticated/sale/combo/detail/index.tsx", "../../src/routes/_authenticated/sale/discount/membership.tsx", "../../src/routes/_authenticated/sale/discount/regular.tsx", "../../src/routes/_authenticated/sale/discount/membership/detail.tsx", "../../src/routes/_authenticated/sale/discount/membership/index.tsx", "../../src/routes/_authenticated/sale/discount/membership/detail/$id.tsx", "../../src/routes/_authenticated/sale/discount/membership/detail/index.tsx", "../../src/routes/_authenticated/sale/discount/membership/detail/$companyid/$brandid.tsx", "../../src/routes/_authenticated/sale/discount/regular/detail.tsx", "../../src/routes/_authenticated/sale/discount/regular/index.tsx", "../../src/routes/_authenticated/sale/discount/regular/detail/$id.tsx", "../../src/routes/_authenticated/sale/discount/regular/detail/index.tsx", "../../src/routes/_authenticated/sale/discount-payment/detail.tsx", "../../src/routes/_authenticated/sale/discount-payment/index.tsx", "../../src/routes/_authenticated/sale/discount-payment/detail/$id.tsx", "../../src/routes/_authenticated/sale/discount-payment/detail/index.tsx", "../../src/routes/_authenticated/sale/promotion/index.tsx", "../../src/routes/_authenticated/sale/service-charge/detail.tsx", "../../src/routes/_authenticated/sale/service-charge/index.tsx", "../../src/routes/_authenticated/sale/service-charge/detail/$id.tsx", "../../src/routes/_authenticated/sale/service-charge/detail/index.tsx", "../../src/routes/_authenticated/sale-channel/discount.tsx", "../../src/routes/_authenticated/sale-channel/channel/detail.tsx", "../../src/routes/_authenticated/sale-channel/channel/index.tsx", "../../src/routes/_authenticated/sale-channel/channel/detail/$uuid.tsx", "../../src/routes/_authenticated/sale-channel/discount/detail.tsx", "../../src/routes/_authenticated/sale-channel/discount/index.tsx", "../../src/routes/_authenticated/sale-channel/discount/detail/$id.tsx", "../../src/routes/_authenticated/sale-channel/discount/detail/index.tsx", "../../src/routes/_authenticated/setting/area.tsx", "../../src/routes/_authenticated/setting/store.tsx", "../../src/routes/_authenticated/setting/table-layout.tsx", "../../src/routes/_authenticated/setting/table.tsx", "../../src/routes/_authenticated/setting/area/detail.tsx", "../../src/routes/_authenticated/setting/area/index.tsx", "../../src/routes/_authenticated/setting/area/detail/$areaid.tsx", "../../src/routes/_authenticated/setting/area/detail/index.tsx", "../../src/routes/_authenticated/setting/bill-model/index.tsx", "../../src/routes/_authenticated/setting/payment-method/index.tsx", "../../src/routes/_authenticated/setting/payment-method/detail/$paymentmethodid.tsx", "../../src/routes/_authenticated/setting/payment-method/detail/index.tsx", "../../src/routes/_authenticated/setting/printer-position/printer-position-in-brand/index.tsx", "../../src/routes/_authenticated/setting/printer-position/printer-position-in-store/index.tsx", "../../src/routes/_authenticated/setting/source/index.tsx", "../../src/routes/_authenticated/setting/source/detail/$sourceid.tsx", "../../src/routes/_authenticated/setting/source/detail/index.tsx", "../../src/routes/_authenticated/setting/store/detail.tsx", "../../src/routes/_authenticated/setting/store/index.tsx", "../../src/routes/_authenticated/setting/store/detail/$storeid.tsx", "../../src/routes/_authenticated/setting/store/detail/index.tsx", "../../src/routes/_authenticated/setting/table/detail.tsx", "../../src/routes/_authenticated/setting/table/index.tsx", "../../src/routes/_authenticated/setting/table/detail/$areaid.tsx", "../../src/routes/_authenticated/setting/table/detail/$tableid.tsx", "../../src/routes/_authenticated/setting/table/detail/index.tsx", "../../src/routes/_authenticated/setting/table-layout/index.tsx", "../../src/routes/_authenticated/settings/account.tsx", "../../src/routes/_authenticated/settings/appearance.tsx", "../../src/routes/_authenticated/settings/display.tsx", "../../src/routes/_authenticated/settings/index.tsx", "../../src/routes/_authenticated/settings/notifications.tsx", "../../src/routes/_authenticated/settings/route.tsx", "../../src/routes/_authenticated/users/index.tsx", "../../src/routes/clerk/route.tsx", "../../src/routes/clerk/(auth)/route.tsx", "../../src/routes/clerk/(auth)/sign-in.tsx", "../../src/routes/clerk/(auth)/sign-up.tsx", "../../src/routes/clerk/_authenticated/route.tsx", "../../src/routes/clerk/_authenticated/user-management.tsx", "../../src/stores/authstore.ts", "../../src/stores/index.ts", "../../src/stores/posstore.ts", "../../src/stores/user.ts", "../../src/types/auth.ts", "../../src/types/brands-data.ts", "../../src/types/categories.ts", "../../src/types/channels.ts", "../../src/types/combos.ts", "../../src/types/customizations.ts", "../../src/types/device.ts", "../../src/types/discount-payment.ts", "../../src/types/discounts.ts", "../../src/types/index.ts", "../../src/types/item-categories.ts", "../../src/types/item-class.ts", "../../src/types/item-removed.ts", "../../src/types/package-types.ts", "../../src/types/pagination.ts", "../../src/types/payment-method.ts", "../../src/types/pos-company.ts", "../../src/types/role.ts", "../../src/types/service-charge.ts", "../../src/types/sources.ts", "../../src/types/store.ts", "../../src/types/table.ts", "../../src/types/user.ts", "../../src/types/api/combo-types.ts", "../../src/types/api/combo.ts", "../../src/types/api/customer-report.ts", "../../src/types/api/discount-types.ts", "../../src/types/api/index.ts", "../../src/types/api/item.ts", "../../src/types/api/items-types.ts", "../../src/types/api/ktv-reports.ts", "../../src/types/api/menu-items.ts", "../../src/types/api/order-sources.ts", "../../src/types/api/reports-sources.ts", "../../src/types/api/reports-stores.ts", "../../src/types/api/reports-weekdays.ts", "../../src/types/api/revenue-payment-method.ts", "../../src/types/api/revenue-promotion.ts", "../../src/types/api/sale-by-date-types.ts", "../../src/types/api/sale-summary-overview.ts", "../../src/types/api/sources-types.ts", "../../src/types/api/vietqr-types.ts", "../../src/types/api/crm/base.ts", "../../src/types/api/crm/billing-detail.ts", "../../src/types/api/crm/connect-crm.ts", "../../src/types/api/crm/customer-report.ts", "../../src/types/api/crm/extra-point.ts", "../../src/types/api/crm/index.ts", "../../src/types/api/crm/membership-type.ts", "../../src/types/api/crm/register-page-config.ts", "../../src/types/api/crm/settings.ts", "../../src/types/api/crm/using-daily.ts", "../../src/utils/applied-sources-utils.ts", "../../src/utils/date-filters.ts", "../../src/utils/date-utils.ts", "../../src/utils/error-utils.ts", "../../src/utils/excel-util.ts", "../../src/utils/file-upload.ts", "../../src/utils/handle-server-error.ts", "../../src/utils/image-utils.ts", "../../src/utils/index.ts", "../../src/utils/indexeddb.ts", "../../src/utils/localstorage-debug.ts", "../../src/utils/responsive-classes.ts"], "errors": true, "version": "5.8.3"}