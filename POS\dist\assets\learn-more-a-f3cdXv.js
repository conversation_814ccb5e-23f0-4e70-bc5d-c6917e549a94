import{j as a,c as n,B as l}from"./index-B283E1a3.js";import{P as m,a as t,b as r}from"./popover-CCXriU_R.js";import{c}from"./createReactComponent-WabRa4kY.js";/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var u=c("outline","question-mark","IconQuestionMark",[["path",{d:"M8 8a3.5 3 0 0 1 3.5 -3h1a3.5 3 0 0 1 3.5 3a3 3 0 0 1 -2 3a3 4 0 0 0 -2 4",key:"svg-0"}],["path",{d:"M12 19l0 .01",key:"svg-1"}]]);function j({children:o,contentProps:s,triggerProps:e,...i}){return a.jsxs(m,{...i,children:[a.jsx(t,{asChild:!0,...e,className:n("size-5 rounded-full",e==null?void 0:e.className),children:a.jsxs(l,{variant:"outline",size:"icon",children:[a.jsx("span",{className:"sr-only",children:"Learn more"}),a.jsx(u,{className:"size-3"})]})}),a.jsx(r,{side:"top",align:"start",...s,className:n("text-muted-foreground text-sm",s==null?void 0:s.className),children:o})]})}export{j as L};
