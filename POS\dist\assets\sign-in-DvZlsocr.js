import{j as r}from"./index-B283E1a3.js";import{C as e,a as t,b as o,c as i,d as s,f as a}from"./card-Cd40a-ap.js";import{A as n}from"./auth-layout-7lbf9HLX.js";import{U as m}from"./user-auth-form-BbFk7p2d.js";import"./form-dNL1hWKC.js";import"./zod-BOoGjb2n.js";import"./use-auth-Cjjp-n-O.js";import"./useMutation-Bf5OzDko.js";import"./utils-km2FGkQ4.js";import"./pos-api-C7RsFAun.js";import"./input-Bx4sCRS0.js";import"./password-input-twuS4daZ.js";import"./createReactComponent-WabRa4kY.js";import"./IconBrandGithub-Bczl3_XA.js";function c(){return r.jsx(n,{children:r.jsxs(e,{className:"gap-4",children:[r.jsxs(t,{children:[r.jsx(o,{className:"text-lg tracking-tight",children:"Login"}),r.jsxs(i,{children:["Enter your email and password below to ",r.jsx("br",{}),"log into your account"]})]}),r.jsx(s,{children:r.jsx(m,{})}),r.jsx(a,{children:r.jsxs("p",{className:"text-muted-foreground px-8 text-center text-sm",children:["By clicking login, you agree to our"," ",r.jsx("a",{href:"/terms",className:"hover:text-primary underline underline-offset-4",children:"Terms of Service"})," ","and"," ",r.jsx("a",{href:"/privacy",className:"hover:text-primary underline underline-offset-4",children:"Privacy Policy"}),"."]})})]})})}const A=c;export{A as component};
