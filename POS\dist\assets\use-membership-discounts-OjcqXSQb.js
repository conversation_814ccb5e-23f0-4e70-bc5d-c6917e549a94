import{u as a}from"./useQuery-Cc4LgMzN.js";import{a3 as m,a4 as c,b as g,l as h}from"./index-B283E1a3.js";import{u as b}from"./useMutation-Bf5OzDko.js";import{b as o}from"./pos-api-C7RsFAun.js";import{convertMembershipDiscountApiData as p}from"./discounts-DCn5I4UW.js";import{Q as s}from"./query-keys-3lmd-xp6.js";async function U(e={}){try{const r=new URLSearchParams;e.companyUid&&r.append("company_uid",e.companyUid),e.brandUid&&r.append("brand_uid",e.brandUid),e.page&&r.append("page",e.page.toString()),e.listStoreUid&&e.listStoreUid.length>0&&r.append("list_store_uid",e.listStoreUid.join(",")),e.status&&r.append("status",e.status),e.active!==void 0&&r.append("active",e.active.toString());const i=(await o.get(`/mdata/v1/discount-membership?${r.toString()}`)).data;return i!=null&&i.data?i.data.map(u=>p(u,"Store Name")):[]}catch(r){return console.error("Error fetching membership discounts:",r),[]}}async function f(e){try{return(await o.post("/mdata/v1/discount-membership",e)).data.data}catch(r){throw console.error("Error creating membership discount:",r),r}}async function S(e){try{await o.put("/mdata/v1/discount-membership",e)}catch(r){throw console.error("Error updating membership discount:",r),r}}async function _(e){try{const r=new URLSearchParams({skip_limit:"true",company_uid:e.companyUid,brand_uid:e.brandUid,store_uid:e.storeUid}),i=(await o.get(`/mdata/v1/discount-membership?${r.toString()}`)).data;return(i==null?void 0:i.data)||[]}catch(r){return console.error("Error fetching membership discount programs:",r),[]}}async function D(e){try{const t=`/mdata/v1/discount-membership?${new URLSearchParams({company_uid:e.companyUid,brand_uid:e.brandUid,id:e.id,store_uid:e.storeUid}).toString()}`;console.log("🔥 Membership Discount Detail API Call:",t),console.log("🔥 Params:",e);const i=await o.get(t);console.log("🔥 Membership Discount Detail Response:",i.data);const n=i.data;if(!(n!=null&&n.data))throw new Error("Membership discount not found");const u=n.data;return console.log("🔥 Parsed Membership Discount:",u),u}catch(r){throw console.error("Error fetching membership discount by ID:",r),r}}async function M(e){var r;try{const i=`/mdata/v1/promotions?${new URLSearchParams({skip_limit:"true",company_uid:e.companyUid,brand_uid:e.brandUid,store_uid:e.storeUid,partner_auto_gen:"0",active:"1"}).toString()}`;console.log("🔥 Membership Discount Promotions API Call:",i),console.log("🔥 Params:",e);const n=await o.get(i);console.log("🔥 Membership Discount Promotions Response:",n.data);const d=(Array.isArray((r=n.data)==null?void 0:r.data)?n.data.data:[]).flatMap(y=>Array.isArray(y.list_data)?y.list_data.map(l=>({promotion_uid:l.id,promotion_name:l.promotion_name})):[]);return console.log("🔥 Parsed Membership Promotions:",d),d}catch(t){return console.error("Error fetching membership discount promotions:",t),[]}}async function w(e){try{const r={company_uid:e.companyUid,brand_uid:e.brandUid,list_discount_uid:e.listDiscountUid,store_uid_root:e.storeUidRoot,store_uid_target:e.storeUidTarget};return(await o.post("/mdata/v1/discount-membership/clone",r)).data.data}catch(r){throw console.error("Error cloning membership discounts:",r),r}}function I(e={}){return a({queryKey:[s.DISCOUNTS,"membership",e],queryFn:()=>U(e),staleTime:2*60*1e3,gcTime:5*60*1e3})}function N(e){const r=m();return b({mutationFn:t=>f(t),onSuccess:()=>{var t;c.success("Tạo membership discount thành công"),r.invalidateQueries({queryKey:[s.DISCOUNTS]}),(t=e==null?void 0:e.onSuccess)==null||t.call(e)},onError:t=>{var n;const i=t.message||"Có lỗi xảy ra khi tạo membership discount";c.error(i),(n=e==null?void 0:e.onError)==null||n.call(e,i)}})}function K(e){const r=m();return b({mutationFn:S,onSuccess:()=>{var t;c.success("Cập nhật membership discount thành công"),r.invalidateQueries({queryKey:[s.DISCOUNTS]}),(t=e==null?void 0:e.onSuccess)==null||t.call(e)},onError:t=>{var n;const i=t.message||"Có lỗi xảy ra khi cập nhật membership discount";c.error(i),(n=e==null?void 0:e.onError)==null||n.call(e,i)}})}function F(e){return a({queryKey:[s.DISCOUNTS,"programs",e.companyUid,e.brandUid,e.storeUid],queryFn:async()=>{if(!e.companyUid||!e.brandUid||!e.storeUid)throw new Error("Missing required parameters");return _({companyUid:e.companyUid,brandUid:e.brandUid,storeUid:e.storeUid})},enabled:e.enabled&&!!e.companyUid&&!!e.brandUid&&!!e.storeUid,staleTime:2*60*1e3,gcTime:5*60*1e3})}function O(){const e=m();return b({mutationFn:w,onSuccess:()=>{e.invalidateQueries({queryKey:[s.DISCOUNTS]})}})}function R(){const{selectedBrand:e}=h();return a({queryKey:[s.DISCOUNTS,"membership-types",e==null?void 0:e.brandId],queryFn:async()=>{if(!(e!=null&&e.brandId))throw new Error("Brand ID is required");return(await o.get(`/v3/forward/food-book/pos-cms/membership-type?page=1&results_per_page=15000&pos_parent=${e.brandId}`)).data.data||[]},enabled:!!(e!=null&&e.brandId),staleTime:5*60*1e3,gcTime:10*60*1e3})}function A(e,r={}){const{company:t}=g(),{selectedBrand:i}=h(),{enabled:n=!0}=r;return a({queryKey:[s.DISCOUNTS,"membership-promotions",e,t==null?void 0:t.id,i==null?void 0:i.id],queryFn:()=>{if(!(t!=null&&t.id)||!(i!=null&&i.id)||!e)throw new Error("Thiếu thông tin cần thiết để lấy danh sách CTKM");return M({companyUid:t.id,brandUid:i.id,storeUid:e})},enabled:n&&!!e&&!!(t!=null&&t.id)&&!!(i!=null&&i.id),staleTime:2*60*1e3,gcTime:5*60*1e3})}function Q(e,r){const{company:t}=g(),{selectedBrand:i}=h();return a({queryKey:[s.DISCOUNTS,"membership-detail",e,r,t==null?void 0:t.id,i==null?void 0:i.id],queryFn:()=>{if(!(t!=null&&t.id)||!(i!=null&&i.id)||!e||!r)throw new Error("Thiếu thông tin cần thiết để lấy chi tiết membership discount");return D({companyUid:t.id,brandUid:i.id,id:e,storeUid:r})},enabled:!!e&&!!r&&!!(t!=null&&t.id)&&!!(i!=null&&i.id),staleTime:2*60*1e3,gcTime:5*60*1e3})}export{F as a,I as b,K as c,Q as d,R as e,A as f,N as g,O as u};
