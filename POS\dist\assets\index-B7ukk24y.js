import{h as q,i as B,r as j,a4 as v,j as e,B as z}from"./index-B283E1a3.js";import{M as U,L as X,a as H}from"./loading-spinner-eLAGL-LT.js";import"./search-context-CZoJZmsi.js";import"./date-range-picker-B7aoXHt3.js";import{L as T}from"./form-dNL1hWKC.js";import"./pos-api-C7RsFAun.js";import"./vietqr-api-BbJFOv9v.js";import{u as W}from"./use-items-m6ZLkQ_w.js";import{u as K}from"./use-customization-by-id-CI3Gk9XA.js";import"./user-Cxn8z8PZ.js";import"./crm-api-CE_jLH-z.js";import{P as $}from"./modal-COeiv6He.js";import{I as D}from"./input-Bx4sCRS0.js";import{T as Y,a as J,b as E,c as V,d as Q,e as G}from"./table-vHuVXp9f.js";import{u as Z}from"./useCanGoBack-Dw8taFBS.js";import{a as ee}from"./use-customizations-BFIcwtLL.js";import{u as te}from"./use-update-customization-CptRo3Io.js";import{C as _}from"./checkbox-DtNgKdj2.js";import{C as A,a as k,b as L}from"./collapsible-lV085WFw.js";import{C as R,S as se,a as ne,b as ae,c as ie,d as oe}from"./select-BzVwefGp.js";import{C as F}from"./chevron-right-CVT48KKP.js";import{I as re}from"./IconX-CiUjqjQQ.js";import{X as ce}from"./calendar-AxR9kFpj.js";import{u as le}from"./use-pos-data-D7fYs1vU.js";function de(t={}){const r=q(),u=B(),l=Z(),[a,m]=j.useState(""),[i,g]=j.useState(""),[C,o]=j.useState(!1),[f,p]=j.useState(null),N=ee(),x=te(),d=()=>{l?u.history.back():r({to:"/menu/customization/customization-in-city"})},s=async(c,h,n)=>{var y;if(!a.trim()){v.error("Vui lòng nhập tên customization");return}if(!i){v.error("Vui lòng chọn thành phố");return}if(c.length===0){v.error("Vui lòng tạo ít nhất một nhóm");return}if(h.size===0){v.error("Vui lòng chọn ít nhất một món áp dụng");return}o(!0);try{const I=c.map(S=>({LstItem_Id:S.items.map(w=>w.code||w.id),Min_Permitted:S.minRequired,Max_Permitted:S.maxAllowed,Name:S.name,id:`CUS_GROUP_${Math.random().toString(36).substring(2,7).toUpperCase()}`})),M=Array.from(h).map(S=>{const w=n.find(b=>b.id===S);return(w==null?void 0:w.item_id)||S});t.isEdit&&t.customizationId?(await x.mutateAsync({customizationId:t.customizationId,name:a.trim(),cityUid:i,data:{LstItem_Options:I},listItem:M,sort:1e3,isUpdateSameCustomization:!1,existingCustomization:f||void 0}),v.success("Đã cập nhật customization thành công!")):(await N.mutateAsync({name:a.trim(),cityUid:i,data:{LstItem_Options:I},listItem:M,sort:1e3,isUpdateSameCustomization:!1}),v.success("Đã tạo customization thành công!")),(y=t.onSuccess)==null||y.call(t),l?u.history.back():r({to:"/menu/customization/customization-in-city"})}catch{const I=t.isEdit?"Lỗi khi cập nhật customization. Vui lòng thử lại.":"Lỗi khi tạo customization. Vui lòng thử lại.";v.error(I)}finally{o(!1)}};return{customizationName:a,selectedCityId:i,isSubmitting:C,existingCustomization:f,customizationId:t.customizationId,setCustomizationName:m,setSelectedCityId:g,setExistingCustomization:p,handleBack:d,handleSave:s,isFormValid:a.trim()&&i}}function me(){const[t,r]=j.useState([]),[u,l]=j.useState(null),[a,m]=j.useState(""),[i,g]=j.useState("0"),[C,o]=j.useState("0"),[f,p]=j.useState([]),N=()=>{l(null),c()},x=h=>{const n=t.find(y=>y.id===h);n&&(l(h),m(n.name),g(n.minRequired.toString()),o(n.maxAllowed.toString()),p(n.items))},d=h=>{r(n=>n.filter(y=>y.id!==h)),v.success("Đã xóa nhóm thành công!")},s=h=>{if(!a.trim())return v.error("Vui lòng nhập tên nhóm"),!1;const n=parseInt(i),y=parseInt(C);if(isNaN(n)||n<0)return v.error("Yêu cầu phải là số hợp lệ"),!1;if(isNaN(y)||y<0)return v.error("Tối đa phải là số hợp lệ"),!1;if(f.length===0)return v.error("Vui lòng thêm ít nhất một món"),!1;const I={id:u||Date.now().toString(),name:a.trim(),minRequired:n,maxAllowed:y,items:f.map(M=>{const S=h.find(w=>w.id===M.id);return{...M,code:(S==null?void 0:S.item_id)||M.code||M.id,size:"M",active:(S==null?void 0:S.active)??M.active??1}})};return u?(r(M=>M.map(S=>S.id===u?I:S)),v.success("Đã cập nhật nhóm thành công!")):(r(M=>[...M,I]),v.success("Đã tạo nhóm thành công!")),c(),!0},c=()=>{m(""),g("0"),o("0"),p([]),l(null)};return{customizationGroups:t,editingGroupId:u,groupName:a,minRequired:i,maxAllowed:C,menuItems:f,setCustomizationGroups:r,setGroupName:m,setMinRequired:g,setMaxAllowed:o,setMenuItems:p,handleCreateGroup:N,handleEditGroup:x,handleDeleteGroup:d,handleSaveGroup:s,resetGroupForm:c,isEditing:!!u}}function he(t={}){const[r,u]=j.useState(new Set),[l,a]=j.useState(""),[m,i]=j.useState(!0),[g,C]=j.useState(!0),o=c=>{const h=new Set(r);h.has(c)?h.delete(c):h.add(c),u(h)},f=(c,h)=>{var w;const n=c.filter(b=>r.has(b.id)),y=new Set(h.map(b=>b.id)),I=n.filter(b=>!y.has(b.id)),M=I.map(b=>({id:b.id,name:b.item_name,price:b.ots_price,active:b.active})),S=[...h,...M];return(w=t.onConfirm)==null||w.call(t,S),p(),I.length>0?v.success(`Đã thêm ${I.length} món`):v.info("Các món đã được thêm trước đó"),S},p=()=>{u(new Set),a(""),i(!0),C(!0)},N=c=>{const h=new Set(c.map(n=>n.id));u(h)},x=c=>c.filter(h=>h.item_name.toLowerCase().includes(l.toLowerCase()));return{selectedMenuItems:r,menuItemSearchTerm:l,selectedMenuSectionOpen:m,remainingMenuSectionOpen:g,setMenuItemSearchTerm:a,setSelectedMenuSectionOpen:i,setRemainingMenuSectionOpen:C,handleMenuItemToggle:o,handleConfirmMenuItems:f,resetSelection:p,syncWithCurrentMenuItems:N,getFilteredMenuItems:x,getSelectedMenuItemsList:c=>x(c).filter(n=>r.has(n.id)),getRemainingMenuItemsList:c=>x(c).filter(n=>!r.has(n.id)),hasSelectedItems:r.size>0}}function ue(){const[t,r]=j.useState(new Set),[u,l]=j.useState(""),[a,m]=j.useState(!0),[i,g]=j.useState(!0),C=d=>{const s=new Set(t);s.has(d)?s.delete(d):s.add(d),r(s)},o=()=>(v.success(`Đã chọn ${t.size} món`),!0),f=()=>{l(""),m(!0),g(!0)},p=d=>d.filter(s=>s.item_name.toLowerCase().includes(u.toLowerCase()));return{selectedDishes:t,dishSearchTerm:u,selectedSectionOpen:a,remainingSectionOpen:i,setSelectedDishes:r,setDishSearchTerm:l,setSelectedSectionOpen:m,setRemainingSectionOpen:g,handleDishToggle:C,handleConfirmDishSelection:o,resetDishSelection:f,getFilteredDishes:p,getSelectedDishItems:d=>p(d).filter(c=>t.has(c.id)),getRemainingDishItems:d=>p(d).filter(c=>!t.has(c.id)),hasSelectedDishes:t.size>0,selectedDishesCount:t.size}}function xe(){const[t,r]=j.useState(!1),[u,l]=j.useState(!1),[a,m]=j.useState(!1);return{createGroupModalOpen:t,addItemModalOpen:u,dishModalOpen:a,setCreateGroupModalOpen:r,setAddItemModalOpen:l,setDishModalOpen:m,handleCloseModal:()=>{r(!1)},handleCloseAddItemModal:()=>{l(!1)},handleCloseDishModal:()=>{m(!1)},handleAddMenuItem:p=>p?(l(!0),!0):(v.error("Vui lòng chọn thành phố trước"),!1),handleOpenDishModal:p=>p?!1:(m(!0),!0)}}const P=j.createContext(void 0);function pe({children:t,customizationId:r}){const u=de({isEdit:!!r,customizationId:r||""}),l=me(),a=he({onConfirm:s=>l.setMenuItems(s)}),m=ue(),i=xe(),g=()=>{l.handleCreateGroup(),i.setCreateGroupModalOpen(!0)},C=s=>{l.handleEditGroup(s),i.setCreateGroupModalOpen(!0)},o=()=>{i.handleCloseModal(),l.resetGroupForm(),a.resetSelection()},d={customizationForm:u,groupManagement:l,menuItemSelection:a,dishSelection:m,modalState:i,handlers:{handleCreateGroup:g,handleEditGroup:C,handleCloseModal:o,handleCloseAddItemModal:()=>{i.handleCloseAddItemModal(),a.resetSelection()},handleConfirmMenuItems:s=>{a.handleConfirmMenuItems(s,l.menuItems),i.handleCloseAddItemModal()},handleAddMenuItem:s=>{a.syncWithCurrentMenuItems(l.menuItems),i.handleAddMenuItem(s)},handleSaveGroup:s=>{const c=l.handleSaveGroup(s);return c&&o(),c}}};return e.jsx(P.Provider,{value:d,children:t})}function O(){const t=j.useContext(P);if(t===void 0)throw new Error("useFormContext must be used within a FormProvider");return t}function ge({open:t,onOpenChange:r,onCancel:u,onConfirm:l,selectedMenuItemsList:a,remainingMenuItemsList:m}){const{menuItemSelection:i}=O(),{menuItemSearchTerm:g,setMenuItemSearchTerm:C,selectedMenuSectionOpen:o,setSelectedMenuSectionOpen:f,remainingMenuSectionOpen:p,setRemainingMenuSectionOpen:N,selectedMenuItems:x,handleMenuItemToggle:d}=i;return e.jsx($,{title:"Chọn món để thêm vào nhóm",open:t,onOpenChange:r,onCancel:u,onConfirm:l,confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(D,{placeholder:"Tìm kiếm món",value:g,onChange:s=>C(s.target.value),className:"w-full"}),e.jsxs(A,{open:o,onOpenChange:f,children:[e.jsxs(k,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",a.length,")"]}),o?e.jsx(R,{className:"h-4 w-4"}):e.jsx(F,{className:"h-4 w-4"})]}),e.jsx(L,{className:"mt-2",children:e.jsxs("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:[a.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"}),a.length>0&&a.map(s=>{const c=s.active===1;return e.jsxs("label",{className:`flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50 ${c?"":"bg-gray-100 opacity-50"}`,children:[e.jsx(_,{checked:x.has(s.id),onCheckedChange:()=>d(s.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm font-medium ${c?"":"text-gray-400"}`,children:s.item_name}),e.jsxs("p",{className:`text-xs ${c?"text-gray-500":"text-gray-400"}`,children:[s.ots_price.toLocaleString("vi-VN")," đ"]})]})]},s.id)})]})})]}),e.jsxs(A,{open:p,onOpenChange:N,children:[e.jsxs(k,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",m.filter(s=>s.active!==0).length,")"]}),p?e.jsx(R,{className:"h-4 w-4"}):e.jsx(F,{className:"h-4 w-4"})]}),e.jsx(L,{className:"mt-2",children:e.jsxs("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:[m.filter(s=>s.active!==0).length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Không có món nào"}),m.filter(s=>s.active!==0).length>0&&m.filter(s=>s.active!==0).map(s=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(_,{checked:x.has(s.id),onCheckedChange:()=>d(s.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:s.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[s.ots_price.toLocaleString("vi-VN")," đ"]})]})]},s.id))]})})]})]})})}function fe({items:t}){const{customizationForm:r,groupManagement:u,modalState:l,handlers:a,menuItemSelection:m}=O(),i=m.getSelectedMenuItemsList(t),g=m.getRemainingMenuItemsList(t),{isEditing:C,groupName:o,setGroupName:f,minRequired:p,setMinRequired:N,maxAllowed:x,setMaxAllowed:d,menuItems:s}=u,{customizationName:c}=r,h=n=>u.setMenuItems(y=>y.filter(I=>I.id!==n));return e.jsxs(e.Fragment,{children:[e.jsx($,{title:`${C?"Sửa":"Tạo"} nhóm cho customization ${c||""}`,open:l.createGroupModalOpen,onOpenChange:l.setCreateGroupModalOpen,onCancel:a.handleCloseModal,onConfirm:()=>a.handleSaveGroup(t),confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(D,{placeholder:"Tên nhóm",value:o,onChange:n=>f(n.target.value)})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(T,{htmlFor:"min-required",className:"min-w-[80px] text-sm font-medium",children:"Yêu cầu"}),e.jsx(D,{id:"min-required",type:"number",value:p,onChange:n=>N(n.target.value),min:"0",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(T,{htmlFor:"max-allowed",className:"min-w-[80px] text-sm font-medium",children:"Tối đa"}),e.jsx(D,{id:"max-allowed",type:"number",value:x,onChange:n=>d(n.target.value),min:"0",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Danh sách món"}),e.jsx("div",{className:"scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 max-h-[50vh] overflow-y-auto rounded-md border",style:{scrollBehavior:"smooth"},children:e.jsxs(Y,{children:[e.jsx(J,{children:e.jsxs(E,{children:[e.jsx(V,{children:"Tên"}),e.jsx(V,{children:"Giá"}),e.jsx(V,{className:"w-10 text-right"})]})}),e.jsxs(Q,{children:[s.map(n=>e.jsxs(E,{className:n.active===0?"opacity-50 blur-[0.5px] filter":"",children:[e.jsx(G,{children:n.name}),e.jsxs(G,{children:[n.price.toLocaleString("vi-VN")," đ"]}),e.jsx(G,{className:"text-right",children:e.jsx(z,{type:"button",variant:"ghost",size:"icon","aria-label":"Xóa món khỏi nhóm",title:"Xóa",onClick:()=>h(n.id),className:"text-red-600 hover:text-red-700",children:e.jsx(re,{size:16})})})]},n.id)),e.jsx(E,{className:"cursor-pointer hover:bg-gray-50",onClick:()=>a.handleAddMenuItem(r.selectedCityId),children:e.jsx(G,{colSpan:3,className:"py-4 text-center",children:e.jsx("span",{className:"font-medium text-blue-600",children:"Thêm món"})})})]})]})})]})]})}),e.jsx(ge,{open:l.addItemModalOpen,onOpenChange:l.setAddItemModalOpen,onCancel:a.handleCloseAddItemModal,onConfirm:()=>a.handleConfirmMenuItems(t),selectedMenuItemsList:i,remainingMenuItemsList:g})]})}function je({items:t}){const{dishSelection:r,modalState:u}=O(),l=r.getSelectedDishItems(t),a=r.getRemainingDishItems(t),{dishSearchTerm:m,setDishSearchTerm:i,selectedSectionOpen:g,setSelectedSectionOpen:C,remainingSectionOpen:o,setRemainingSectionOpen:f,selectedDishes:p,handleDishToggle:N}=r;return e.jsx($,{title:"",open:u.dishModalOpen,onOpenChange:u.setDishModalOpen,onCancel:u.handleCloseDishModal,onConfirm:()=>{r.handleConfirmDishSelection(),u.handleCloseDishModal()},confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(D,{placeholder:"Tìm kiếm",value:m,onChange:x=>i(x.target.value),className:"w-full"}),e.jsxs(A,{open:g,onOpenChange:C,children:[e.jsxs(k,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",l.length,")"]}),g?e.jsx(R,{className:"h-4 w-4"}):e.jsx(F,{className:"h-4 w-4"})]}),e.jsx(L,{className:"mt-2",children:e.jsxs("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:[l.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"}),l.length>0&&l.sort((x,d)=>(d.active||0)-(x.active||0)).map(x=>{const d=x.active===1;return e.jsxs("label",{className:`flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50 ${d?"":"bg-gray-100 opacity-50"}`,children:[e.jsx(_,{checked:p.has(x.id),onCheckedChange:()=>N(x.id),disabled:!1}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm font-medium ${d?"":"text-gray-400"}`,children:x.item_name}),e.jsxs("p",{className:`text-xs ${d?"text-gray-500":"text-gray-400"}`,children:[x.ots_price.toLocaleString("vi-VN")," đ"]})]})]},x.id)})]})})]}),e.jsxs(A,{open:o,onOpenChange:f,children:[e.jsxs(k,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",a.length,")"]}),o?e.jsx(R,{className:"h-4 w-4"}):e.jsx(F,{className:"h-4 w-4"})]}),e.jsx(L,{className:"mt-2",children:e.jsxs("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:[a.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Không có món nào"}),a.length>0&&a.sort((x,d)=>(d.active||0)-(x.active||0)).map(x=>{const d=x.active===1;return e.jsxs("label",{className:`flex items-center space-x-3 rounded p-2 ${d?"cursor-pointer hover:bg-gray-50":"cursor-not-allowed bg-gray-100 opacity-50"}`,children:[e.jsx(_,{checked:p.has(x.id),onCheckedChange:()=>d&&N(x.id),disabled:!d}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm font-medium ${d?"":"text-gray-400"}`,children:x.item_name}),e.jsxs("p",{className:`text-xs ${d?"text-gray-500":"text-gray-400"}`,children:[x.ots_price.toLocaleString("vi-VN")," đ"]})]})]},x.id)})]})})]})]})})}function Ce({onBack:t,onSave:r,isSubmitting:u,isFormValid:l,title:a="Tạo customization",saveButtonText:m="Lưu",submittingText:i="Đang tạo..."}){return e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(z,{variant:"ghost",size:"sm",onClick:t,className:"flex items-center",children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx(z,{type:"button",disabled:u||!l,className:"min-w-[100px]",onClick:r,children:u?i:m})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:a})})]})}function Se({customizationId:t,isLoadingItems:r,handleCreateGroup:u,handleEditGroup:l}){const{customizationForm:a,groupManagement:m,dishSelection:i,modalState:g}=O(),{cities:C}=le();return e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(T,{htmlFor:"customization-name",className:"min-w-[200px] text-sm font-medium",children:["Tên customization ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(D,{id:"customization-name",value:a.customizationName,onChange:o=>a.setCustomizationName(o.target.value),placeholder:"Nhập tên customization",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(T,{htmlFor:"city-select",className:"min-w-[200px] text-sm font-medium",children:["Thành phố ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs(se,{value:a.selectedCityId,onValueChange:a.setSelectedCityId,disabled:!!t,children:[e.jsx(ne,{className:"flex-1",children:e.jsx(ae,{placeholder:"Chọn thành phố"})}),e.jsx(ie,{children:C.map(o=>e.jsx(oe,{value:o.id,children:o.city_name},o.id))})]})]}),a.selectedCityId&&e.jsxs("div",{className:"space-y-4 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Áp dụng customization cho món"}),e.jsx("div",{className:`cursor-pointer rounded-md border p-4 hover:bg-gray-50 ${r?"cursor-not-allowed opacity-50":""}`,onClick:()=>g.handleOpenDishModal(r),children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:"Món ăn"}),e.jsx("span",{className:"text-sm text-gray-500",children:r?"Đang tải...":`${i.selectedDishesCount} món áp dụng`})]})})]}),e.jsx("div",{className:"flex justify-center pt-4",children:e.jsx(z,{onClick:u,children:"Tạo nhóm"})}),m.customizationGroups.length>0&&e.jsxs("div",{className:"space-y-6 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Danh sách nhóm đã tạo"}),m.customizationGroups.map(o=>e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:o.name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(Chọn từ ",o.minRequired," đến ",o.maxAllowed," món)"]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(z,{variant:"outline",size:"sm",onClick:()=>l(o.id),children:"Sửa"}),e.jsx(z,{variant:"outline",size:"sm",onClick:()=>m.handleDeleteGroup(o.id),className:"text-red-600 hover:text-red-700",children:"Xóa"})]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:o.items.map(f=>{const p=f.active===1;return e.jsxs("div",{className:`rounded-md border p-3 text-center ${p?"bg-gray-50":"cursor-not-allowed bg-gray-100 opacity-50"}`,children:[e.jsx("p",{className:`text-sm font-medium ${p?"":"text-gray-400"}`,children:f.name}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["(",f.code,")"]}),e.jsxs("p",{className:`mt-1 text-sm font-medium ${p?"text-green-600":"text-gray-400"}`,children:[f.price.toLocaleString("vi-VN",{minimumFractionDigits:0,maximumFractionDigits:0})," ","₫"]})]},f.id)})})]},o.id))]})]})})})}function ve(){const{customizationForm:t,groupManagement:r,dishSelection:u,handlers:l}=O(),a=j.useRef(!1),m=t.customizationId,{data:i,isLoading:g,error:C}=K(m||"",!!m),{data:o=[],isLoading:f,error:p}=W({params:{city_uid:t.selectedCityId,skip_limit:!0},enabled:!!t.selectedCityId});j.useEffect(()=>{var d;if(i&&!g&&(t.setExistingCustomization(i),t.setCustomizationName(i.name),t.setSelectedCityId(i.cityUid||""),(d=i.data)!=null&&d.LstItem_Options&&o.length>0)){const s=i.data.LstItem_Options.map(c=>({id:c.id,name:c.Name,minRequired:c.Min_Permitted,maxAllowed:c.Max_Permitted,items:c.LstItem_Id.map(h=>{const n=o.find(y=>y.item_id===h);return{id:(n==null?void 0:n.id)||h,name:(n==null?void 0:n.item_name)||h,price:(n==null?void 0:n.ots_price)||0,code:h,active:(n==null?void 0:n.active)??1}})}));r.setCustomizationGroups(s)}},[i,g,o]),j.useEffect(()=>{if(o.length>0){if(r.customizationGroups.length>0){const d=r.customizationGroups.map(s=>({...s,items:s.items.map(c=>{const h=o.find(n=>n.item_id===c.code);return h?{id:h.id,name:h.item_name,price:h.ots_price,code:h.item_id,active:h.active}:c})}));r.setCustomizationGroups(d)}if(i!=null&&i.listItem&&i.listItem.length>0&&!a.current){const d=new Set;i.listItem.forEach(s=>{const c=o.find(h=>h.item_id===s);c&&d.add(c.id)}),u.setSelectedDishes(d),a.current=!0}}},[o,i]);const N=C||p&&t.selectedCityId;if(m&&!i&&!g&&!C)return e.jsx(U,{});if(g)return e.jsx(X,{});if(N)return e.jsx(H,{description:"Không thể tải dữ liệu"});const x=async()=>{await t.handleSave(r.customizationGroups,u.selectedDishes,o)};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ce,{onBack:t.handleBack,onSave:x,isSubmitting:t.isSubmitting,isFormValid:!!t.isFormValid,title:m?"Sửa customization":"Tạo customization",saveButtonText:m?"Cập nhật":"Lưu",submittingText:m?"Đang cập nhật...":"Đang tạo..."}),e.jsx(Se,{customizationId:m,isLoadingItems:f,handleCreateGroup:l.handleCreateGroup,handleEditGroup:l.handleEditGroup}),e.jsx(fe,{items:o}),e.jsx(je,{items:o})]})}function Xe({customizationId:t}){return e.jsx(pe,{customizationId:t,children:e.jsx(ve,{})})}export{Xe as C};
